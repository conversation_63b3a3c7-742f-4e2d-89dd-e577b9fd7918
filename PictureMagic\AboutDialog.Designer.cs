namespace PictureMagic
{
    partial class AboutDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;
        private Label labelAppName;
        private Label lblVersion;
        private Label labelAuthor;
        private Button buttonClose;
        private LinkLabel lblEmail;
        private Label lblMachineCode;
        private Button btnRegister;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            labelAppName = new Label();
            lblVersion = new Label();
            labelAuthor = new Label();
            buttonClose = new Button();
            lblEmail = new LinkLabel();
            lblMachineCode = new Label();
            btnRegister = new Button();
            txtMachineCode = new TextBox();
            lblDescription = new Label();
            btnCopyMachineCode = new Button();
            btnUnReg = new Button();
            SuspendLayout();
            // 
            // labelAppName
            // 
            labelAppName.AutoSize = true;
            labelAppName.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point, 134);
            labelAppName.Location = new Point(46, 50);
            labelAppName.Margin = new Padding(4, 0, 4, 0);
            labelAppName.Name = "labelAppName";
            labelAppName.Size = new Size(338, 36);
            labelAppName.TabIndex = 0;
            labelAppName.Text = "PictureMagic 图像编辑器";
            // 
            // lblVersion
            // 
            lblVersion.AutoSize = true;
            lblVersion.Location = new Point(52, 117);
            lblVersion.Margin = new Padding(4, 0, 4, 0);
            lblVersion.Name = "lblVersion";
            lblVersion.Size = new Size(69, 20);
            lblVersion.TabIndex = 1;
            lblVersion.Text = "版本: 1.0";
            // 
            // labelAuthor
            // 
            labelAuthor.AutoSize = true;
            labelAuthor.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold, GraphicsUnit.Point, 134);
            labelAuthor.Location = new Point(52, 233);
            labelAuthor.Margin = new Padding(4, 0, 4, 0);
            labelAuthor.Name = "labelAuthor";
            labelAuthor.Size = new Size(321, 19);
            labelAuthor.TabIndex = 3;
            labelAuthor.Text = "New Army Software Development Studio";
            // 
            // buttonClose
            // 
            buttonClose.Location = new Point(310, 414);
            buttonClose.Margin = new Padding(4, 5, 4, 5);
            buttonClose.Name = "buttonClose";
            buttonClose.Size = new Size(112, 38);
            buttonClose.TabIndex = 4;
            buttonClose.Text = "确定";
            buttonClose.UseVisualStyleBackColor = true;
            buttonClose.Click += buttonClose_Click;
            // 
            // lblEmail
            // 
            lblEmail.AutoSize = true;
            lblEmail.Location = new Point(379, 233);
            lblEmail.Name = "lblEmail";
            lblEmail.Size = new Size(195, 20);
            lblEmail.TabIndex = 5;
            lblEmail.TabStop = true;
            lblEmail.Text = "<EMAIL>";
            lblEmail.LinkClicked += lblEmail_LinkClicked;
            // 
            // lblMachineCode
            // 
            lblMachineCode.AutoSize = true;
            lblMachineCode.Location = new Point(50, 281);
            lblMachineCode.Name = "lblMachineCode";
            lblMachineCode.Size = new Size(62, 20);
            lblMachineCode.TabIndex = 6;
            lblMachineCode.Text = "序列号: ";
            // 
            // btnRegister
            // 
            btnRegister.Location = new Point(580, 278);
            btnRegister.Name = "btnRegister";
            btnRegister.Size = new Size(120, 30);
            btnRegister.TabIndex = 7;
            btnRegister.Text = "注册";
            btnRegister.UseVisualStyleBackColor = true;
            btnRegister.Click += btnRegister_Click;
            // 
            // txtMachineCode
            // 
            txtMachineCode.BorderStyle = BorderStyle.FixedSingle;
            txtMachineCode.Location = new Point(114, 279);
            txtMachineCode.Name = "txtMachineCode";
            txtMachineCode.ReadOnly = true;
            txtMachineCode.Size = new Size(460, 27);
            txtMachineCode.TabIndex = 9;
            // 
            // lblDescription
            // 
            lblDescription.AutoSize = true;
            lblDescription.Location = new Point(56, 171);
            lblDescription.Name = "lblDescription";
            lblDescription.Size = new Size(534, 20);
            lblDescription.TabIndex = 10;
            lblDescription.Text = "一个简单易用的图像编辑工具，提供裁剪、调整大小、滤镜、图片拼接等功能。";
            // 
            // btnCopyMachineCode
            // 
            btnCopyMachineCode.Location = new Point(114, 321);
            btnCopyMachineCode.Name = "btnCopyMachineCode";
            btnCopyMachineCode.Size = new Size(125, 29);
            btnCopyMachineCode.TabIndex = 11;
            btnCopyMachineCode.Text = "复制序列号";
            btnCopyMachineCode.UseVisualStyleBackColor = true;
            btnCopyMachineCode.Click += btnCopyMachineCode_Click;
            // 
            // btnUnReg
            // 
            btnUnReg.Location = new Point(282, 321);
            btnUnReg.Name = "btnUnReg";
            btnUnReg.Size = new Size(116, 29);
            btnUnReg.TabIndex = 12;
            btnUnReg.Text = "取消注册";
            btnUnReg.UseVisualStyleBackColor = true;
            btnUnReg.Click += btnUnReg_Click;
            // 
            // AboutDialog
            // 
            AutoScaleDimensions = new SizeF(9F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(722, 484);
            Controls.Add(btnUnReg);
            Controls.Add(btnCopyMachineCode);
            Controls.Add(lblDescription);
            Controls.Add(txtMachineCode);
            Controls.Add(btnRegister);
            Controls.Add(lblMachineCode);
            Controls.Add(lblEmail);
            Controls.Add(buttonClose);
            Controls.Add(labelAuthor);
            Controls.Add(lblVersion);
            Controls.Add(labelAppName);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Margin = new Padding(4, 5, 4, 5);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "AboutDialog";
            StartPosition = FormStartPosition.CenterParent;
            Text = "关于 PictureMagic";
            Load += AboutDialog_Load;
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion
        private TextBox txtMachineCode;
        private Label lblDescription;
        private Button btnCopyMachineCode;
        private Button btnUnReg;
    }
}