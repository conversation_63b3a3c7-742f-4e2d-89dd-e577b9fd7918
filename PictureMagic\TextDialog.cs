using System.Drawing.Drawing2D;
using System.Drawing.Text;

namespace PictureMagic
{
    public partial class TextDialog : Form
    {
        private Image originalImage;
        private Image previewImage;
        private string textContent = "输入文本";
        private Font textFont = new Font("微软雅黑", 48, FontStyle.Regular);
        private Color textColor = Color.Red;
        private Color outlineColor = Color.White;
        private bool useOutline = false;
        private int outlineWidth = 2;
        private Point textPosition;
        private bool isDragging = false;
        private Point dragStartPoint;
        private StringAlignment horizontalAlignment = StringAlignment.Center;
        private StringAlignment verticalAlignment = StringAlignment.Near;
        private float textOpacity = 1.0f;
        private float textRotation = 0.0f;

        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public Image ResultImage { get; private set; } = null!; // 使用null抑制操作符

        public TextDialog(Image image)
        {
            InitializeComponent();
            originalImage = image;
            previewImage = new Bitmap(originalImage);
            pictureBoxPreview.Image = previewImage;
            pictureBoxPreview.SizeMode = PictureBoxSizeMode.Zoom;

            // 初始化文本位置为图片顶部中间
            textPosition = new Point(originalImage.Width / 2, 50);

            // 初始化字体下拉框
            InitializeFontComboBox();

            // 初始化字体大小下拉框
            InitializeFontSizeComboBox();

            // 初始化对齐方式下拉框
            InitializeAlignmentComboBox();

            // 设置文本内容
            textBoxContent.Text = textContent;

            // 设置不透明度滑块
            trackBarOpacity.Minimum = 0;
            trackBarOpacity.Maximum = 100;
            trackBarOpacity.Value = (int)(textOpacity * 100);
            labelOpacityValue.Text = trackBarOpacity.Value.ToString();

            // 设置旋转角度滑块
            trackBarRotation.Minimum = 0;
            trackBarRotation.Maximum = 360;
            trackBarRotation.Value = (int)textRotation;
            labelRotationValue.Text = trackBarRotation.Value.ToString() + "°";

            // 设置轮廓宽度滑块
            trackBarOutlineWidth.Minimum = 1;
            trackBarOutlineWidth.Maximum = 10;
            trackBarOutlineWidth.Value = outlineWidth;
            labelOutlineWidthValue.Text = outlineWidth.ToString() + "px";

            // 设置轮廓复选框
            checkBoxOutline.Checked = useOutline;
            trackBarOutlineWidth.Enabled = useOutline;
            buttonOutlineColor.Enabled = useOutline;

            // 设置文本颜色按钮背景色
            buttonTextColor.BackColor = textColor;

            // 设置水平对齐方式为居中
            comboBoxHAlign.SelectedIndex = 1; // 居中

            // 设置字体大小
            comboBoxFontSize.Text = textFont.Size.ToString();

            // 更新预览
            UpdatePreview();
        }

        private void InitializeFontComboBox()
        {
            // 获取系统字体
            InstalledFontCollection fonts = new InstalledFontCollection();
            foreach (FontFamily family in fonts.Families)
            {
                comboBoxFont.Items.Add(family.Name);
            }

            // 设置默认字体
            comboBoxFont.Text = textFont.FontFamily.Name;
        }

        private void InitializeFontSizeComboBox()
        {
            int[] commonSizes = { 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72 };
            foreach (int size in commonSizes)
            {
                comboBoxFontSize.Items.Add(size.ToString());
            }

            // 设置默认大小
            comboBoxFontSize.Text = textFont.Size.ToString();
        }

        private void InitializeAlignmentComboBox()
        {
            // 水平对齐方式
            comboBoxHAlign.Items.Add("左对齐");
            comboBoxHAlign.Items.Add("居中");
            comboBoxHAlign.Items.Add("右对齐");
            comboBoxHAlign.Items.Add("两端对齐");
            comboBoxHAlign.Items.Add("分散对齐");
            comboBoxHAlign.SelectedIndex = 0;

            // 垂直对齐方式
            comboBoxVAlign.Items.Add("顶部对齐");
            comboBoxVAlign.Items.Add("居中");
            comboBoxVAlign.Items.Add("底部对齐");
            comboBoxVAlign.SelectedIndex = 0;
        }

        // 文本工具不需要使用进度窗体

        private void UpdatePreview()
        {
            try
            {
                // 释放之前的预览图像
                if (previewImage != null && previewImage != originalImage)
                {
                    pictureBoxPreview.Image = null;
                    previewImage.Dispose();
                }
            
                // 创建新的预览图像
                previewImage = new Bitmap(originalImage);
                
                using (Graphics g = Graphics.FromImage(previewImage))
                {
                    g.SmoothingMode = SmoothingMode.AntiAlias;
                    g.TextRenderingHint = TextRenderingHint.AntiAlias;
            
                    // 创建文本格式
                    StringFormat format = new StringFormat();
                    format.Alignment = horizontalAlignment;
                    format.LineAlignment = verticalAlignment;
            
                    // 处理特殊的对齐方式
                    if (comboBoxHAlign.SelectedIndex == 3) // 两端对齐
                    {
                        // 注意：System.Drawing.StringFormat不直接支持两端对齐
                        // 这里使用Far作为基础（与左对齐保持一致），并添加一些标志来尽可能接近两端对齐效果
                        format.Alignment = StringAlignment.Far; // 修改为Far以匹配左对齐的修改
                        format.FormatFlags |= StringFormatFlags.LineLimit;
                        format.Trimming = StringTrimming.Word;
                        // 要实现真正的两端对齐，需要自定义文本渲染逻辑，计算每行的空格分布
                    }
                    else if (comboBoxHAlign.SelectedIndex == 4) // 分散对齐
                    {
                        // 注意：System.Drawing.StringFormat不直接支持分散对齐
                        // 这里使用Far作为基础（与左对齐保持一致），并添加一些标志来尽可能接近分散对齐效果
                        format.Alignment = StringAlignment.Far; // 修改为Far以匹配左对齐的修改
                        format.FormatFlags |= StringFormatFlags.LineLimit;
                        format.Trimming = StringTrimming.Word;
                        // 要实现真正的分散对齐，需要自定义文本渲染逻辑，计算字符间距
                    }
            
                    // 创建文本路径
                    GraphicsPath path = new GraphicsPath();
                    path.AddString(
                        textContent,
                        textFont.FontFamily,
                        (int)textFont.Style,
                        g.DpiY * textFont.Size / 72,
                        textPosition,
                        format);
            
                    // 应用旋转
                    if (textRotation != 0)
                    {
                        Matrix rotationMatrix = new Matrix();
                        rotationMatrix.RotateAt(textRotation, textPosition);
                        path.Transform(rotationMatrix);
                    }
            
                    // 绘制文本轮廓
                    if (useOutline)
                    {
                        using (Pen outlinePen = new Pen(outlineColor, outlineWidth))
                        {
                            outlinePen.LineJoin = LineJoin.Round;
                            g.DrawPath(outlinePen, path);
                        }
                    }
            
                    // 绘制文本填充
                    using (SolidBrush textBrush = new SolidBrush(Color.FromArgb((int)(textOpacity * 255), textColor)))
                    {
                        g.FillPath(textBrush, path);
                    }
                }

                pictureBoxPreview.Image = previewImage;
            }
            catch (Exception ex)
            {
                // ... existing code ...
            }
            finally
            {
                // 文本工具不需要使用进度窗体
            }
        }

        private void textBoxContent_TextChanged(object sender, EventArgs e)
        {
            textContent = textBoxContent.Text;
            if (string.IsNullOrEmpty(textContent))
            {
                textContent = " "; // 防止空字符串
            }
            UpdatePreview();
        }

        private void comboBoxFont_SelectedIndexChanged(object sender, EventArgs e)
        {
            string fontName = comboBoxFont.Text;
            textFont = new Font(fontName, textFont.Size, textFont.Style);
            UpdatePreview();
        }

        private void comboBoxFontSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (float.TryParse(comboBoxFontSize.Text, out float fontSize))
            {
                textFont = new Font(textFont.FontFamily, fontSize, textFont.Style);
                UpdatePreview();
            }
        }

        private void comboBoxFontSize_TextChanged(object sender, EventArgs e)
        {
            if (float.TryParse(comboBoxFontSize.Text, out float fontSize) && fontSize > 0)
            {
                textFont = new Font(textFont.FontFamily, fontSize, textFont.Style);
                UpdatePreview();
            }
        }

        private void buttonBold_Click(object sender, EventArgs e)
        {
            FontStyle newStyle = textFont.Style ^ FontStyle.Bold;
            textFont = new Font(textFont.FontFamily, textFont.Size, newStyle);
            buttonBold.BackColor = (newStyle & FontStyle.Bold) != 0 ? SystemColors.ControlDark : SystemColors.Control;
            UpdatePreview();
        }

        private void buttonItalic_Click(object sender, EventArgs e)
        {
            FontStyle newStyle = textFont.Style ^ FontStyle.Italic;
            textFont = new Font(textFont.FontFamily, textFont.Size, newStyle);
            buttonItalic.BackColor = (newStyle & FontStyle.Italic) != 0 ? SystemColors.ControlDark : SystemColors.Control;
            UpdatePreview();
        }

        private void buttonUnderline_Click(object sender, EventArgs e)
        {
            FontStyle newStyle = textFont.Style ^ FontStyle.Underline;
            textFont = new Font(textFont.FontFamily, textFont.Size, newStyle);
            buttonUnderline.BackColor = (newStyle & FontStyle.Underline) != 0 ? SystemColors.ControlDark : SystemColors.Control;
            UpdatePreview();
        }

        private void buttonTextColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDialog = new ColorDialog();
            colorDialog.Color = textColor;
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                textColor = colorDialog.Color;
                buttonTextColor.BackColor = textColor;
                UpdatePreview();
            }
        }

        private void buttonOutlineColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDialog = new ColorDialog();
            colorDialog.Color = outlineColor;
            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                outlineColor = colorDialog.Color;
                buttonOutlineColor.BackColor = outlineColor;
                UpdatePreview();
            }
        }

        private void checkBoxOutline_CheckedChanged(object sender, EventArgs e)
        {
            useOutline = checkBoxOutline.Checked;
            trackBarOutlineWidth.Enabled = useOutline;
            buttonOutlineColor.Enabled = useOutline;
            UpdatePreview();
        }

        private void trackBarOutlineWidth_Scroll(object sender, EventArgs e)
        {
            outlineWidth = trackBarOutlineWidth.Value;
            labelOutlineWidthValue.Text = outlineWidth.ToString() + "px";
            UpdatePreview();
        }

        private void trackBarOpacity_Scroll(object sender, EventArgs e)
        {
            textOpacity = trackBarOpacity.Value / 100.0f;
            labelOpacityValue.Text = trackBarOpacity.Value.ToString();
            UpdatePreview();
        }

        private void trackBarRotation_Scroll(object sender, EventArgs e)
        {
            textRotation = trackBarRotation.Value;
            labelRotationValue.Text = trackBarRotation.Value.ToString() + "°";
            UpdatePreview();
        }

        private void comboBoxHAlign_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (comboBoxHAlign.SelectedIndex)
            {
                case 0: // 左对齐
                    horizontalAlignment = StringAlignment.Far; // 修改为Far以解决左右对齐反了的问题
                    break;
                case 1: // 居中
                    horizontalAlignment = StringAlignment.Center;
                    break;
                case 2: // 右对齐
                    horizontalAlignment = StringAlignment.Near; // 修改为Near以解决左右对齐反了的问题
                    break;
                case 3: // 两端对齐
                    horizontalAlignment = StringAlignment.Far; // 修改为Far以匹配左对齐的修改
                    // 注意：StringFormat没有直接支持两端对齐的选项，这里使用Far作为基础
                    // 在UpdatePreview方法中需要额外处理
                    break;
                case 4: // 分散对齐
                    horizontalAlignment = StringAlignment.Far; // 修改为Far以匹配左对齐的修改
                    // 注意：StringFormat没有直接支持分散对齐的选项，这里使用Far作为基础
                    // 在UpdatePreview方法中需要额外处理
                    break;
            }
            UpdatePreview();
        }

        private void comboBoxVAlign_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (comboBoxVAlign.SelectedIndex)
            {
                case 0: // 顶部对齐
                    verticalAlignment = StringAlignment.Near;
                    break;
                case 1: // 居中
                    verticalAlignment = StringAlignment.Center;
                    break;
                case 2: // 底部对齐
                    verticalAlignment = StringAlignment.Far;
                    break;
            }
            UpdatePreview();
        }

        private void pictureBoxPreview_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isDragging = true;
                dragStartPoint = e.Location;

                // 计算图像在PictureBox中的实际位置和缩放比例
                Rectangle imageRect = GetImageRectangle();
                float scaleX = (float)originalImage.Width / imageRect.Width;
                float scaleY = (float)originalImage.Height / imageRect.Height;

                // 转换鼠标位置到图像坐标
                int imageX = (int)((e.X - imageRect.X) * scaleX);
                int imageY = (int)((e.Y - imageRect.Y) * scaleY);

                // 更新文本位置
                textPosition = new Point(imageX, imageY);
                UpdatePreview();
            }
        }

        private void pictureBoxPreview_MouseMove(object sender, MouseEventArgs e)
        {
            if (isDragging)
            {
                // 计算图像在PictureBox中的实际位置和缩放比例
                Rectangle imageRect = GetImageRectangle();
                float scaleX = (float)originalImage.Width / imageRect.Width;
                float scaleY = (float)originalImage.Height / imageRect.Height;

                // 转换鼠标位置到图像坐标
                int imageX = (int)((e.X - imageRect.X) * scaleX);
                int imageY = (int)((e.Y - imageRect.Y) * scaleY);

                // 更新文本位置
                textPosition = new Point(imageX, imageY);
                UpdatePreview();
            }
        }

        private void pictureBoxPreview_MouseUp(object sender, MouseEventArgs e)
        {
            isDragging = false;
        }

        private Rectangle GetImageRectangle()
        {
            // 计算图像在PictureBox中的实际位置和大小
            Rectangle result = new Rectangle(0, 0, pictureBoxPreview.Width, pictureBoxPreview.Height);
            if (originalImage != null)
            {
                float imageRatio = (float)originalImage.Width / originalImage.Height;
                float containerRatio = (float)pictureBoxPreview.Width / pictureBoxPreview.Height;

                if (imageRatio > containerRatio)
                {
                    // 图像比容器宽，上下有空白
                    int height = (int)(pictureBoxPreview.Width / imageRatio);
                    int y = (pictureBoxPreview.Height - height) / 2;
                    result = new Rectangle(0, y, pictureBoxPreview.Width, height);
                }
                else
                {
                    // 图像比容器高，左右有空白
                    int width = (int)(pictureBoxPreview.Height * imageRatio);
                    int x = (pictureBoxPreview.Width - width) / 2;
                    result = new Rectangle(x, 0, width, pictureBoxPreview.Height);
                }
            }
            return result;
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            ResultImage = previewImage;
            DialogResult = DialogResult.OK;
            Close();
        }

        public Image GetResultImage()
        {
            return ResultImage;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            // 如果取消，释放预览图像
            if (DialogResult == DialogResult.Cancel && previewImage != null && previewImage != originalImage)
            {
                previewImage.Dispose();
            }
        }

        // 文本工具不需要使用进度窗体
    }
}