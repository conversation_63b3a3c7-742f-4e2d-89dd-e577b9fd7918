using CommonLib;
using PictureMagic.Common;
using System.Diagnostics;

namespace PictureMagic
{
    public partial class AboutDialog : Form
    {
        public AboutDialog()
        {
            InitializeComponent();
        }

        private void buttonClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void AboutDialog_Load(object sender, EventArgs e)
        {
            lblVersion.Text = $"版本:{Tools.GetVersion()}";

            // 加载注册信息
            var settings = AppSettings.LoadSettings();
            string machineCode = HardwareInfo.GenerateMachineCode();
            txtMachineCode.Text = machineCode;
            if (!string.IsNullOrEmpty(settings.LicenseKey) &&
                LicenseGenerator.ValidateLicenseKey(machineCode, settings.LicenseKey, "PictureMagicSecretKey"))
            {
                btnRegister.Visible = false;
                btnUnReg.Visible = true;
            }
            else
            {
                btnRegister.Visible = true;
                btnRegister.Text = "点我注册";
                btnUnReg.Visible = false;
            }
        }




        private void lblEmail_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo($"mailto:{lblEmail.Text}") { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                MessageBox.Show("无法启动邮件客户端：" + ex.Message);
            }
        }

        private void btnRegister_Click(object sender, EventArgs e)
        {
            //// 创建一个简单的输入对话框
            //Form inputDialog = new Form()
            //{
            //    AutoScaleMode = AutoScaleMode.Dpi,
            //    Width = 400,
            //    Height = 180,
            //    FormBorderStyle = FormBorderStyle.FixedDialog,
            //    Text = "软件注册",
            //    StartPosition = FormStartPosition.CenterParent,
            //    MaximizeBox = false,
            //    MinimizeBox = false
            //};

            //Label label = new Label() { Left = 20, Top = 20, Text = "请输入注册码：", Width = 360 };
            //TextBox textBox = new TextBox() { Left = 20, Top = 50, Width = 350, BorderStyle = BorderStyle.FixedSingle };
            //Button confirmButton = new Button() { Text = "确定", Left = 150, Width = 100, Top = 90, Height = 30, DialogResult = DialogResult.OK };
            //Button cancelButton = new Button() { Text = "取消", Left = 260, Width = 100, Top = 90, Height = 30, DialogResult = DialogResult.Cancel };

            //inputDialog.Controls.Add(label);
            //inputDialog.Controls.Add(textBox);
            //inputDialog.Controls.Add(confirmButton);
            //inputDialog.Controls.Add(cancelButton);
            //inputDialog.AcceptButton = confirmButton;
            //inputDialog.CancelButton = cancelButton;

            var inputDialog = new CustomInputDialog("软件注册", "请输入注册码：", "");
            string inputLicense = "";
            if (inputDialog.ShowDialog() == DialogResult.OK)
            {
                inputLicense = inputDialog.InputText;
            }

            if (string.IsNullOrEmpty(inputLicense))
            {
                return; // 用户取消输入
            }

            string machineCode = HardwareInfo.GenerateMachineCode();

            // 验证注册码
            if (LicenseGenerator.ValidateLicenseKey(machineCode, inputLicense, "PictureMagicSecretKey"))
            {
                // 保存注册信息
                var settings = AppSettings.LoadSettings();
                settings.LicenseKey = inputLicense;
                AppSettings.SaveSettings(settings);

                // 更新界面
                btnRegister.Visible = false;
                Tools.IsRegistered = true;
                MessageBox.Show("注册成功！感谢您的支持！", "注册成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                Tools.IsRegistered = false;
                MessageBox.Show("注册码无效，请检查后重试！", "注册失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnCopyMachineCode_Click(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(txtMachineCode.Text))
            {
                Clipboard.SetText(txtMachineCode.Text);
                MessageBox.Show("序列号已复制到剪贴板！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void btnUnReg_Click(object sender, EventArgs e)
        {
            var inputDialog = new CustomInputDialog("取消注册", "请输入“取消注册”：", "");
            string inputLicense = "";
            var result = inputDialog.ShowDialog();
            if (result == DialogResult.Cancel)
                return;

            if (result == DialogResult.OK)
            {
                inputLicense = inputDialog.InputText;
            }

            if (string.IsNullOrEmpty(inputLicense))
            {
                MessageBox.Show("若要取消注册，请输入“取消注册”！", "取消注册", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return; // 用户取消输入
            }

            if (inputLicense == "取消注册")
            {
                // 取消注册
                var settings = AppSettings.LoadSettings();
                settings.LicenseKey = "";
                AppSettings.SaveSettings(settings);
                MessageBox.Show("取消注册成功！", "取消注册", MessageBoxButtons.OK, MessageBoxIcon.Information);
                btnRegister.Visible = true;
                btnUnReg.Visible = true;
                Tools.IsRegistered = false;
            }
            else
            {
                MessageBox.Show("请输入“取消注册”！", "取消注册", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}