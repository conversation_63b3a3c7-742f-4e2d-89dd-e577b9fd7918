namespace PictureMagic
{
    partial class ResizeDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }

                // 关闭进度窗体
                if (progressForm != null && !progressForm.IsDisposed)
                {
                    progressForm.CloseProgress();
                    progressForm = null;
                }
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            labelWidth = new Label();
            labelHeight = new Label();
            numericWidth = new NumericUpDown();
            numericHeight = new NumericUpDown();
            checkBoxMaintainAspectRatio = new CheckBox();
            buttonOK = new Button();
            buttonCancel = new Button();
            labelOriginalSize = new Label();
            labelNewSize = new Label();
            labelFileSize = new Label();
            groupBoxSize = new GroupBox();
            labelPresets = new Label();
            comboBoxPresets = new ComboBox();
            labelScaleRatio = new Label();
            comboBoxScaleRatio = new ComboBox();
            groupBoxMethod = new GroupBox();
            comboBoxResizeMethod = new ComboBox();
            labelMethod = new Label();
            originalPanel = new Panel();
            originalPictureBox = new PictureBox();
            previewPanel = new Panel();
            previewPictureBox = new PictureBox();
            originalLabel = new Label();
            previewLabel = new Label();

            ((System.ComponentModel.ISupportInitialize)numericWidth).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numericHeight).BeginInit();
            groupBoxSize.SuspendLayout();
            groupBoxMethod.SuspendLayout();
            originalPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)originalPictureBox).BeginInit();
            previewPanel.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)previewPictureBox).BeginInit();
            SuspendLayout();
            // 
            // labelWidth
            // 
            labelWidth.AutoSize = true;
            labelWidth.Location = new Point(28, 73);
            labelWidth.Name = "labelWidth";
            labelWidth.Size = new Size(43, 20);
            labelWidth.TabIndex = 0;
            labelWidth.Text = "宽度:";
            // 
            // labelHeight
            // 
            labelHeight.AutoSize = true;
            labelHeight.Location = new Point(28, 113);
            labelHeight.Name = "labelHeight";
            labelHeight.Size = new Size(43, 20);
            labelHeight.TabIndex = 1;
            labelHeight.Text = "高度:";
            // 
            // numericWidth
            // 
            numericWidth.Location = new Point(92, 71);
            numericWidth.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            numericWidth.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericWidth.Name = "numericWidth";
            numericWidth.Size = new Size(120, 27);
            numericWidth.TabIndex = 2;
            numericWidth.Value = new decimal(new int[] { 800, 0, 0, 0 });
            numericWidth.ValueChanged += numericWidth_ValueChanged;
            // 
            // numericHeight
            // 
            numericHeight.Location = new Point(92, 111);
            numericHeight.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            numericHeight.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numericHeight.Name = "numericHeight";
            numericHeight.Size = new Size(120, 27);
            numericHeight.TabIndex = 4;
            numericHeight.Value = new decimal(new int[] { 600, 0, 0, 0 });
            numericHeight.ValueChanged += numericHeight_ValueChanged;
            // 
            // checkBoxMaintainAspectRatio
            // 
            checkBoxMaintainAspectRatio.AutoSize = true;
            checkBoxMaintainAspectRatio.Checked = true;
            checkBoxMaintainAspectRatio.CheckState = CheckState.Checked;
            checkBoxMaintainAspectRatio.Location = new Point(22, 89);
            checkBoxMaintainAspectRatio.Name = "checkBoxMaintainAspectRatio";
            checkBoxMaintainAspectRatio.Size = new Size(106, 24);
            checkBoxMaintainAspectRatio.TabIndex = 4;
            checkBoxMaintainAspectRatio.Text = "保持纵横比";
            checkBoxMaintainAspectRatio.UseVisualStyleBackColor = true;
            checkBoxMaintainAspectRatio.CheckedChanged += checkBoxMaintainAspectRatio_CheckedChanged;
            // 
            // buttonOK
            // 
            buttonOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonOK.Location = new Point(506, 570);
            buttonOK.Name = "buttonOK";
            buttonOK.Size = new Size(119, 29);
            buttonOK.TabIndex = 5;
            buttonOK.Text = "确定";
            buttonOK.UseVisualStyleBackColor = true;
            buttonOK.Click += buttonOK_Click;
            // 
            // buttonCancel
            // 
            buttonCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonCancel.DialogResult = DialogResult.Cancel;
            buttonCancel.Location = new Point(633, 570);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new Size(119, 29);
            buttonCancel.TabIndex = 6;
            buttonCancel.Text = "取消";
            buttonCancel.UseVisualStyleBackColor = true;
            buttonCancel.Click += buttonCancel_Click;
            // 
            // labelOriginalSize
            // 
            labelOriginalSize.AutoSize = true;
            labelOriginalSize.Location = new Point(16, 9);
            labelOriginalSize.Name = "labelOriginalSize";
            labelOriginalSize.Size = new Size(190, 20);
            labelOriginalSize.TabIndex = 7;
            labelOriginalSize.Text = "原始尺寸: 1000 x 800 像素";
            // 
            // labelNewSize
            // 
            labelNewSize.AutoSize = true;
            labelNewSize.Location = new Point(16, 570);
            labelNewSize.Name = "labelNewSize";
            labelNewSize.Size = new Size(175, 20);
            labelNewSize.TabIndex = 8;
            labelNewSize.Text = "新尺寸: 1000 x 800 像素";
            // 
            // labelFileSize
            // 
            labelFileSize.AutoSize = true;
            labelFileSize.Location = new Point(281, 570);
            labelFileSize.Name = "labelFileSize";
            labelFileSize.Size = new Size(109, 20);
            labelFileSize.TabIndex = 11;
            labelFileSize.Text = "文件大小: 0 KB";
            // 
            // groupBoxSize
            // 
            groupBoxSize.Controls.Add(labelPresets);
            groupBoxSize.Controls.Add(comboBoxPresets);
            groupBoxSize.Controls.Add(labelWidth);
            groupBoxSize.Controls.Add(labelHeight);
            groupBoxSize.Controls.Add(numericWidth);
            groupBoxSize.Controls.Add(numericHeight);
            groupBoxSize.Controls.Add(labelScaleRatio);
            groupBoxSize.Controls.Add(comboBoxScaleRatio);
            groupBoxSize.Location = new Point(16, 41);
            groupBoxSize.Name = "groupBoxSize";
            groupBoxSize.Size = new Size(360, 205);
            groupBoxSize.TabIndex = 9;
            groupBoxSize.TabStop = false;
            groupBoxSize.Text = "尺寸";
            // 
            // labelPresets
            // 
            labelPresets.AutoSize = true;
            labelPresets.Location = new Point(27, 30);
            labelPresets.Name = "labelPresets";
            labelPresets.Size = new Size(43, 20);
            labelPresets.TabIndex = 6;
            labelPresets.Text = "预设:";
            // 
            // comboBoxPresets
            // 
            comboBoxPresets.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxPresets.FormattingEnabled = true;
            comboBoxPresets.Location = new Point(91, 27);
            comboBoxPresets.Name = "comboBoxPresets";
            comboBoxPresets.Size = new Size(191, 28);
            comboBoxPresets.TabIndex = 5;
            comboBoxPresets.SelectedIndexChanged += comboBoxPresets_SelectedIndexChanged;
            // 
            // labelScaleRatio
            // 
            labelScaleRatio.AutoSize = true;
            labelScaleRatio.Location = new Point(28, 153);
            labelScaleRatio.Name = "labelScaleRatio";
            labelScaleRatio.Size = new Size(43, 20);
            labelScaleRatio.TabIndex = 8;
            labelScaleRatio.Text = "比例:";
            // 
            // comboBoxScaleRatio
            // 
            comboBoxScaleRatio.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxScaleRatio.FormattingEnabled = true;
            comboBoxScaleRatio.Location = new Point(92, 150);
            comboBoxScaleRatio.Name = "comboBoxScaleRatio";
            comboBoxScaleRatio.Size = new Size(190, 28);
            comboBoxScaleRatio.TabIndex = 9;
            comboBoxScaleRatio.SelectedIndexChanged += comboBoxScaleRatio_SelectedIndexChanged;
            // 
            // groupBoxMethod
            // 
            groupBoxMethod.Controls.Add(comboBoxResizeMethod);
            groupBoxMethod.Controls.Add(labelMethod);
            groupBoxMethod.Controls.Add(checkBoxMaintainAspectRatio);
            groupBoxMethod.Location = new Point(394, 41);
            groupBoxMethod.Name = "groupBoxMethod";
            groupBoxMethod.Size = new Size(351, 165);
            groupBoxMethod.TabIndex = 10;
            groupBoxMethod.TabStop = false;
            groupBoxMethod.Text = "调整方法";
            // 
            // comboBoxResizeMethod
            // 
            comboBoxResizeMethod.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxResizeMethod.FormattingEnabled = true;
            comboBoxResizeMethod.Location = new Point(86, 38);
            comboBoxResizeMethod.Name = "comboBoxResizeMethod";
            comboBoxResizeMethod.Size = new Size(238, 28);
            comboBoxResizeMethod.TabIndex = 1;
            comboBoxResizeMethod.SelectedIndexChanged += comboBoxResizeMethod_SelectedIndexChanged;
            // 
            // labelMethod
            // 
            labelMethod.AutoSize = true;
            labelMethod.Location = new Point(22, 43);
            labelMethod.Name = "labelMethod";
            labelMethod.Size = new Size(43, 20);
            labelMethod.TabIndex = 0;
            labelMethod.Text = "方法:";
            // 
            // originalPanel
            // 
            originalPanel.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left;
            originalPanel.AutoScroll = true;
            originalPanel.BorderStyle = BorderStyle.FixedSingle;
            originalPanel.Controls.Add(originalPictureBox);
            originalPanel.Location = new Point(16, 247);
            originalPanel.Name = "originalPanel";
            originalPanel.Size = new Size(360, 307);
            originalPanel.TabIndex = 12;
            // 
            // originalPictureBox
            // 
            originalPictureBox.Location = new Point(0, 0);
            originalPictureBox.Name = "originalPictureBox";
            originalPictureBox.Size = new Size(112, 50);
            originalPictureBox.TabIndex = 0;
            originalPictureBox.TabStop = false;
            // 
            // previewPanel
            // 
            previewPanel.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            previewPanel.AutoScroll = true;
            previewPanel.BorderStyle = BorderStyle.FixedSingle;
            previewPanel.Controls.Add(previewPictureBox);
            // 移除进度条和标签控件
            // previewPanel.Controls.Add(progressBarPreview);
            // previewPanel.Controls.Add(labelProgress);
            previewPanel.Location = new Point(394, 247);
            previewPanel.Name = "previewPanel";
            previewPanel.Size = new Size(360, 307);
            previewPanel.TabIndex = 13;
            // 
            // previewPictureBox
            // 
            previewPictureBox.Location = new Point(0, 0);
            previewPictureBox.Name = "previewPictureBox";
            previewPictureBox.Size = new Size(112, 50);
            previewPictureBox.TabIndex = 0;
            previewPictureBox.TabStop = false;
            // 
            // originalLabel
            // 
            originalLabel.AutoSize = true;
            originalLabel.Location = new Point(16, 224);
            originalLabel.Name = "originalLabel";
            originalLabel.Size = new Size(69, 20);
            originalLabel.TabIndex = 14;
            originalLabel.Text = "原始图像";
            // 
            // previewLabel
            // 
            previewLabel.AutoSize = true;
            previewLabel.Location = new Point(394, 224);
            previewLabel.Name = "previewLabel";
            previewLabel.Size = new Size(84, 20);
            previewLabel.TabIndex = 15;
            previewLabel.Text = "调整后预览";
            // 
            // 注释掉进度条和提示文本控件的初始化代码
            // progressBarPreview
            // 
            // progressBarPreview.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            // progressBarPreview.Location = new Point(10, 270);
            // progressBarPreview.Name = "progressBarPreview";
            // progressBarPreview.Size = new Size(340, 23);
            // progressBarPreview.TabIndex = 16;
            // progressBarPreview.Visible = false;
            // 
            // labelProgress
            // 
            // labelProgress.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            // labelProgress.AutoSize = true;
            // labelProgress.Location = new Point(10, 247);
            // labelProgress.Name = "labelProgress";
            // labelProgress.Size = new Size(99, 20);
            // labelProgress.TabIndex = 17;
            // labelProgress.Text = "正在处理...";
            // labelProgress.BackColor = Color.White;
            // labelProgress.Visible = false;
            // 
            // ResizeDialog
            // 
            AcceptButton = buttonOK;
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            CancelButton = buttonCancel;
            ClientSize = new Size(765, 610);
            Controls.Add(previewLabel);
            Controls.Add(originalLabel);
            Controls.Add(previewPanel);
            Controls.Add(originalPanel);
            Controls.Add(labelFileSize);
            Controls.Add(groupBoxMethod);
            Controls.Add(groupBoxSize);
            Controls.Add(labelNewSize);
            Controls.Add(labelOriginalSize);
            Controls.Add(buttonCancel);
            Controls.Add(buttonOK);
            // 移除进度条和标签控件
            // Controls.Add(progressBarPreview);
            // Controls.Add(labelProgress);
            // 移除将进度条和标签提到最前面的代码
            // progressBarPreview.BringToFront();
            // labelProgress.BringToFront();
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "ResizeDialog";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "调整图像大小";
            ((System.ComponentModel.ISupportInitialize)numericWidth).EndInit();
            ((System.ComponentModel.ISupportInitialize)numericHeight).EndInit();
            groupBoxSize.ResumeLayout(false);
            groupBoxSize.PerformLayout();
            groupBoxMethod.ResumeLayout(false);
            groupBoxMethod.PerformLayout();
            originalPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)originalPictureBox).EndInit();
            previewPanel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)previewPictureBox).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private Label labelWidth;
        private Label labelHeight;
        private NumericUpDown numericWidth;
        private NumericUpDown numericHeight;
        private CheckBox checkBoxMaintainAspectRatio;
        private Button buttonOK;
        private Button buttonCancel;
        private Label labelOriginalSize;
        private Label labelNewSize;
        private Label labelFileSize;
        private GroupBox groupBoxSize;
        private GroupBox groupBoxMethod;
        private Label labelMethod;
        private ComboBox comboBoxResizeMethod;
        private Label labelPresets;
        private ComboBox comboBoxPresets;
        private Label labelScaleRatio;
        private ComboBox comboBoxScaleRatio;
        private Panel originalPanel;
        private PictureBox originalPictureBox;
        private Panel previewPanel;
        private PictureBox previewPictureBox;
        private Label originalLabel;
        private Label previewLabel;
        // 移除进度条和提示文本控件的字段定义
        // private ProgressBar progressBarPreview;
        // private Label labelProgress;
    }
}