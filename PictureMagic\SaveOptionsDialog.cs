using PictureMagic.Common;
using System.ComponentModel;
using System.Drawing.Imaging;
using System.Text;

namespace PictureMagic
{
    public partial class SaveOptionsDialog : Form
    {
        private Image originalImage;
        private string originalFilePath;
        private string suggestedFileName;
        private bool preventOverwrite;
        private long lastEstimatedSize;

        // 不使用进度提示

        // 保存选项
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public string SelectedFilePath { get; private set; }
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public ImageFormat SelectedFormat { get; private set; }
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int JpegQuality { get; private set; }
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public EncoderParameters EncoderParams { get; private set; }

        public SaveOptionsDialog(Image image, string originalFilePath = "", bool preventOverwrite = false, bool isBatchSave = false)
        {
            InitializeComponent();
            this.originalImage = image;
            this.originalFilePath = originalFilePath;
            this.preventOverwrite = preventOverwrite;
            this.JpegQuality = 100; // 默认JPEG质量

            // 添加窗体大小改变事件处理程序
            this.Resize += SaveOptionsDialog_Resize;

            // 初始化界面
            InitializeUI();

            // 如果是批量保存模式，禁用文件路径输入框和浏览按钮
            if (isBatchSave)
            {
                textBoxFilePath.Enabled = false;
                buttonBrowse.Enabled = false;
            }

            if (!Tools.IsRegistered)
            {
                trackBarQuality.Value = 10;
                labelQualityValue.Text = "10%";
                trackBarQuality.Enabled = false;
                comboBoxFormat.Enabled = false;
                trackBarQuality_Scroll(null, null);
            }
            else
            {
                trackBarQuality.Enabled = true;
                comboBoxFormat.Enabled = true;
                trackBarQuality.Value = 100;
                labelQualityValue.Text = "100%";

            }

            // 更新文件大小预估
            UpdateFileSizeEstimate();
        }



        private void SaveOptionsDialog_Resize(object sender, EventArgs e)
        {
            // 窗体大小改变时调整预览图像大小
            AdjustPreviewSize();
        }

        private void InitializeUI()
        {
            // 设置默认格式为JPEG
            comboBoxFormat.Items.AddRange(new string[] { "JPEG (.jpg)", "PNG (.png)", "GIF (.gif)", "BMP (.bmp)" });
            comboBoxFormat.SelectedIndex = 0;

            // 设置质量滑块
            trackBarQuality.Minimum = 1;
            trackBarQuality.Maximum = 100;
            trackBarQuality.Value = JpegQuality;
            labelQualityValue.Text = $"{JpegQuality}%";

            // 设置建议的文件名
            if (!string.IsNullOrEmpty(originalFilePath))
            {
                string directory = Path.GetDirectoryName(originalFilePath);
                string fileName = Path.GetFileNameWithoutExtension(originalFilePath);
                string extension = ".jpg"; // 默认扩展名

                // 始终添加随机后缀，无论是否需要防止覆盖

                fileName += $"--{DateTime.Now.ToString("yyyyMMdd--HHmmssfff")}";

                suggestedFileName = Path.Combine(directory, fileName + extension);
                textBoxFilePath.Text = suggestedFileName;
            }
            else
            {
                // 如果没有原始文件路径，使用默认路径
                string documentsPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                string randomSuffix = GenerateRandomSuffix(6);
                suggestedFileName = Path.Combine(documentsPath, $"未命名_{randomSuffix}.jpg");
                textBoxFilePath.Text = suggestedFileName;
            }

            // 显示原始图像信息
            labelOriginalSize.Text = $"图像尺寸: {originalImage.Width} x {originalImage.Height} 像素";

            // 设置预览图像
            pictureBoxPreview.Image = originalImage;
            pictureBoxPreview.SizeMode = PictureBoxSizeMode.Zoom;

            // 调整预览图像框的大小以适应图像
            AdjustPreviewSize();

            // 根据选择的格式更新UI
            UpdateUIForSelectedFormat();
        }

        private void UpdateUIForSelectedFormat()
        {
            // 根据选择的格式启用或禁用质量设置
            bool isJpeg = comboBoxFormat.SelectedIndex == 0;
            labelQuality.Enabled = isJpeg;
            trackBarQuality.Enabled = isJpeg;
            labelQualityValue.Enabled = isJpeg;

            // 更新文件扩展名
            string extension = GetExtensionForSelectedFormat();
            if (!string.IsNullOrEmpty(textBoxFilePath.Text))
            {
                string directory = Path.GetDirectoryName(textBoxFilePath.Text);
                string fileName = Path.GetFileNameWithoutExtension(textBoxFilePath.Text);
                textBoxFilePath.Text = Path.Combine(directory, fileName + extension);
            }

            // 更新预览图像以反映当前格式和质量设置
            UpdatePreviewImage();

            // 更新文件大小预估
            UpdateFileSizeEstimate();
        }

        private string GetExtensionForSelectedFormat()
        {
            switch (comboBoxFormat.SelectedIndex)
            {
                case 0: return ".jpg";
                case 1: return ".png";
                case 2: return ".gif";
                case 3: return ".bmp";
                default: return ".jpg";
            }
        }

        private ImageFormat GetSelectedImageFormat()
        {
            switch (comboBoxFormat.SelectedIndex)
            {
                case 0: return ImageFormat.Jpeg;
                case 1: return ImageFormat.Png;
                case 2: return ImageFormat.Gif;
                case 3: return ImageFormat.Bmp;
                default: return ImageFormat.Jpeg;
            }
        }

        private void UpdateFileSizeEstimate()
        {
            if (originalImage == null) return;

            try
            {
                // 使用内存流来估算文件大小
                using (MemoryStream ms = new MemoryStream())
                {
                    ImageFormat format = GetSelectedImageFormat();

                    if (format.Equals(ImageFormat.Jpeg))
                    {
                        // 对于JPEG格式，使用指定的质量设置
                        EncoderParameters encoderParams = new EncoderParameters(1);
                        encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, (long)trackBarQuality.Value);

                        ImageCodecInfo jpegCodec = GetEncoderInfo("image/jpeg");
                        originalImage.Save(ms, jpegCodec, encoderParams);
                    }
                    else
                    {
                        // 对于其他格式，使用默认设置
                        originalImage.Save(ms, format);
                    }

                    lastEstimatedSize = ms.Length;
                    labelEstimatedSize.Text = $"预估文件大小: {FormatFileSize(ms.Length)}";
                }
            }
            catch (Exception ex)
            {
                labelEstimatedSize.Text = "无法估算文件大小";
                Console.WriteLine($"估算文件大小出错: {ex.Message}");
            }
            finally
            {
                // 不需要进度提示
            }
        }

        private ImageCodecInfo GetEncoderInfo(string mimeType)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.MimeType == mimeType)
                {
                    return codec;
                }
            }
            return null;
        }

        private string FormatFileSize(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;

            while (number > 1024 && counter < suffixes.Length - 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{Math.Round(number, 2)} {suffixes[counter]}";
        }

        private void buttonBrowse_Click(object sender, EventArgs e)
        {
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                // 设置对话框属性
                saveFileDialog.Filter = "JPEG 图像|*.jpg;*.jpeg|PNG 图像|*.png|GIF 图像|*.gif|BMP 图像|*.bmp|所有文件|*.*";
                saveFileDialog.Title = "选择保存位置";
                saveFileDialog.DefaultExt = GetExtensionForSelectedFormat().Substring(1); // 移除点号
                saveFileDialog.FileName = Path.GetFileName(textBoxFilePath.Text);
                saveFileDialog.InitialDirectory = Path.GetDirectoryName(textBoxFilePath.Text);

                // 根据当前选择的格式设置过滤器索引
                saveFileDialog.FilterIndex = comboBoxFormat.SelectedIndex + 1;

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    textBoxFilePath.Text = saveFileDialog.FileName;

                    // 根据选择的文件类型更新格式下拉框
                    string extension = Path.GetExtension(saveFileDialog.FileName).ToLower();
                    switch (extension)
                    {
                        case ".jpg":
                        case ".jpeg":
                            comboBoxFormat.SelectedIndex = 0;
                            break;
                        case ".png":
                            comboBoxFormat.SelectedIndex = 1;
                            break;
                        case ".gif":
                            comboBoxFormat.SelectedIndex = 2;
                            break;
                        case ".bmp":
                            comboBoxFormat.SelectedIndex = 3;
                            break;
                    }
                }
            }
        }

        private void comboBoxFormat_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateUIForSelectedFormat();
        }

        private void trackBarQuality_Scroll(object sender, EventArgs e)
        {
            JpegQuality = trackBarQuality.Value;
            labelQualityValue.Text = $"{JpegQuality}%";

            // 更新预览图像以反映新的质量设置
            UpdatePreviewImage();

            // 更新文件大小预估
            UpdateFileSizeEstimate();
        }

        private void buttonSave_Click(object sender, EventArgs e)
        {
            // 检查文件路径是否有效
            if (string.IsNullOrWhiteSpace(textBoxFilePath.Text))
            {
                MessageBox.Show("请输入有效的文件路径", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 检查是否会覆盖原文件
            if (preventOverwrite && File.Exists(textBoxFilePath.Text) &&
                string.Equals(Path.GetFullPath(textBoxFilePath.Text), Path.GetFullPath(originalFilePath), StringComparison.OrdinalIgnoreCase))
            {
                MessageBox.Show("不能覆盖原始文件，请选择不同的文件名或路径", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            // 检查文件是否已存在
            if (File.Exists(textBoxFilePath.Text))
            {
                DialogResult result = MessageBox.Show("文件已存在，是否覆盖？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result != DialogResult.Yes)
                {
                    return;
                }
            }

            // 设置返回值
            SelectedFilePath = textBoxFilePath.Text;
            SelectedFormat = GetSelectedImageFormat();

            // 如果是JPEG格式，设置编码参数
            if (SelectedFormat.Equals(ImageFormat.Jpeg))
            {
                EncoderParams = new EncoderParameters(1);
                // 确保JPEG质量值在有效范围内（0-100），并使用long类型而不是byte
                EncoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, (long)JpegQuality);
            }
            else
            {
                EncoderParams = null;
            }

            DialogResult = DialogResult.OK;
            Close();
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void AdjustPreviewSize()
        {
            if (originalImage == null) return;

            // 设置pictureBoxPreview的尺寸以匹配图像的宽高比
            float imageRatio = (float)originalImage.Width / originalImage.Height;

            // 根据panelPreview的尺寸调整pictureBoxPreview的大小
            if (imageRatio > 1) // 宽图像
            {
                // 宽度设为面板宽度，高度按比例计算
                pictureBoxPreview.Width = panelPreview.ClientSize.Width - 4; // 减去边框宽度
                pictureBoxPreview.Height = (int)(pictureBoxPreview.Width / imageRatio);
            }
            else // 高图像或正方形
            {
                // 高度设为面板高度，宽度按比例计算
                pictureBoxPreview.Height = panelPreview.ClientSize.Height - 4; // 减去边框高度
                pictureBoxPreview.Width = (int)(pictureBoxPreview.Height * imageRatio);
            }

            // 居中显示
            pictureBoxPreview.Left = (panelPreview.ClientSize.Width - pictureBoxPreview.Width) / 2;
            pictureBoxPreview.Top = (panelPreview.ClientSize.Height - pictureBoxPreview.Height) / 2;
        }

        private void UpdatePreviewImage()
        {
            if (originalImage == null) return;

            try
            {
                // 释放之前的预览图像（如果不是原始图像）
                if (pictureBoxPreview.Image != null && pictureBoxPreview.Image != originalImage)
                {
                    pictureBoxPreview.Image.Dispose();
                }

                // 对于JPEG格式，应用质量设置以显示压缩效果
                if (comboBoxFormat.SelectedIndex == 0) // JPEG
                {
                    // 使用内存流和当前质量设置创建JPEG预览
                    using (MemoryStream ms = new MemoryStream())
                    {
                        EncoderParameters encoderParams = new EncoderParameters(1);
                        encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, (long)JpegQuality);

                        ImageCodecInfo jpegCodec = GetEncoderInfo("image/jpeg");
                        originalImage.Save(ms, jpegCodec, encoderParams);

                        // 重置流位置并从流创建新图像
                        ms.Position = 0;
                        Image previewImage = Image.FromStream(ms);
                        pictureBoxPreview.Image = previewImage;
                    }
                }
                else
                {
                    // 对于其他格式，直接显示原始图像
                    // 注意：这里简化处理，实际上不同格式可能有不同的视觉效果
                    pictureBoxPreview.Image = originalImage;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新预览图像出错: {ex.Message}");
                // 出错时使用原始图像
                pictureBoxPreview.Image = originalImage;
            }
            finally
            {
                // 不需要进度提示
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            // 释放资源
            if (pictureBoxPreview.Image != null && pictureBoxPreview.Image != originalImage)
            {
                pictureBoxPreview.Image.Dispose();
            }
        }

        /// <summary>
        /// 生成指定长度的随机字符串作为文件名后缀
        /// </summary>
        /// <param name="length">后缀长度</param>
        /// <returns>随机字符串</returns>
        private string GenerateRandomSuffix(int length)
        {
            // 定义可用字符集（字母和数字）
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            Random random = new Random();

            // 使用StringBuilder构建随机字符串
            StringBuilder sb = new StringBuilder(length);
            for (int i = 0; i < length; i++)
            {
                sb.Append(chars[random.Next(chars.Length)]);
            }

            return sb.ToString();
        }
    }
}