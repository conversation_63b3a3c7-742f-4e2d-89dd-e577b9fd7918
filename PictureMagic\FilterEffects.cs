using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.Runtime.InteropServices;

namespace PictureMagic
{
    public static class FilterEffects
    {
        // 滤镜类型枚举
        public enum FilterType
        {
            None,
            Grayscale,
            Sepia,
            Invert,
            Blur,
            Sharpen,
            EdgeDetection,
            Emboss,
            Vintage,
            BlackAndWhite,
            Vignette,
            Pixelate,
            OilPainting,
            Posterize,
            Solarize,
            Neon
        }

        // 获取所有滤镜名称
        public static List<string> GetFilterNames()
        {
            List<string> names = new List<string>();
            foreach (FilterType filter in Enum.GetValues(typeof(FilterType)))
            {
                names.Add(GetFilterDisplayName(filter));
            }
            return names;
        }

        // 获取滤镜显示名称
        public static string GetFilterDisplayName(FilterType filter)
        {
            switch (filter)
            {
                case FilterType.None: return "无滤镜";
                case FilterType.Grayscale: return "灰度";
                case FilterType.Sepia: return "复古棕褐色";
                case FilterType.Invert: return "反相";
                case FilterType.Blur: return "模糊";
                case FilterType.Sharpen: return "锐化";
                case FilterType.EdgeDetection: return "边缘检测";
                case FilterType.Emboss: return "浮雕";
                case FilterType.Vintage: return "老照片";
                case FilterType.BlackAndWhite: return "黑白";
                case FilterType.Vignette: return "暗角";
                case FilterType.Pixelate: return "像素化";
                case FilterType.OilPainting: return "油画";
                case FilterType.Posterize: return "海报化";
                case FilterType.Solarize: return "曝光";
                case FilterType.Neon: return "霓虹";
                default: return "未知滤镜";
            }
        }

        // 根据滤镜名称获取滤镜类型
        public static FilterType GetFilterTypeByName(string name)
        {
            // 添加null检查
            if (name == null)
                return FilterType.None;
                
            foreach (FilterType filter in Enum.GetValues(typeof(FilterType)))
            {
                if (GetFilterDisplayName(filter) == name)
                    return filter;
            }
            return FilterType.None;
        }

        // 应用滤镜
        public static Bitmap ApplyFilter(Image image, FilterType filterType, int intensity = 100, ProgressForm progressForm = null)
        {
            // 添加null检查并返回空白图像而不是null
            if (image == null) 
                return new Bitmap(1, 1); // 返回一个1x1的空白图像而不是null

            // 对于"无滤镜"选项，直接返回原图的精确副本
            if (filterType == FilterType.None)
            {
                return new Bitmap(image);
            }
            
            // 创建图像副本用于其他滤镜
            Bitmap result = new Bitmap(image.Width, image.Height);
            
            // 更新进度消息
            string filterName = GetFilterDisplayName(filterType);
            if (progressForm != null)
            {
                progressForm.UpdateMessage($"正在应用{filterName}滤镜...");
                progressForm.SetContinuousStyle(0, 100);
            }
            
            // 根据滤镜类型应用不同效果
            switch (filterType)
            {
                case FilterType.Grayscale:
                    ApplyGrayscale(image, result, intensity, progressForm);
                    break;
                case FilterType.Sepia:
                    ApplySepia(image, result, intensity, progressForm);
                    break;
                case FilterType.Invert:
                    ApplyInvert(image, result, progressForm);
                    break;
                case FilterType.Blur:
                    ApplyBlur(image, result, intensity, progressForm);
                    break;
                case FilterType.Sharpen:
                    ApplySharpen(image, result, intensity, progressForm);
                    break;
                case FilterType.EdgeDetection:
                    ApplyEdgeDetection(image, result, intensity, progressForm);
                    break;
                case FilterType.Emboss:
                    ApplyEmboss(image, result, progressForm);
                    break;
                case FilterType.Vintage:
                    ApplyVintage(image, result, intensity, progressForm);
                    break;
                case FilterType.BlackAndWhite:
                    ApplyBlackAndWhite(image, result, intensity, progressForm);
                    break;
                case FilterType.Vignette:
                    ApplyVignette(image, result, intensity, progressForm);
                    break;
                case FilterType.Pixelate:
                    ApplyPixelate(image, result, intensity, progressForm);
                    break;
                case FilterType.OilPainting:
                    ApplyOilPainting(image, result, intensity, progressForm);
                    break;
                case FilterType.Posterize:
                    ApplyPosterize(image, result, intensity, progressForm);
                    break;
                case FilterType.Solarize:
                    ApplySolarize(image, result, intensity, progressForm);
                    break;
                case FilterType.Neon:
                    ApplyNeon(image, result, intensity, progressForm);
                    break;
                default:
                    using (Graphics g = Graphics.FromImage(result))
                    {
                        g.DrawImage(image, 0, 0, image.Width, image.Height);
                    }
                    break;
            }

            // 完成进度 - 只更新消息，不重复更新进度值
            // 因为大多数滤镜方法内部已经更新到95%或100%
            if (progressForm != null)
            {
                // 确保进度达到100%，但避免重复更新
                int currentProgress = progressForm.GetCurrentProgress();
                if (currentProgress < 100)
                {
                    progressForm.UpdateProgress(100);
                }
                progressForm.UpdateMessage($"{filterName}滤镜应用完成");
            }

            return result;
        }

        // 灰度滤镜
        private static void ApplyGrayscale(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            float intensityFactor = intensity / 100f;
            Bitmap bmp = new Bitmap(image);
            int height = bmp.Height;
            int width = bmp.Width;

            for (int y = 0; y < height; y++)
            {
                // 更新进度条
                if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                {
                    int progressPercentage = (int)((float)y / height * 100);
                    progressForm.UpdateProgress(progressPercentage);
                }

                for (int x = 0; x < width; x++)
                {
                    Color c = bmp.GetPixel(x, y);
                    int gray = (int)(c.R * 0.3 + c.G * 0.59 + c.B * 0.11);

                    // 根据强度混合原始颜色和灰度
                    int r = (int)(gray * intensityFactor + c.R * (1 - intensityFactor));
                    int g = (int)(gray * intensityFactor + c.G * (1 - intensityFactor));
                    int b = (int)(gray * intensityFactor + c.B * (1 - intensityFactor));

                    result.SetPixel(x, y, Color.FromArgb(c.A, r, g, b));
                }
            }
        }

        // 复古棕褐色滤镜
        private static void ApplySepia(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            float intensityFactor = intensity / 100f;
            Bitmap bmp = new Bitmap(image);
            int height = bmp.Height;
            int width = bmp.Width;

            for (int y = 0; y < height; y++)
            {
                // 更新进度条
                if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                {
                    int progressPercentage = (int)((float)y / height * 100);
                    progressForm.UpdateProgress(progressPercentage);
                }

                for (int x = 0; x < width; x++)
                {
                    Color c = bmp.GetPixel(x, y);
                    int gray = (int)(c.R * 0.3 + c.G * 0.59 + c.B * 0.11);

                    // 棕褐色效果
                    int sepiaR = Math.Min(255, (int)(gray * 1.08));
                    int sepiaG = Math.Min(255, (int)(gray * 0.82));
                    int sepiaB = Math.Min(255, (int)(gray * 0.55));

                    // 根据强度混合原始颜色和棕褐色
                    int r = (int)(sepiaR * intensityFactor + c.R * (1 - intensityFactor));
                    int g = (int)(sepiaG * intensityFactor + c.G * (1 - intensityFactor));
                    int b = (int)(sepiaB * intensityFactor + c.B * (1 - intensityFactor));

                    result.SetPixel(x, y, Color.FromArgb(c.A, r, g, b));
                }
            }
        }

        // 反相滤镜
        private static void ApplyInvert(Image image, Bitmap result, ProgressForm progressForm = null)
        {
            Bitmap bmp = new Bitmap(image);
            int height = bmp.Height;
            int width = bmp.Width;

            for (int y = 0; y < height; y++)
            {
                // 更新进度条
                if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                {
                    int progressPercentage = (int)((float)y / height * 100);
                    progressForm.UpdateProgress(progressPercentage);
                }

                for (int x = 0; x < width; x++)
                {
                    Color c = bmp.GetPixel(x, y);
                    result.SetPixel(x, y, Color.FromArgb(c.A, 255 - c.R, 255 - c.G, 255 - c.B));
                }
            }
        }

        // 模糊滤镜
        private static void ApplyBlur(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            try
            {
                // 更新进度消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在创建模糊滤镜...");
                    progressForm.UpdateProgress(10);
                }

                // 根据强度确定模糊半径
                int radius = Math.Max(1, intensity / 20);
                Bitmap bmp = new Bitmap(image);

                // 创建高斯模糊卷积核
                int size = radius * 2 + 1;
                float[,] kernel = new float[size, size];
                float sigma = radius / 3.0f;
                float sum = 0.0f;

                for (int y = -radius; y <= radius; y++)
                {
                    for (int x = -radius; x <= radius; x++)
                    {
                        float value = (float)Math.Exp(-(x * x + y * y) / (2 * sigma * sigma));
                        kernel[y + radius, x + radius] = value;
                        sum += value;
                    }
                }

                // 归一化卷积核
                for (int y = 0; y < size; y++)
                {
                    for (int x = 0; x < size; x++)
                    {
                        kernel[y, x] /= sum;
                    }
                }

                // 更新进度
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在应用模糊效果...");
                    progressForm.UpdateProgress(20);
                }

                // 应用卷积 - 将进度范围限制在20-95之间，避免与ApplyFilter中的进度更新重叠
                ApplyConvolution(bmp, result, kernel, progressForm, 20, 95);
                
                // 确保进度更新到95%
                if (progressForm != null)
                {
                    progressForm.UpdateProgress(95);
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                System.Diagnostics.Debug.WriteLine($"应用模糊滤镜时发生错误: {ex.Message}");
                
                // 如果有进度窗体，更新错误消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage($"应用模糊滤镜时发生错误: {ex.Message}");
                    progressForm.UpdateProgress(100); // 确保进度条完成
                }
                
                // 重新抛出异常，让上层处理
                throw;
            }
        }

        // 锐化滤镜
        private static void ApplySharpen(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            try
            {
                // 更新进度消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在创建锐化滤镜...");
                    progressForm.UpdateProgress(10);
                }

                float factor = intensity / 100.0f;
                Bitmap bmp = new Bitmap(image);

                // 创建锐化卷积核
                float[,] kernel = {
                    { -factor, -factor, -factor },
                    { -factor, 1 + 8 * factor, -factor },
                    { -factor, -factor, -factor }
                };

                // 更新进度
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在应用锐化效果...");
                    progressForm.UpdateProgress(20);
                }

                // 应用卷积 - 将进度范围限制在20-95之间，避免与ApplyFilter中的进度更新重叠
                ApplyConvolution(bmp, result, kernel, progressForm, 20, 95);
                
                // 确保进度更新到95%
                if (progressForm != null)
                {
                    progressForm.UpdateProgress(95);
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                System.Diagnostics.Debug.WriteLine($"应用锐化滤镜时发生错误: {ex.Message}");
                
                // 如果有进度窗体，更新错误消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage($"应用锐化滤镜时发生错误: {ex.Message}");
                    progressForm.UpdateProgress(100); // 确保进度条完成
                }
                
                // 重新抛出异常，让上层处理
                throw;
            }
        }

        // 边缘检测滤镜
        private static void ApplyEdgeDetection(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 声明临时图像变量，以便在finally块中释放
            Bitmap tempX = null;
            Bitmap tempY = null;
            Bitmap bmp = null;
            
            try
            {
                // 更新进度消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在创建边缘检测滤镜...");
                    progressForm.UpdateProgress(10);
                }

                float factor = intensity / 100.0f;
                bmp = new Bitmap(image);

                // 创建边缘检测卷积核 (Sobel算子)
                float[,] kernelX = {
                    { -1 * factor, 0, 1 * factor },
                    { -2 * factor, 0, 2 * factor },
                    { -1 * factor, 0, 1 * factor }
                };

                float[,] kernelY = {
                    { -1 * factor, -2 * factor, -1 * factor },
                    { 0, 0, 0 },
                    { 1 * factor, 2 * factor, 1 * factor }
                };

                // 创建临时图像
                tempX = new Bitmap(bmp.Width, bmp.Height);
                tempY = new Bitmap(bmp.Width, bmp.Height);

                // 更新进度
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在应用X方向边缘检测...");
                    progressForm.UpdateProgress(20);
                }

                // 应用X方向卷积 - 不在内部更新到50%，而是只更新到40%
                ApplyConvolution(bmp, tempX, kernelX, progressForm, 20, 40);

                // 更新进度
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在应用Y方向边缘检测...");
                    progressForm.UpdateProgress(40);
                }

                // 应用Y方向卷积 - 不在内部更新到70%，而是只更新到60%
                ApplyConvolution(bmp, tempY, kernelY, progressForm, 40, 60);

                // 更新进度
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在合并边缘检测结果...");
                    progressForm.UpdateProgress(60);
                }

                // 合并结果
                for (int y = 0; y < bmp.Height; y++)
                {
                    // 更新进度条
                    if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                    {
                        int progressPercentage = 60 + (int)((float)y / bmp.Height * 35); // 60%-95%的进度
                        progressForm.UpdateProgress(progressPercentage);
                    }

                    for (int x = 0; x < bmp.Width; x++)
                    {
                        Color cX = tempX.GetPixel(x, y);
                        Color cY = tempY.GetPixel(x, y);

                        // 计算梯度幅值
                        int r = Math.Min(255, (int)Math.Sqrt(cX.R * cX.R + cY.R * cY.R));
                        int g = Math.Min(255, (int)Math.Sqrt(cX.G * cX.G + cY.G * cY.G));
                        int b = Math.Min(255, (int)Math.Sqrt(cX.B * cX.B + cY.B * cY.B));

                        result.SetPixel(x, y, Color.FromArgb(255, r, g, b));
                    }
                }

                // 更新进度到95%
                if (progressForm != null)
                {
                    progressForm.UpdateProgress(95);
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                System.Diagnostics.Debug.WriteLine($"应用边缘检测滤镜时发生错误: {ex.Message}");
                
                // 如果有进度窗体，更新错误消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage($"应用边缘检测滤镜时发生错误: {ex.Message}");
                    progressForm.UpdateProgress(100); // 确保进度条完成
                }
                
                // 重新抛出异常，让上层处理
                throw;
            }
            finally
            {
                // 释放临时图像
                if (tempX != null) tempX.Dispose();
                if (tempY != null) tempY.Dispose();
                if (bmp != null && bmp != image) bmp.Dispose();
            }
        }

        // 浮雕滤镜
        private static void ApplyEmboss(Image image, Bitmap result, ProgressForm progressForm = null)
        {
            try
            {
                // 更新进度消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage("正在应用浮雕效果...");
                    progressForm.UpdateProgress(10);
                }

                Bitmap bmp = new Bitmap(image);

                // 创建浮雕卷积核
                float[,] kernel = {
                    { -2, -1, 0 },
                    { -1, 1, 1 },
                    { 0, 1, 2 }
                };

                // 应用卷积
                ApplyConvolution(bmp, result, kernel, progressForm, 10, 100, 128);
                
                // 确保进度更新到95%
                if (progressForm != null)
                {
                    progressForm.UpdateProgress(95);
                }
            }
            catch (Exception ex)
            {
                // 记录异常信息
                System.Diagnostics.Debug.WriteLine($"应用浮雕滤镜时发生错误: {ex.Message}");
                
                // 如果有进度窗体，更新错误消息
                if (progressForm != null)
                {
                    progressForm.UpdateMessage($"应用浮雕滤镜时发生错误: {ex.Message}");
                    progressForm.UpdateProgress(100); // 确保进度条完成
                }
                
                // 重新抛出异常，让上层处理
                throw;
            }
        }

        // 老照片滤镜
        private static void ApplyVintage(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用老照片效果...");
                progressForm.UpdateProgress(10);
            }

            float intensityFactor = intensity / 100f;
            Bitmap bmp = new Bitmap(image);
            int height = bmp.Height;

            // 创建老照片效果
            for (int y = 0; y < height; y++)
            {
                // 更新进度条
                if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                {
                    int progressPercentage = 10 + (int)((float)y / height * 70); // 10%-80%的进度用于颜色处理
                    progressForm.UpdateProgress(progressPercentage);
                }

                for (int x = 0; x < bmp.Width; x++)
                {
                    Color c = bmp.GetPixel(x, y);

                    // 降低饱和度
                    int gray = (int)(c.R * 0.3 + c.G * 0.59 + c.B * 0.11);

                    // 添加黄褐色调
                    int r = Math.Min(255, (int)(gray * 1.0 + 40 * intensityFactor));
                    int g = Math.Min(255, (int)(gray * 0.9 + 20 * intensityFactor));
                    int b = Math.Min(255, (int)(gray * 0.7));

                    // 降低对比度
                    r = (int)(r * 0.8 + 30);
                    g = (int)(g * 0.8 + 30);
                    b = (int)(b * 0.8 + 30);

                    // 根据强度混合原始颜色和老照片效果
                    int finalR = (int)(r * intensityFactor + c.R * (1 - intensityFactor));
                    int finalG = (int)(g * intensityFactor + c.G * (1 - intensityFactor));
                    int finalB = (int)(b * intensityFactor + c.B * (1 - intensityFactor));

                    result.SetPixel(x, y, Color.FromArgb(c.A, finalR, finalG, finalB));
                }
            }

            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在添加噪点效果...");
                progressForm.UpdateProgress(80);
            }

            // 添加噪点
            AddNoise(result, (int)(intensityFactor * 20), progressForm);
        }

        // 黑白滤镜
        private static void ApplyBlackAndWhite(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用黑白效果...");
                progressForm.UpdateProgress(10);
            }

            float threshold = 128 + (intensity - 50) * 1.5f;
            Bitmap bmp = new Bitmap(image);
            int height = bmp.Height;

            for (int y = 0; y < height; y++)
            {
                // 更新进度条
                if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                {
                    int progressPercentage = 10 + (int)((float)y / height * 90); // 10%-100%的进度
                    progressForm.UpdateProgress(progressPercentage);
                }

                for (int x = 0; x < bmp.Width; x++)
                {
                    Color c = bmp.GetPixel(x, y);
                    int gray = (int)(c.R * 0.3 + c.G * 0.59 + c.B * 0.11);
                    int value = (gray > threshold) ? 255 : 0;
                    result.SetPixel(x, y, Color.FromArgb(c.A, value, value, value));
                }
            }

            // 完成处理，更新进度到100%
            if (progressForm != null)
            {
                progressForm.UpdateProgress(100);
            }
        }

        // 暗角滤镜
        private static void ApplyVignette(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用暗角效果...");
                progressForm.UpdateProgress(10);
            }

            float intensityFactor = intensity / 100f;
            Bitmap bmp = new Bitmap(image);
            int height = bmp.Height;

            // 计算中心点和最大距离
            float centerX = bmp.Width / 2f;
            float centerY = height / 2f;
            float maxDistance = (float)Math.Sqrt(centerX * centerX + centerY * centerY);

            for (int y = 0; y < height; y++)
            {
                // 更新进度条
                if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                {
                    int progressPercentage = 10 + (int)((float)y / height * 90); // 10%-100%的进度
                    progressForm.UpdateProgress(progressPercentage);
                }

                for (int x = 0; x < bmp.Width; x++)
                {
                    Color c = bmp.GetPixel(x, y);

                    // 计算到中心的距离
                    float distance = (float)Math.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
                    float ratio = distance / maxDistance;

                    // 计算暗角效果
                    float vignetteAmount = 1.0f - (ratio * intensityFactor * 1.5f);
                    vignetteAmount = Math.Max(0, vignetteAmount);

                    // 应用暗角
                    int r = (int)(c.R * vignetteAmount);
                    int g = (int)(c.G * vignetteAmount);
                    int b = (int)(c.B * vignetteAmount);

                    result.SetPixel(x, y, Color.FromArgb(c.A, r, g, b));
                }
            }

            // 完成处理，更新进度到100%
            if (progressForm != null)
            {
                progressForm.UpdateProgress(100);
            }
        }

        // 像素化滤镜
        private static void ApplyPixelate(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用像素化效果...");
                progressForm.UpdateProgress(10);
            }

            // 根据强度确定像素块大小
            int pixelSize = Math.Max(2, 20 - (intensity / 5));
            Bitmap bmp = new Bitmap(image);

            int width = bmp.Width;
            int height = bmp.Height;

            // 使用LockBits直接访问像素数据以提高性能
            System.Drawing.Imaging.BitmapData sourceData = bmp.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.ReadOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            System.Drawing.Imaging.BitmapData resultData = result.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            try
            {
                int stride = sourceData.Stride;
                int bytes = Math.Abs(stride) * height;
                byte[] pixelBuffer = new byte[bytes];
                byte[] resultBuffer = new byte[bytes];

                // 复制源位图数据到缓冲区
                System.Runtime.InteropServices.Marshal.Copy(sourceData.Scan0, pixelBuffer, 0, bytes);

                // 设置进度条为Marquee样式（不确定进度）
                if (progressForm != null)
                {
                    // 使用Marquee样式，不需要更新具体进度值
                    progressForm.SetMarqueeStyle();
                    
                    // 不再需要定时器来更新进度，减少资源冲突
                    // 但仍然保留消息更新的功能
                }

                // 并行处理以提高性能
                System.Threading.Tasks.Parallel.For(0, height, y =>
                {
                    // 按像素块大小处理
                    if (y % pixelSize == 0)
                    {
                        for (int x = 0; x < width; x += pixelSize)
                        {
                            // 获取像素块的平均颜色
                            int avgR = 0, avgG = 0, avgB = 0, avgA = 0;
                            int count = 0;

                            // 计算像素块内的平均颜色
                            for (int j = 0; j < pixelSize && y + j < height; j++)
                            {
                                for (int i = 0; i < pixelSize && x + i < width; i++)
                                {
                                    int pixelOffset = (y + j) * stride + (x + i) * 4;
                                    avgB += pixelBuffer[pixelOffset];
                                    avgG += pixelBuffer[pixelOffset + 1];
                                    avgR += pixelBuffer[pixelOffset + 2];
                                    avgA += pixelBuffer[pixelOffset + 3];
                                    count++;
                                }
                            }

                            if (count > 0)
                            {
                                // 计算平均值
                                avgB /= count;
                                avgG /= count;
                                avgR /= count;
                                avgA /= count;

                                // 将平均颜色应用到整个像素块
                                for (int j = 0; j < pixelSize && y + j < height; j++)
                                {
                                    for (int i = 0; i < pixelSize && x + i < width; i++)
                                    {
                                        int pixelOffset = (y + j) * stride + (x + i) * 4;
                                        resultBuffer[pixelOffset] = (byte)avgB;
                                        resultBuffer[pixelOffset + 1] = (byte)avgG;
                                        resultBuffer[pixelOffset + 2] = (byte)avgR;
                                        resultBuffer[pixelOffset + 3] = (byte)avgA;
                                    }
                                }
                            }
                        }
                    }

                    // 使用Marquee样式时不需要更新进度计数器
                    // 移除进度计数逻辑，减少锁竞争和资源消耗
                    // 进度更新将在处理完成后一次性完成
                });

                // 复制处理后的数据回结果位图
                System.Runtime.InteropServices.Marshal.Copy(resultBuffer, 0, resultData.Scan0, bytes);
                
                // 完成处理，将进度条改回连续样式并更新进度到100%
                if (progressForm != null)
                {
                    // 将进度条从Marquee样式改回连续样式
                    progressForm.SetContinuousStyle(0, 100);
                    // 更新进度到100%
                    progressForm.UpdateProgress(100);
                }
            }
            finally
            {
                // 解锁位图
                bmp.UnlockBits(sourceData);
                result.UnlockBits(resultData);
            }
        }

        // 油画滤镜
        private static void ApplyOilPainting(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用油画效果...");
                progressForm.UpdateProgress(10);
            }
            // 根据强度确定半径和级别
            int radius = Math.Max(1, intensity / 20);
            int levels = 10;
            Bitmap bmp = new Bitmap(image);

            int width = bmp.Width;
            int height = bmp.Height;

            // 使用LockBits直接访问像素数据以提高性能
            System.Drawing.Imaging.BitmapData sourceData = bmp.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.ReadOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            System.Drawing.Imaging.BitmapData resultData = result.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            try
            {
                int stride = sourceData.Stride;
                int bytes = Math.Abs(stride) * height;
                byte[] pixelBuffer = new byte[bytes];
                byte[] resultBuffer = new byte[bytes];

                // 复制源位图数据到缓冲区
                System.Runtime.InteropServices.Marshal.Copy(sourceData.Scan0, pixelBuffer, 0, bytes);

                // 设置进度条为Marquee样式（不确定进度）
                if (progressForm != null)
                {
                    // 使用Marquee样式，不需要更新具体进度值
                    progressForm.SetMarqueeStyle();
                    
                    // 不再需要定时器来更新进度，减少资源冲突
                    // 但仍然保留消息更新的功能
                }

                // 并行处理以提高性能
                System.Threading.Tasks.Parallel.For(0, height, y =>
                {
                    for (int x = 0; x < width; x++)
                    {
                        // 创建直方图数组
                        int[] intensityCount = new int[levels];
                        int[] redAverage = new int[levels];
                        int[] greenAverage = new int[levels];
                        int[] blueAverage = new int[levels];

                        // 遍历邻域像素
                        for (int j = -radius; j <= radius; j++)
                        {
                            for (int i = -radius; i <= radius; i++)
                            {
                                int pixelX = Math.Min(Math.Max(x + i, 0), width - 1);
                                int pixelY = Math.Min(Math.Max(y + j, 0), height - 1);
                                int currentPixelOffset = pixelY * stride + pixelX * 4;

                                byte b = pixelBuffer[currentPixelOffset];
                                byte g = pixelBuffer[currentPixelOffset + 1];
                                byte r = pixelBuffer[currentPixelOffset + 2];

                                // 计算强度级别
                                int intensityLevel = (int)((r + g + b) / 3.0 * levels / 255);
                                if (intensityLevel >= levels) intensityLevel = levels - 1;

                                intensityCount[intensityLevel]++;
                                redAverage[intensityLevel] += r;
                                greenAverage[intensityLevel] += g;
                                blueAverage[intensityLevel] += b;
                            }
                        }

                        // 找到最大强度级别
                        int maxIndex = 0;
                        for (int i = 1; i < levels; i++)
                        {
                            if (intensityCount[i] > intensityCount[maxIndex])
                                maxIndex = i;
                        }

                        // 计算平均颜色
                        int pixelOffset = y * stride + x * 4;
                        if (intensityCount[maxIndex] > 0)
                        {
                            resultBuffer[pixelOffset] = (byte)(blueAverage[maxIndex] / intensityCount[maxIndex]);     // Blue
                            resultBuffer[pixelOffset + 1] = (byte)(greenAverage[maxIndex] / intensityCount[maxIndex]);   // Green
                            resultBuffer[pixelOffset + 2] = (byte)(redAverage[maxIndex] / intensityCount[maxIndex]);     // Red
                            resultBuffer[pixelOffset + 3] = 255;                                                         // Alpha
                        }
                        else
                        {
                            // 防止除零错误
                            resultBuffer[pixelOffset] = 0;     // Blue
                            resultBuffer[pixelOffset + 1] = 0; // Green
                            resultBuffer[pixelOffset + 2] = 0; // Red
                            resultBuffer[pixelOffset + 3] = 255; // Alpha
                        }
                    }
                });

                // 复制处理后的数据回结果位图
                System.Runtime.InteropServices.Marshal.Copy(resultBuffer, 0, resultData.Scan0, bytes);
                
                // 完成处理，将进度条改回连续样式并更新进度到100%
                if (progressForm != null)
                {
                    // 将进度条从Marquee样式改回连续样式
                    progressForm.SetContinuousStyle(0, 100);
                    // 更新进度到100%
                    progressForm.UpdateProgress(100);
                }
            }
            finally
            {
                // 解锁位图
                bmp.UnlockBits(sourceData);
                result.UnlockBits(resultData);
            }
        }

        // 海报化滤镜
        private static void ApplyPosterize(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用海报化效果...");
                progressForm.UpdateProgress(10);
            }
            // 根据强度确定级别数
            int levels = Math.Max(2, (intensity / 10) + 2);
            Bitmap bmp = new Bitmap(image);

            int width = bmp.Width;
            int height = bmp.Height;

            // 使用LockBits直接访问像素数据以提高性能
            System.Drawing.Imaging.BitmapData sourceData = bmp.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.ReadOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            System.Drawing.Imaging.BitmapData resultData = result.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            try
            {
                int stride = sourceData.Stride;
                int bytes = Math.Abs(stride) * height;
                byte[] pixelBuffer = new byte[bytes];
                byte[] resultBuffer = new byte[bytes];

                // 复制源位图数据到缓冲区
                System.Runtime.InteropServices.Marshal.Copy(sourceData.Scan0, pixelBuffer, 0, bytes);

                // 设置进度条为Marquee样式（不确定进度）
                if (progressForm != null)
                {
                    // 使用Marquee样式，不需要更新具体进度值
                    progressForm.SetMarqueeStyle();
                    
                    // 不再需要定时器来更新进度，减少资源冲突
                    // 但仍然保留消息更新的功能
                }

                // 并行处理以提高性能
                System.Threading.Tasks.Parallel.For(0, height, y =>
                {
                    for (int x = 0; x < width; x++)
                    {
                        int pixelOffset = y * stride + x * 4;
                        
                        byte b = pixelBuffer[pixelOffset];
                        byte g = pixelBuffer[pixelOffset + 1];
                        byte r = pixelBuffer[pixelOffset + 2];
                        byte a = pixelBuffer[pixelOffset + 3];

                        // 将每个颜色通道量化为指定级别
                        byte newR = (byte)Math.Min(255, ((r * levels) / 255) * (255 / (levels - 1)));
                        byte newG = (byte)Math.Min(255, ((g * levels) / 255) * (255 / (levels - 1)));
                        byte newB = (byte)Math.Min(255, ((b * levels) / 255) * (255 / (levels - 1)));

                        resultBuffer[pixelOffset] = newB;
                        resultBuffer[pixelOffset + 1] = newG;
                        resultBuffer[pixelOffset + 2] = newR;
                        resultBuffer[pixelOffset + 3] = a;
                    }
                });

                // 复制处理后的数据回结果位图
                System.Runtime.InteropServices.Marshal.Copy(resultBuffer, 0, resultData.Scan0, bytes);
                
                // 完成处理，将进度条改回连续样式并更新进度到100%
                if (progressForm != null)
                {
                    // 将进度条从Marquee样式改回连续样式
                    progressForm.SetContinuousStyle(0, 100);
                    // 更新进度到100%
                    progressForm.UpdateProgress(100);
                }
            }
            finally
            {
                // 解锁位图
                bmp.UnlockBits(sourceData);
                result.UnlockBits(resultData);
            }
        }

        // 曝光滤镜
        private static void ApplySolarize(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用曝光效果...");
                progressForm.UpdateProgress(10);
            }
            // 根据强度确定阈值
            int threshold = 128 + (intensity - 50) * 2;
            threshold = Math.Min(255, Math.Max(0, threshold));
            Bitmap bmp = new Bitmap(image);

            int width = bmp.Width;
            int height = bmp.Height;

            // 使用LockBits直接访问像素数据以提高性能
            System.Drawing.Imaging.BitmapData sourceData = bmp.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.ReadOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            System.Drawing.Imaging.BitmapData resultData = result.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            try
            {
                int stride = sourceData.Stride;
                int bytes = Math.Abs(stride) * height;
                byte[] pixelBuffer = new byte[bytes];
                byte[] resultBuffer = new byte[bytes];

                // 复制源位图数据到缓冲区
                System.Runtime.InteropServices.Marshal.Copy(sourceData.Scan0, pixelBuffer, 0, bytes);

                // 设置进度条为Marquee样式（不确定进度）
                if (progressForm != null)
                {
                    // 使用Marquee样式，不需要更新具体进度值
                    progressForm.SetMarqueeStyle();
                    
                    // 不再需要定时器来更新进度，减少资源冲突
                    // 但仍然保留消息更新的功能
                }

                // 并行处理以提高性能
                System.Threading.Tasks.Parallel.For(0, height, y =>
                {
                    for (int x = 0; x < width; x++)
                    {
                        int pixelOffset = y * stride + x * 4;
                        
                        byte b = pixelBuffer[pixelOffset];
                        byte g = pixelBuffer[pixelOffset + 1];
                        byte r = pixelBuffer[pixelOffset + 2];
                        byte a = pixelBuffer[pixelOffset + 3];

                        // 应用曝光效果
                        byte newR = (byte)((r > threshold) ? 255 - r : r);
                        byte newG = (byte)((g > threshold) ? 255 - g : g);
                        byte newB = (byte)((b > threshold) ? 255 - b : b);

                        resultBuffer[pixelOffset] = newB;
                        resultBuffer[pixelOffset + 1] = newG;
                        resultBuffer[pixelOffset + 2] = newR;
                        resultBuffer[pixelOffset + 3] = a;
                    }
                });

                // 复制处理后的数据回结果位图
                System.Runtime.InteropServices.Marshal.Copy(resultBuffer, 0, resultData.Scan0, bytes);
                
                // 完成处理，将进度条改回连续样式并更新进度到100%
                if (progressForm != null)
                {
                    // 将进度条从Marquee样式改回连续样式
                    progressForm.SetContinuousStyle(0, 100);
                    // 更新进度到100%
                    progressForm.UpdateProgress(100);
                }
            }
            finally
            {
                // 解锁位图
                bmp.UnlockBits(sourceData);
                result.UnlockBits(resultData);
            }
        }

        // 霓虹滤镜
        private static void ApplyNeon(Image image, Bitmap result, int intensity, ProgressForm progressForm = null)
        {
            // 更新进度消息
            if (progressForm != null)
            {
                progressForm.UpdateMessage("正在应用霓虹效果...");
                progressForm.UpdateProgress(10);
            }
            float intensityFactor = intensity / 100f;
            Bitmap bmp = new Bitmap(image);
            int width = bmp.Width;
            int height = bmp.Height;

            // 先应用边缘检测
            Bitmap edgeImage = new Bitmap(width, height);
            ApplyEdgeDetection(bmp, edgeImage, 100);

            // 使用LockBits直接访问像素数据以提高性能
            System.Drawing.Imaging.BitmapData sourceData = bmp.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.ReadOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            System.Drawing.Imaging.BitmapData edgeData = edgeImage.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.ReadOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            System.Drawing.Imaging.BitmapData resultData = result.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            try
            {
                int stride = sourceData.Stride;
                int bytes = Math.Abs(stride) * height;
                byte[] sourceBuffer = new byte[bytes];
                byte[] edgeBuffer = new byte[bytes];
                byte[] resultBuffer = new byte[bytes];

                // 复制源位图和边缘图像数据到缓冲区
                System.Runtime.InteropServices.Marshal.Copy(sourceData.Scan0, sourceBuffer, 0, bytes);
                System.Runtime.InteropServices.Marshal.Copy(edgeData.Scan0, edgeBuffer, 0, bytes);

                // 设置进度条为Marquee样式（不确定进度）
                if (progressForm != null)
                {
                    // 使用Marquee样式，不需要更新具体进度值
                    progressForm.SetMarqueeStyle();
                    
                    // 不再需要定时器来更新进度，减少资源冲突
                    // 但仍然保留消息更新的功能
                }

                // 并行处理以提高性能
                System.Threading.Tasks.Parallel.For(0, height, y =>
                {
                    for (int x = 0; x < width; x++)
                    {
                        int pixelOffset = y * stride + x * 4;
                        
                        byte b = sourceBuffer[pixelOffset];
                        byte g = sourceBuffer[pixelOffset + 1];
                        byte r = sourceBuffer[pixelOffset + 2];
                        byte a = sourceBuffer[pixelOffset + 3];

                        // 获取边缘强度
                        byte edgeB = edgeBuffer[pixelOffset];
                        byte edgeG = edgeBuffer[pixelOffset + 1];
                        byte edgeR = edgeBuffer[pixelOffset + 2];
                        int edgeIntensity = (edgeR + edgeG + edgeB) / 3;

                        // 增强饱和度和亮度
                        float[] hsv = RGBtoHSV(r, g, b);
                        hsv[1] = Math.Min(1.0f, hsv[1] + 0.5f * intensityFactor); // 增加饱和度
                        hsv[2] = Math.Min(1.0f, hsv[2] + 0.3f * intensityFactor); // 增加亮度

                        int[] rgb = HSVtoRGB(hsv[0], hsv[1], hsv[2]);

                        // 根据边缘强度混合原始颜色和增强颜色
                        float blendFactor = edgeIntensity / 255.0f * intensityFactor;
                        byte newR = (byte)(rgb[0] * blendFactor + r * (1 - blendFactor));
                        byte newG = (byte)(rgb[1] * blendFactor + g * (1 - blendFactor));
                        byte newB = (byte)(rgb[2] * blendFactor + b * (1 - blendFactor));

                        resultBuffer[pixelOffset] = newB;
                        resultBuffer[pixelOffset + 1] = newG;
                        resultBuffer[pixelOffset + 2] = newR;
                        resultBuffer[pixelOffset + 3] = a;
                    }
                });

                // 复制处理后的数据回结果位图
                System.Runtime.InteropServices.Marshal.Copy(resultBuffer, 0, resultData.Scan0, bytes);
                
                // 完成处理，将进度条改回连续样式并更新进度到100%
                if (progressForm != null)
                {
                    // 将进度条从Marquee样式改回连续样式
                    progressForm.SetContinuousStyle(0, 100);
                    // 更新进度到100%
                    progressForm.UpdateProgress(100);
                }
            }
            finally
            {
                // 解锁位图
                bmp.UnlockBits(sourceData);
                edgeImage.UnlockBits(edgeData);
                result.UnlockBits(resultData);
                
                // 释放临时图像
                edgeImage.Dispose();
            }
        }

        // 辅助方法：应用卷积
        private static void ApplyConvolution(Bitmap source, Bitmap result, float[,] kernel, int offset = 0)
        {
            // 调用带进度条参数的重载方法，不传递进度表单
            ApplyConvolution(source, result, kernel, progressForm: null, 0, 100, offset);
        }

        // 辅助方法：应用卷积（带进度条）
        private static void ApplyConvolution(Bitmap source, Bitmap result, float[,] kernel, ProgressForm progressForm = null, int startProgress = 0, int endProgress = 100, int offset = 0)
        {
            int width = source.Width;
            int height = source.Height;
            int kernelSize = kernel.GetLength(0);
            int radius = kernelSize / 2;

            // 使用LockBits直接访问像素数据以提高性能
            System.Drawing.Imaging.BitmapData sourceData = source.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.ReadOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            System.Drawing.Imaging.BitmapData resultData = result.LockBits(
                new Rectangle(0, 0, width, height),
                System.Drawing.Imaging.ImageLockMode.WriteOnly,
                System.Drawing.Imaging.PixelFormat.Format32bppArgb);

            // 使用Marquee样式时不需要进度计数器
            // 计算进度范围（仅用于最终进度更新）
            int progressRange = endProgress - startProgress;

            // 创建一个定时器来更新进度，而不是在并行循环中更新
            System.Threading.Timer progressTimer = null;
            try
            {
                int stride = sourceData.Stride;
                int bytes = Math.Abs(stride) * height;
                byte[] pixelBuffer = new byte[bytes];
                byte[] resultBuffer = new byte[bytes];

                // 复制源位图数据到缓冲区
                System.Runtime.InteropServices.Marshal.Copy(sourceData.Scan0, pixelBuffer, 0, bytes);

                // 设置进度条为Marquee样式（不确定进度）
                if (progressForm != null)
                {
                    // 使用Marquee样式，不需要更新具体进度值
                    progressForm.SetMarqueeStyle();
                    
                    // 不再需要定时器来更新进度，减少资源冲突
                    // 但仍然保留消息更新的功能
                    progressTimer = new System.Threading.Timer((state) =>
                    {
                        try
                        {
                            // 只更新消息，表明处理仍在进行中
                            progressForm.UpdateMessage("正在处理图像...");
                        }
                        catch
                        {
                            // 忽略定时器回调中的异常，防止崩溃
                        }
                    }, null, 0, 1000); // 每1000毫秒（1秒）更新一次消息
                }

                // 并行处理以提高性能
                System.Threading.Tasks.Parallel.For(0, height, y =>
                {
                    // 使用Marquee样式时不需要更新进度计数器
                    // 移除进度计数逻辑，减少锁竞争和资源消耗

                    for (int x = 0; x < width; x++)
                    {
                        float r = 0, g = 0, b = 0;
                        int pixelOffset = y * stride + x * 4;

                        // 应用卷积核
                        for (int ky = 0; ky < kernelSize; ky++)
                        {
                            for (int kx = 0; kx < kernelSize; kx++)
                            {
                                int pixelX = Math.Min(Math.Max(x + kx - radius, 0), width - 1);
                                int pixelY = Math.Min(Math.Max(y + ky - radius, 0), height - 1);
                                int currentPixelOffset = pixelY * stride + pixelX * 4;

                                b += pixelBuffer[currentPixelOffset] * kernel[ky, kx];
                                g += pixelBuffer[currentPixelOffset + 1] * kernel[ky, kx];
                                r += pixelBuffer[currentPixelOffset + 2] * kernel[ky, kx];
                            }
                        }

                        // 添加偏移并限制在0-255范围内
                        b = Math.Min(Math.Max(b + offset, 0), 255);
                        g = Math.Min(Math.Max(g + offset, 0), 255);
                        r = Math.Min(Math.Max(r + offset, 0), 255);

                        // 设置结果像素
                        resultBuffer[pixelOffset] = (byte)b;     // Blue
                        resultBuffer[pixelOffset + 1] = (byte)g; // Green
                        resultBuffer[pixelOffset + 2] = (byte)r; // Red
                        resultBuffer[pixelOffset + 3] = 255;     // Alpha
                    }
                });

                // 复制处理后的数据回结果位图
                System.Runtime.InteropServices.Marshal.Copy(resultBuffer, 0, resultData.Scan0, bytes);
                
                // 完成处理，将进度条改回连续样式并更新进度到结束值
                if (progressForm != null)
                {
                    // 将进度条从Marquee样式改回连续样式
                    progressForm.SetContinuousStyle(0, 100);
                    // 更新进度到结束值
                    progressForm.UpdateProgress(endProgress);
                }
            }
            finally
            {
                // 停止并释放定时器
                if (progressTimer != null)
                {
                    try
                    {
                        progressTimer.Change(System.Threading.Timeout.Infinite, System.Threading.Timeout.Infinite);
                        progressTimer.Dispose();
                        progressTimer = null;
                    }
                    catch
                    {
                        // 忽略定时器释放过程中的异常
                    }
                }
                
                // 解锁位图
                source.UnlockBits(sourceData);
                result.UnlockBits(resultData);
            }
        }

        // 辅助方法：添加噪点
        private static void AddNoise(Bitmap image, int amount, ProgressForm progressForm = null)
        {
            Random random = new Random();
            int height = image.Height;

            for (int y = 0; y < height; y++)
            {
                // 更新进度条
                if (progressForm != null && y % 10 == 0) // 每10行更新一次进度，避免过于频繁的UI更新
                {
                    int progressPercentage = (int)((float)y / height * 100);
                    progressForm.UpdateProgress(progressPercentage);
                }

                for (int x = 0; x < image.Width; x++)
                {
                    if (random.Next(100) < amount)
                    {
                        Color c = image.GetPixel(x, y);
                        int noise = random.Next(-50, 50);
                        int r = Math.Min(Math.Max(c.R + noise, 0), 255);
                        int g = Math.Min(Math.Max(c.G + noise, 0), 255);
                        int b = Math.Min(Math.Max(c.B + noise, 0), 255);
                        image.SetPixel(x, y, Color.FromArgb(c.A, r, g, b));
                    }
                }
            }

            // 完成处理，更新进度到100%
            if (progressForm != null)
            {
                progressForm.UpdateProgress(100);
            }
        }

        // 辅助方法：RGB转HSV
        private static float[] RGBtoHSV(int r, int g, int b)
        {
            float[] hsv = new float[3];
            float rf = r / 255f;
            float gf = g / 255f;
            float bf = b / 255f;

            float max = Math.Max(rf, Math.Max(gf, bf));
            float min = Math.Min(rf, Math.Min(gf, bf));
            float delta = max - min;

            // 色调
            if (delta == 0)
            {
                hsv[0] = 0; // 无色调
            }
            else if (max == rf)
            {
                hsv[0] = ((gf - bf) / delta) % 6;
            }
            else if (max == gf)
            {
                hsv[0] = (bf - rf) / delta + 2;
            }
            else
            {
                hsv[0] = (rf - gf) / delta + 4;
            }

            hsv[0] *= 60;
            if (hsv[0] < 0) hsv[0] += 360;

            // 饱和度
            hsv[1] = (max == 0) ? 0 : delta / max;

            // 亮度
            hsv[2] = max;

            return hsv;
        }

        // 辅助方法：HSV转RGB
        private static int[] HSVtoRGB(float h, float s, float v)
        {
            int[] rgb = new int[3];
            float c = v * s;
            float x = c * (1 - Math.Abs((h / 60) % 2 - 1));
            float m = v - c;

            float rf, gf, bf;
            if (h < 60)
            {
                rf = c; gf = x; bf = 0;
            }
            else if (h < 120)
            {
                rf = x; gf = c; bf = 0;
            }
            else if (h < 180)
            {
                rf = 0; gf = c; bf = x;
            }
            else if (h < 240)
            {
                rf = 0; gf = x; bf = c;
            }
            else if (h < 300)
            {
                rf = x; gf = 0; bf = c;
            }
            else
            {
                rf = c; gf = 0; bf = x;
            }

            rgb[0] = (int)((rf + m) * 255);
            rgb[1] = (int)((gf + m) * 255);
            rgb[2] = (int)((bf + m) * 255);

            return rgb;
        }
    }
}