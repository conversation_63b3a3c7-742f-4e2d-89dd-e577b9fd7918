namespace PictureMagic
{
    public partial class DrawingDialog : Form
    {
        private DrawingTool drawingTool;
        private Image originalImage;
        
        // 添加ProgressForm成员变量
        private ProgressForm progressForm = null;

        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public Image ResultImage { get; private set; } = null!; // 使用null抑制操作符

        public DrawingDialog(Image image)
        {
            InitializeComponent();
            originalImage = image;

            // 初始化绘图工具
            drawingTool = new DrawingTool(pictureBoxPreview, originalImage);
            drawingTool.DrawingCompleted += DrawingTool_DrawingCompleted;
            drawingTool.HistoryStateChanged += DrawingTool_HistoryStateChanged;

            // 初始化工具下拉列表
            comboBoxTool.Items.Add("铅笔");
            comboBoxTool.Items.Add("画笔");
            comboBoxTool.Items.Add("直线");
            comboBoxTool.Items.Add("矩形");
            comboBoxTool.Items.Add("椭圆");
            comboBoxTool.Items.Add("橡皮擦");
            comboBoxTool.SelectedIndex = 0;

            // 初始化大小下拉列表
            for (int i = 1; i <= 20; i++)
            {
                comboBoxSize.Items.Add(i);
            }
            comboBoxSize.SelectedIndex = 2; // 默认大小为3

            // 设置颜色按钮的背景色
            buttonColor.BackColor = Color.Black;
            
            // 初始化撤销和重做按钮状态
            UpdateUndoRedoButtons();
            
            // 添加键盘事件处理
            this.KeyPreview = true;
            this.KeyDown += DrawingDialog_KeyDown;
        }
        
        // 历史状态变更事件处理
        private void DrawingTool_HistoryStateChanged(object? sender, EventArgs e)
        {
            UpdateUndoRedoButtons();
        }

        // 键盘事件处理
        private void DrawingDialog_KeyDown(object sender, KeyEventArgs e)
        {
            // 处理Ctrl+Z撤销
            if (e.Control && e.KeyCode == Keys.Z)
            {
                if (buttonUndo.Enabled)
                {
                    buttonUndo_Click(sender, e);
                    e.Handled = true;
                }
            }
            // 处理Ctrl+Y重做
            else if (e.Control && e.KeyCode == Keys.Y)
            {
                if (buttonRedo.Enabled)
                {
                    buttonRedo_Click(sender, e);
                    e.Handled = true;
                }
            }
        }
        
        // 更新撤销和重做按钮状态
        private void UpdateUndoRedoButtons()
        {
            buttonUndo.Enabled = drawingTool.CanUndo();
            buttonRedo.Enabled = drawingTool.CanRedo();
        }

        // 绘图工具完成事件处理
        private void DrawingTool_DrawingCompleted(object sender, DrawingCompletedEventArgs e)
        {
            ResultImage = e.ResultImage;
            UpdateUndoRedoButtons();
        }

        // 获取结果图像
        public Image GetResultImage()
        {
            return ResultImage;
        }

        // 工具选择改变事件
        private void comboBoxTool_SelectedIndexChanged(object sender, EventArgs e)
        {
            switch (comboBoxTool.SelectedIndex)
            {
                case 0: // 铅笔
                    drawingTool.SetToolType(DrawingTool.ToolType.Pencil);
                    break;
                case 1: // 画笔
                    drawingTool.SetToolType(DrawingTool.ToolType.Brush);
                    break;
                case 2: // 直线
                    drawingTool.SetToolType(DrawingTool.ToolType.Line);
                    break;
                case 3: // 矩形
                    drawingTool.SetToolType(DrawingTool.ToolType.Rectangle);
                    break;
                case 4: // 椭圆
                    drawingTool.SetToolType(DrawingTool.ToolType.Ellipse);
                    break;
                case 5: // 橡皮擦
                    drawingTool.SetToolType(DrawingTool.ToolType.Eraser);
                    break;
            }
        }

        // 颜色选择按钮点击事件
        private void buttonColor_Click(object sender, EventArgs e)
        {
            ColorDialog colorDialog = new ColorDialog();
            colorDialog.Color = buttonColor.BackColor;
            colorDialog.FullOpen = true;

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                buttonColor.BackColor = colorDialog.Color;
                drawingTool.SetColor(colorDialog.Color);
            }
        }

        // 大小选择改变事件
        private void comboBoxSize_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (comboBoxSize.SelectedIndex >= 0)
            {
                int size = comboBoxSize.SelectedIndex + 1;
                drawingTool.SetSize(size);
            }
        }

        // 撤销按钮点击事件
        private void buttonUndo_Click(object sender, EventArgs e)
        {
            drawingTool.Undo();
            UpdateUndoRedoButtons();
        }

        // 重做按钮点击事件
        private void buttonRedo_Click(object sender, EventArgs e)
        {
            drawingTool.Redo();
            UpdateUndoRedoButtons();
        }

        // 清除按钮点击事件
        private void buttonClear_Click(object sender, EventArgs e)
        {
            drawingTool.Clear();
            UpdateUndoRedoButtons();
        }

        // 添加显示进度的方法
        private void ShowProgress(string message = "正在处理...")
        {
            // 使用独立的进度窗体显示进度
            if (progressForm == null || progressForm.IsDisposed)
            {
                progressForm = ProgressForm.ShowProgressDialog(this, message, true); // 使用遮罩效果
            }
            else
            {
                progressForm.UpdateMessage(message);
            }
            Application.DoEvents(); // 强制刷新UI
        }

        // 添加隐藏进度的方法
        private void HideProgress()
        {
            // 关闭进度窗体
            if (progressForm != null && !progressForm.IsDisposed)
            {
                progressForm.CloseProgress();
                progressForm = null;
            }
        }

        // 确定按钮点击事件
        private void buttonOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示进度提示
                ShowProgress("正在完成绘图...");
                
                drawingTool.FinishDrawing();
                DialogResult = DialogResult.OK;
            }
            finally
            {
                // 隐藏进度提示
                HideProgress();
            }
        }

        // 取消按钮点击事件
        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        // 窗体关闭事件
        private void DrawingDialog_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 确保关闭进度窗体
            HideProgress();
            
            // 取消订阅事件
            drawingTool.DrawingCompleted -= DrawingTool_DrawingCompleted;
            drawingTool.HistoryStateChanged -= DrawingTool_HistoryStateChanged;
            
            // 释放绘图工具资源
            drawingTool.Dispose();
        }
    }
}