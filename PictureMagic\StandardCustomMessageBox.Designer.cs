namespace PictureMagic
{
    partial class StandardCustomMessageBox
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            panelButtons = new Panel();
            btnCustom = new Button();
            btnCancel = new Button();
            btnOK = new Button();
            pictureBoxIcon = new PictureBox();
            txtMessages = new TextBox();
            panelButtons.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxIcon).BeginInit();
            SuspendLayout();
            // 
            // panelButtons
            // 
            panelButtons.Controls.Add(btnCustom);
            panelButtons.Controls.Add(btnCancel);
            panelButtons.Controls.Add(btnOK);
            panelButtons.Dock = DockStyle.Bottom;
            panelButtons.Location = new Point(0, 136);
            panelButtons.Name = "panelButtons";
            panelButtons.Padding = new Padding(10, 0, 10, 0);
            panelButtons.Size = new Size(468, 60);
            panelButtons.TabIndex = 1;
            // 
            // btnCustom
            // 
            btnCustom.AutoSize = true;
            btnCustom.Location = new Point(38, 15);
            btnCustom.Name = "btnCustom";
            btnCustom.Size = new Size(90, 30);
            btnCustom.TabIndex = 2;
            btnCustom.Text = "自定义";
            btnCustom.UseVisualStyleBackColor = true;
            btnCustom.Visible = false;
            btnCustom.Click += btnCustom_Click;
            // 
            // btnCancel
            // 
            btnCancel.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnCancel.DialogResult = DialogResult.Cancel;
            btnCancel.Location = new Point(359, 15);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(90, 30);
            btnCancel.TabIndex = 1;
            btnCancel.Text = "取消";
            btnCancel.UseVisualStyleBackColor = true;
            btnCancel.Click += btnCancel_Click;
            // 
            // btnOK
            // 
            btnOK.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            btnOK.DialogResult = DialogResult.OK;
            btnOK.Location = new Point(259, 15);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(90, 30);
            btnOK.TabIndex = 0;
            btnOK.Text = "确定";
            btnOK.UseVisualStyleBackColor = true;
            btnOK.Click += btnOK_Click;
            // 
            // pictureBoxIcon
            // 
            pictureBoxIcon.Anchor = AnchorStyles.Left;
            pictureBoxIcon.Location = new Point(20, 26);
            pictureBoxIcon.Name = "pictureBoxIcon";
            pictureBoxIcon.Size = new Size(32, 32);
            pictureBoxIcon.SizeMode = PictureBoxSizeMode.AutoSize;
            pictureBoxIcon.TabIndex = 2;
            pictureBoxIcon.TabStop = false;
            // 
            // txtMessages
            // 
            txtMessages.BorderStyle = BorderStyle.FixedSingle;
            txtMessages.Location = new Point(86, 20);
            txtMessages.Multiline = true;
            txtMessages.Name = "txtMessages";
            txtMessages.ReadOnly = true;
            txtMessages.Size = new Size(360, 99);
            txtMessages.TabIndex = 3;
            // 
            // StandardCustomMessageBox
            // 
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            ClientSize = new Size(468, 196);
            Controls.Add(panelButtons);
            Controls.Add(txtMessages);
            Controls.Add(pictureBoxIcon);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "StandardCustomMessageBox";
            StartPosition = FormStartPosition.CenterParent;
            Text = "消息";
            panelButtons.ResumeLayout(false);
            panelButtons.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxIcon).EndInit();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion
        private System.Windows.Forms.Panel panelButtons;
        private System.Windows.Forms.Button btnOK;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnCustom;
        private System.Windows.Forms.PictureBox pictureBoxIcon;
        private TextBox txtMessages;
    }
}