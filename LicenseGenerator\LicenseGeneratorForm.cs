using CommonLib;

namespace LicenseGenerator;

public partial class LicenseGeneratorForm : Form
{
    private const string SecretKey = "PictureMagicSecretKey";
    
    public LicenseGeneratorForm()
    {
        InitializeComponent();
        this.Text = "PictureMagic 注册工具";
    }
    
    private void btnGenerate_Click(object sender, EventArgs e)
    {
        if (string.IsNullOrWhiteSpace(txtMachineCode.Text))
        {
            MessageBox.Show("请输入机器码！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }
        
        try
        {
            string machineCode = txtMachineCode.Text.Trim();
           // 生成注册码
            string licenseKey = CommonLib.LicenseGenerator.GenerateLicenseKey(machineCode, SecretKey);
            
            // 显示注册码
            txtLicenseKey.Text = licenseKey;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"生成注册码时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    
    private void btnCopy_Click(object sender, EventArgs e)
    {
        if (!string.IsNullOrEmpty(txtLicenseKey.Text))
        {
            Clipboard.SetText(txtLicenseKey.Text);
            MessageBox.Show("注册码已复制到剪贴板！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
    
    private void btnGetCurrentMachineCode_Click(object sender, EventArgs e)
    {
        try
        {
            // 获取当前机器的机器码
            string machineCode = HardwareInfo.GenerateMachineCode();
            
            // 格式化显示
            txtMachineCode.Text = FormatMachineCode(machineCode);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"获取机器码时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    
    // 格式化机器码显示
    private string FormatMachineCode(string machineCode)
    {
        if (string.IsNullOrEmpty(machineCode)) return string.Empty;
        
        // 每8个字符添加一个连字符
        var formatted = new System.Text.StringBuilder();
        for (int i = 0; i < machineCode.Length; i++)
        {
            if (i > 0 && i % 8 == 0) formatted.Append('-');
            formatted.Append(machineCode[i]);
        }
        
        return formatted.ToString();
    }
}
