using System.Text.Json;

namespace CommonLib
{
    // 定义一个静态类来处理应用程序设置的保存和加载
    public static class AppSettings
    {
        private static readonly string AppDataPath;
        private static readonly string SettingsFilePath;

        static AppSettings()
        {
            // 获取应用程序数据文件夹路径
            AppDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "PictureMagic");
            // 定义设置文件路径
            SettingsFilePath = Path.Combine(AppDataPath, "settings.json");

            // 确保应用程序数据文件夹存在
            if (!Directory.Exists(AppDataPath))
            {
                Directory.CreateDirectory(AppDataPath);
            }
        }

        public class Settings
        {
            public string LastAddImagesPath { get; set; } = string.Empty;
            public string LastBackgroundPicturePath { get; set; } = string.Empty;
            public string LastDirectory { get; set; } = string.Empty;
            public string LicenseKey { get; set; } = string.Empty;
        }

        public static Settings LoadSettings()
        {
            if (File.Exists(SettingsFilePath))
            {
                try
                {
                    string json = File.ReadAllText(SettingsFilePath);
                    return JsonSerializer.Deserialize<Settings>(json) ?? new Settings();
                }
                catch (Exception ex)
                {
                    // 记录错误或向用户显示消息
                    Console.WriteLine($"Error loading settings: {ex.Message}");
                    return new Settings();
                }
            }
            return new Settings();
        }

        public static void SaveSettings(Settings settings)
        {
            try
            {
                string json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
                File.WriteAllText(SettingsFilePath, json);
            }
            catch (Exception ex)
            {
                // 记录错误或向用户显示消息
                Console.WriteLine($"Error saving settings: {ex.Message}");
            }
        }
    }
}
