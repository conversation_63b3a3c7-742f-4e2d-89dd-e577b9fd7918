using System;
using System.Windows.Forms;

namespace PictureMagic
{
    public partial class CustomMessageBoxTestForm : Form
    {
        public CustomMessageBoxTestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.btnBasicExample = new Button();
            this.btnCustomButtonExample = new Button();
            this.SuspendLayout();
            // 
            // btnBasicExample
            // 
            this.btnBasicExample.Location = new System.Drawing.Point(30, 30);
            this.btnBasicExample.Name = "btnBasicExample";
            this.btnBasicExample.Size = new System.Drawing.Size(240, 40);
            this.btnBasicExample.TabIndex = 0;
            this.btnBasicExample.Text = "基本消息框示例";
            this.btnBasicExample.UseVisualStyleBackColor = true;
            this.btnBasicExample.Click += new EventHandler(this.btnBasicExample_Click);
            // 
            // btnCustomButtonExample
            // 
            this.btnCustomButtonExample.Location = new System.Drawing.Point(30, 90);
            this.btnCustomButtonExample.Name = "btnCustomButtonExample";
            this.btnCustomButtonExample.Size = new System.Drawing.Size(240, 40);
            this.btnCustomButtonExample.TabIndex = 1;
            this.btnCustomButtonExample.Text = "自定义按钮示例";
            this.btnCustomButtonExample.UseVisualStyleBackColor = true;
            this.btnCustomButtonExample.Click += new EventHandler(this.btnCustomButtonExample_Click);
            // 
            // CustomMessageBoxTestForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(120F, 120F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(300, 170);
            this.Controls.Add(this.btnCustomButtonExample);
            this.Controls.Add(this.btnBasicExample);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CustomMessageBoxTestForm";
            this.Padding = new System.Windows.Forms.Padding(15);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Text = "自定义消息框测试";
            this.ResumeLayout(false);
        }

        private void btnBasicExample_Click(object sender, EventArgs e)
        {
            // 基本示例，类似于MessageBox.Show
            DialogResult result = CustomMessageBox.Show(
                "这是一个基本的自定义消息框示例。\n它的功能类似于标准的MessageBox。",
                "信息",
                MessageBoxButtons.OKCancel,
                MessageBoxIcon.Information);

            if (result == DialogResult.OK)
            {
                MessageBox.Show("您点击了确定按钮", "结果");
            }
            else
            {
                MessageBox.Show("您点击了取消按钮", "结果");
            }
        }

        private void btnCustomButtonExample_Click(object sender, EventArgs e)
        {
            // 带自定义按钮的示例（不带额外参数）
            DialogResult result = CustomMessageBox.Show(
                "这是一个带有自定义按钮的消息框示例。\n除了标准的\"是\"和\"否\"按钮外，还有一个自定义的\"详情\"按钮。",
                "确认",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                "详情",
                new CustomMessageBox.CustomButtonClickEventHandler(ShowDetailsHandler));

            switch (result)
            {
                case DialogResult.Yes:
                    MessageBox.Show("您点击了\"是\"按钮", "结果");
                    break;
                case DialogResult.No:
                    MessageBox.Show("您点击了\"否\"按钮", "结果");
                    break;
                case DialogResult.OK: // 自定义按钮返回的结果
                    // 这里不需要处理，因为在ShowDetailsHandler中已经处理了
                    break;
            }

            // 带自定义按钮和额外参数的示例
            string additionalInfo = "这是传递给自定义按钮事件处理程序的额外信息。\n" +
                                   "您可以传递任何类型的数据作为参数，例如字符串、数字、对象等。";

            DialogResult result2 = CustomMessageBox.Show(
                "这是一个带有自定义按钮和额外参数的消息框示例。\n" +
                "点击\"详情\"按钮将显示传递的额外信息。",
                "确认（带额外参数）",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question,
                "详情",
                new CustomMessageBox.CustomButtonClickEventHandler(ShowDetailsHandler),
                additionalInfo); // 传递额外信息作为自定义数据

            switch (result2)
            {
                case DialogResult.Yes:
                    MessageBox.Show("您点击了\"是\"按钮", "结果");
                    break;
                case DialogResult.No:
                    MessageBox.Show("您点击了\"否\"按钮", "结果");
                    break;
                case DialogResult.OK: // 自定义按钮返回的结果
                    // 这里不需要处理，因为在ShowDetailsHandler中已经处理了
                    break;
            }
        }

        private void ShowDetailsHandler(object sender, CustomButtonClickEventArgs e)
        {
            // 获取用户自定义数据（如果有）
            string additionalInfo = e.UserData as string;
            string detailsMessage = "这是自定义按钮点击后显示的详细信息。\n\n" +
                "在实际应用中，您可以在这里执行任何自定义操作，例如：\n" +
                "- 打开一个新的窗体\n" +
                "- 显示更多信息\n" +
                "- 执行特定的业务逻辑\n" +
                "- 等等";

            // 如果有额外信息，则添加到详细信息中
            if (!string.IsNullOrEmpty(additionalInfo))
            {
                detailsMessage += "\n\n额外信息：\n" + additionalInfo;
            }

            // 显示详细信息
            MessageBox.Show(
                detailsMessage,
                "详细信息",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);

            // 关闭自定义消息框并设置结果
            if (sender is Form form)
            {
                form.DialogResult = DialogResult.OK;
                form.Close();
            }
        }

        private Button btnBasicExample;
        private Button btnCustomButtonExample;
    }
}