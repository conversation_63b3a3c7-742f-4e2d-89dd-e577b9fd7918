namespace PictureMagic
{
    partial class SaveBatchOptionsDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            labelFormat = new Label();
            comboBoxFormat = new ComboBox();
            labelQuality = new Label();
            trackBarQuality = new TrackBar();
            labelQualityValue = new Label();
            labelFilePath = new Label();
            textBoxFilePath = new TextBox();
            buttonBrowse = new Button();
            buttonSave = new Button();
            buttonCancel = new Button();
            groupBoxImageInfo = new GroupBox();
            labelEstimatedSize = new Label();
            labelOriginalSize = new Label();
            panelPreview = new Panel();
            pictureBoxPreview = new PictureBox();
            ((System.ComponentModel.ISupportInitialize)trackBarQuality).BeginInit();
            groupBoxImageInfo.SuspendLayout();
            panelPreview.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).BeginInit();
            SuspendLayout();
            // 
            // labelFormat
            // 
            labelFormat.AutoSize = true;
            labelFormat.Location = new Point(15, 20);
            labelFormat.Margin = new Padding(4, 0, 4, 0);
            labelFormat.Name = "labelFormat";
            labelFormat.Size = new Size(84, 20);
            labelFormat.TabIndex = 0;
            labelFormat.Text = "图像格式：";
            // 
            // comboBoxFormat
            // 
            comboBoxFormat.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxFormat.FormattingEnabled = true;
            comboBoxFormat.Location = new Point(109, 16);
            comboBoxFormat.Margin = new Padding(4);
            comboBoxFormat.Name = "comboBoxFormat";
            comboBoxFormat.Size = new Size(192, 28);
            comboBoxFormat.TabIndex = 1;
            comboBoxFormat.SelectedIndexChanged += comboBoxFormat_SelectedIndexChanged;
            // 
            // labelQuality
            // 
            labelQuality.AutoSize = true;
            labelQuality.Location = new Point(326, 20);
            labelQuality.Margin = new Padding(4, 0, 4, 0);
            labelQuality.Name = "labelQuality";
            labelQuality.Size = new Size(88, 20);
            labelQuality.TabIndex = 2;
            labelQuality.Text = "JPEG质量：";
            // 
            // trackBarQuality
            // 
            trackBarQuality.LargeChange = 1;
            trackBarQuality.Location = new Point(407, 16);
            trackBarQuality.Margin = new Padding(4);
            trackBarQuality.Maximum = 100;
            trackBarQuality.Minimum = 1;
            trackBarQuality.Name = "trackBarQuality";
            trackBarQuality.Size = new Size(310, 56);
            trackBarQuality.TabIndex = 3;
            trackBarQuality.TickFrequency = 10;
            trackBarQuality.Value = 100;
            trackBarQuality.Scroll += trackBarQuality_Scroll;
            // 
            // labelQualityValue
            // 
            labelQualityValue.AutoSize = true;
            labelQualityValue.Location = new Point(742, 19);
            labelQualityValue.Margin = new Padding(4, 0, 4, 0);
            labelQualityValue.Name = "labelQualityValue";
            labelQualityValue.Size = new Size(49, 20);
            labelQualityValue.TabIndex = 4;
            labelQualityValue.Text = "100%";
            // 
            // labelFilePath
            // 
            labelFilePath.AutoSize = true;
            labelFilePath.Location = new Point(15, 65);
            labelFilePath.Margin = new Padding(4, 0, 4, 0);
            labelFilePath.Name = "labelFilePath";
            labelFilePath.Size = new Size(84, 20);
            labelFilePath.TabIndex = 5;
            labelFilePath.Text = "保存位置：";
            // 
            // textBoxFilePath
            // 
            textBoxFilePath.BorderStyle = BorderStyle.FixedSingle;
            textBoxFilePath.Location = new Point(109, 62);
            textBoxFilePath.Margin = new Padding(4);
            textBoxFilePath.Name = "textBoxFilePath";
            textBoxFilePath.Size = new Size(587, 27);
            textBoxFilePath.TabIndex = 6;
            // 
            // buttonBrowse
            // 
            buttonBrowse.Location = new Point(708, 58);
            buttonBrowse.Margin = new Padding(4);
            buttonBrowse.Name = "buttonBrowse";
            buttonBrowse.Size = new Size(96, 31);
            buttonBrowse.TabIndex = 7;
            buttonBrowse.Text = "浏览...";
            buttonBrowse.UseVisualStyleBackColor = true;
            buttonBrowse.Click += buttonBrowse_Click;
            // 
            // buttonSave
            // 
            buttonSave.Location = new Point(513, 609);
            buttonSave.Margin = new Padding(4);
            buttonSave.Name = "buttonSave";
            buttonSave.Size = new Size(96, 31);
            buttonSave.TabIndex = 8;
            buttonSave.Text = "保存";
            buttonSave.UseVisualStyleBackColor = true;
            buttonSave.Click += buttonSave_Click;
            // 
            // buttonCancel
            // 
            buttonCancel.DialogResult = DialogResult.Cancel;
            buttonCancel.Location = new Point(647, 608);
            buttonCancel.Margin = new Padding(4);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new Size(96, 31);
            buttonCancel.TabIndex = 9;
            buttonCancel.Text = "取消";
            buttonCancel.UseVisualStyleBackColor = true;
            buttonCancel.Click += buttonCancel_Click;
            // 
            // groupBoxImageInfo
            // 
            groupBoxImageInfo.Controls.Add(labelEstimatedSize);
            groupBoxImageInfo.Controls.Add(labelOriginalSize);
            groupBoxImageInfo.Location = new Point(15, 108);
            groupBoxImageInfo.Margin = new Padding(4);
            groupBoxImageInfo.Name = "groupBoxImageInfo";
            groupBoxImageInfo.Padding = new Padding(4);
            groupBoxImageInfo.Size = new Size(789, 62);
            groupBoxImageInfo.TabIndex = 10;
            groupBoxImageInfo.TabStop = false;
            groupBoxImageInfo.Text = "图像信息";
            // 
            // labelEstimatedSize
            // 
            labelEstimatedSize.AutoSize = true;
            labelEstimatedSize.Location = new Point(351, 27);
            labelEstimatedSize.Margin = new Padding(4, 0, 4, 0);
            labelEstimatedSize.Name = "labelEstimatedSize";
            labelEstimatedSize.Size = new Size(139, 20);
            labelEstimatedSize.TabIndex = 1;
            labelEstimatedSize.Text = "预估文件大小: 0 KB";
            // 
            // labelOriginalSize
            // 
            labelOriginalSize.AutoSize = true;
            labelOriginalSize.Location = new Point(13, 27);
            labelOriginalSize.Margin = new Padding(4, 0, 4, 0);
            labelOriginalSize.Name = "labelOriginalSize";
            labelOriginalSize.Size = new Size(145, 20);
            labelOriginalSize.TabIndex = 0;
            labelOriginalSize.Text = "图像尺寸: 0 x 0 像素";
            // 
            // panelPreview
            // 
            panelPreview.AutoScroll = true;
            panelPreview.BorderStyle = BorderStyle.FixedSingle;
            panelPreview.Controls.Add(pictureBoxPreview);
            panelPreview.Location = new Point(15, 178);
            panelPreview.Margin = new Padding(4);
            panelPreview.Name = "panelPreview";
            panelPreview.Size = new Size(789, 411);
            panelPreview.TabIndex = 11;
            // 
            // pictureBoxPreview
            // 
            pictureBoxPreview.Location = new Point(0, 0);
            pictureBoxPreview.Margin = new Padding(4);
            pictureBoxPreview.Name = "pictureBoxPreview";
            pictureBoxPreview.Size = new Size(129, 133);
            pictureBoxPreview.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBoxPreview.TabIndex = 0;
            pictureBoxPreview.TabStop = false;
            // 
            // SaveBatchOptionsDialog
            // 
            AcceptButton = buttonSave;
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            CancelButton = buttonCancel;
            ClientSize = new Size(822, 658);
            Controls.Add(panelPreview);
            Controls.Add(groupBoxImageInfo);
            Controls.Add(buttonCancel);
            Controls.Add(buttonSave);
            Controls.Add(buttonBrowse);
            Controls.Add(textBoxFilePath);
            Controls.Add(labelFilePath);
            Controls.Add(labelQualityValue);
            Controls.Add(trackBarQuality);
            Controls.Add(labelQuality);
            Controls.Add(comboBoxFormat);
            Controls.Add(labelFormat);
            FormBorderStyle = FormBorderStyle.FixedDialog;
            Margin = new Padding(4);
            MaximizeBox = false;
            MinimizeBox = false;
            Name = "SaveBatchOptionsDialog";
            ShowIcon = false;
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "批量保存选项";
            ((System.ComponentModel.ISupportInitialize)trackBarQuality).EndInit();
            groupBoxImageInfo.ResumeLayout(false);
            groupBoxImageInfo.PerformLayout();
            panelPreview.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).EndInit();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label labelFormat;
        private System.Windows.Forms.ComboBox comboBoxFormat;
        private System.Windows.Forms.Label labelQuality;
        private System.Windows.Forms.TrackBar trackBarQuality;
        private System.Windows.Forms.Label labelQualityValue;
        private System.Windows.Forms.Label labelFilePath;
        private System.Windows.Forms.TextBox textBoxFilePath;
        private System.Windows.Forms.Button buttonBrowse;
        private System.Windows.Forms.Button buttonSave;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.GroupBox groupBoxImageInfo;
        private System.Windows.Forms.Label labelEstimatedSize;
        private System.Windows.Forms.Label labelOriginalSize;
        private System.Windows.Forms.Panel panelPreview;
        private System.Windows.Forms.PictureBox pictureBoxPreview;
    }
}