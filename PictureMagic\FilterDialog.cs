using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PictureMagic
{
    public partial class FilterDialog : Form
    {
        private Image originalImage;
        private Image previewImage;
        private FilterEffects.FilterType currentFilter = FilterEffects.FilterType.None;
        private int filterIntensity = 100;

        // 使用ProgressForm替代内嵌进度条
        private ProgressForm progressForm = null;

        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public Image ResultImage { get; private set; } = null!; // 使用null抑制操作符

        public FilterDialog(Image image)
        {
            InitializeComponent();
            originalImage = image;
            previewImage = new Bitmap(originalImage);
            pictureBoxPreview.Image = previewImage;
            pictureBoxPreview.SizeMode = PictureBoxSizeMode.Zoom;

            // 加载滤镜列表
            LoadFilterList();

            // 设置强度滑块
            trackBarIntensity.Minimum = 0;
            trackBarIntensity.Maximum = 100;
            trackBarIntensity.Value = filterIntensity;
            labelIntensityValue.Text = filterIntensity.ToString() + "%";
        }

        private void ShowProgress(string message = "正在处理...")
        {
            // 使用独立的进度窗体显示进度
            if (progressForm == null || progressForm.IsDisposed)
            {
                progressForm = ProgressForm.ShowProgressDialog(this, message, true); // 使用遮罩效果
            }
            else
            {
                progressForm.UpdateMessage(message);
            }
            Application.DoEvents(); // 强制刷新UI
        }

        private void HideProgress()
        {
            // 关闭进度窗体
            if (progressForm != null && !progressForm.IsDisposed)
            {
                progressForm.CloseProgress();
                progressForm = null;
            }
        }

        private void LoadFilterList()
        {
            listBoxFilters.Items.Clear();
            foreach (string filterName in FilterEffects.GetFilterNames())
            {
                listBoxFilters.Items.Add(filterName);
            }
            listBoxFilters.SelectedIndex = 0; // 默认选择"无滤镜"
        }

        private void listBoxFilters_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (listBoxFilters.SelectedIndex >= 0)
            {
                string filterName = listBoxFilters.SelectedItem.ToString();
                currentFilter = FilterEffects.GetFilterTypeByName(filterName);
                UpdatePreview();
            }
        }

        private void trackBarIntensity_Scroll(object sender, EventArgs e)
        {
            filterIntensity = trackBarIntensity.Value;
            labelIntensityValue.Text = filterIntensity.ToString() + "%";
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            try
            {
                // 检查原始图像是否有效
                if (originalImage == null || originalImage.Width <= 0 || originalImage.Height <= 0)
                {
                    MessageBox.Show("原始图像无效，无法应用滤镜", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 显示进度提示
                ShowProgress($"正在应用 {listBoxFilters.SelectedItem} 滤镜...");

                // 释放之前的预览图像
                if (previewImage != null && previewImage != originalImage)
                {
                    pictureBoxPreview.Image = null;
                    previewImage.Dispose();
                    previewImage = null; // 确保引用被清除
                }

                try
                {
                    // 应用滤镜创建新的预览图像，传递进度窗体
                    previewImage = FilterEffects.ApplyFilter(originalImage, currentFilter, filterIntensity, progressForm);
                    
                    // 检查生成的预览图像是否有效
                    if (previewImage == null || previewImage.Width <= 0 || previewImage.Height <= 0)
                    {
                        throw new ArgumentException("生成的预览图像无效");
                    }
                    
                    pictureBoxPreview.Image = previewImage;
                }
                catch (OutOfMemoryException)
                {
                    MessageBox.Show("内存不足，无法应用滤镜。请尝试使用较小的图像。", "内存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    // 确保有一个有效的预览图像
                    if (previewImage == null)
                    {
                        previewImage = new Bitmap(originalImage);
                        pictureBoxPreview.Image = previewImage;
                    }
                }
                catch (ArgumentException argEx)
                {
                    MessageBox.Show($"参数无效: {argEx.Message}", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    // 确保有一个有效的预览图像
                    if (previewImage == null)
                    {
                        previewImage = new Bitmap(originalImage);
                        pictureBoxPreview.Image = previewImage;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"应用滤镜时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    
                    // 确保有一个有效的预览图像
                    if (previewImage == null)
                    {
                        previewImage = new Bitmap(originalImage);
                        pictureBoxPreview.Image = previewImage;
                    }
                }
                finally
                {
                    // 隐藏进度提示
                    HideProgress();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新预览时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                HideProgress();
            }
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示进度提示
                ShowProgress("正在应用滤镜...");

                // 应用滤镜到结果图像，传递进度窗体
                ResultImage = FilterEffects.ApplyFilter(originalImage, currentFilter, filterIntensity, progressForm);

                // 隐藏进度提示
                HideProgress();

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBox.Show($"应用滤镜时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void buttonReset_Click(object sender, EventArgs e)
        {
            // 重置滤镜和强度
            listBoxFilters.SelectedIndex = 0; // "无滤镜"
            trackBarIntensity.Value = 100;
            filterIntensity = 100;
            labelIntensityValue.Text = "100%";
            
            // 更新预览
            UpdatePreview();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            // 确保关闭进度窗体
            HideProgress();

            // 释放资源
            if (previewImage != null && previewImage != originalImage && previewImage != ResultImage)
            {
                previewImage.Dispose();
            }
        }
    }
}