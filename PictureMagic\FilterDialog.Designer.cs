namespace PictureMagic
{
    partial class FilterDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FilterDialog));
            splitContainer = new SplitContainer();
            listBoxFilters = new ListBox();
            panel1 = new Panel();
            labelIntensity = new Label();
            trackBarIntensity = new TrackBar();
            labelIntensityValue = new Label();
            pictureBoxPreview = new PictureBox();
            panel2 = new Panel();
            buttonReset = new Button();
            buttonCancel = new Button();
            buttonOK = new Button();
            ((System.ComponentModel.ISupportInitialize)splitContainer).BeginInit();
            splitContainer.Panel1.SuspendLayout();
            splitContainer.Panel2.SuspendLayout();
            splitContainer.SuspendLayout();
            panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarIntensity).BeginInit();
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).BeginInit();
            panel2.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer
            // 
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Location = new Point(0, 0);
            splitContainer.Margin = new Padding(4);
            splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            splitContainer.Panel1.Controls.Add(listBoxFilters);
            splitContainer.Panel1.Controls.Add(panel1);
            splitContainer.Panel1MinSize = 200;
            // 
            // splitContainer.Panel2
            // 
            splitContainer.Panel2.Controls.Add(pictureBoxPreview);
            splitContainer.Panel2.Controls.Add(panel2);
            splitContainer.Size = new Size(1029, 600);
            splitContainer.SplitterDistance = 257;
            splitContainer.SplitterWidth = 5;
            splitContainer.TabIndex = 0;
            // 
            // listBoxFilters
            // 
            listBoxFilters.Dock = DockStyle.Fill;
            listBoxFilters.FormattingEnabled = true;
            listBoxFilters.Location = new Point(0, 0);
            listBoxFilters.Margin = new Padding(4);
            listBoxFilters.Name = "listBoxFilters";
            listBoxFilters.Size = new Size(257, 467);
            listBoxFilters.TabIndex = 0;
            listBoxFilters.SelectedIndexChanged += listBoxFilters_SelectedIndexChanged;
            // 
            // panel1
            // 
            panel1.Controls.Add(labelIntensity);
            panel1.Controls.Add(trackBarIntensity);
            panel1.Controls.Add(labelIntensityValue);
            panel1.Dock = DockStyle.Bottom;
            panel1.Location = new Point(0, 467);
            panel1.Margin = new Padding(4);
            panel1.Name = "panel1";
            panel1.Size = new Size(257, 133);
            panel1.TabIndex = 1;
            // 
            // labelIntensity
            // 
            labelIntensity.AutoSize = true;
            labelIntensity.Location = new Point(15, 20);
            labelIntensity.Margin = new Padding(4, 0, 4, 0);
            labelIntensity.Name = "labelIntensity";
            labelIntensity.Size = new Size(84, 20);
            labelIntensity.TabIndex = 0;
            labelIntensity.Text = "滤镜强度：";
            // 
            // trackBarIntensity
            // 
            trackBarIntensity.Location = new Point(15, 53);
            trackBarIntensity.Margin = new Padding(4);
            trackBarIntensity.Name = "trackBarIntensity";
            trackBarIntensity.Size = new Size(226, 56);
            trackBarIntensity.TabIndex = 1;
            trackBarIntensity.Value = 10;
            trackBarIntensity.Scroll += trackBarIntensity_Scroll;
            // 
            // labelIntensityValue
            // 
            labelIntensityValue.AutoSize = true;
            labelIntensityValue.Location = new Point(109, 20);
            labelIntensityValue.Margin = new Padding(4, 0, 4, 0);
            labelIntensityValue.Name = "labelIntensityValue";
            labelIntensityValue.Size = new Size(49, 20);
            labelIntensityValue.TabIndex = 2;
            labelIntensityValue.Text = "100%";
            // 
            // pictureBoxPreview
            // 
            pictureBoxPreview.BackColor = Color.White;
            pictureBoxPreview.Dock = DockStyle.Fill;
            pictureBoxPreview.Location = new Point(0, 0);
            pictureBoxPreview.Margin = new Padding(4);
            pictureBoxPreview.Name = "pictureBoxPreview";
            pictureBoxPreview.Size = new Size(767, 533);
            pictureBoxPreview.TabIndex = 0;
            pictureBoxPreview.TabStop = false;
            // 
            // panel2
            // 
            panel2.Controls.Add(buttonReset);
            panel2.Controls.Add(buttonCancel);
            panel2.Controls.Add(buttonOK);
            panel2.Dock = DockStyle.Bottom;
            panel2.Location = new Point(0, 533);
            panel2.Margin = new Padding(4);
            panel2.Name = "panel2";
            panel2.Size = new Size(767, 67);
            panel2.TabIndex = 1;
            // 
            // buttonReset
            // 
            buttonReset.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            buttonReset.Location = new Point(15, 19);
            buttonReset.Margin = new Padding(4);
            buttonReset.Name = "buttonReset";
            buttonReset.Size = new Size(96, 31);
            buttonReset.TabIndex = 2;
            buttonReset.Text = "重置";
            buttonReset.UseVisualStyleBackColor = true;
            buttonReset.Click += buttonReset_Click;
            // 
            // buttonCancel
            // 
            buttonCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonCancel.Location = new Point(655, 19);
            buttonCancel.Margin = new Padding(4);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new Size(96, 31);
            buttonCancel.TabIndex = 1;
            buttonCancel.Text = "取消";
            buttonCancel.UseVisualStyleBackColor = true;
            buttonCancel.Click += buttonCancel_Click;
            // 
            // buttonOK
            // 
            buttonOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonOK.Location = new Point(551, 19);
            buttonOK.Margin = new Padding(4);
            buttonOK.Name = "buttonOK";
            buttonOK.Size = new Size(96, 31);
            buttonOK.TabIndex = 0;
            buttonOK.Text = "确定";
            buttonOK.UseVisualStyleBackColor = true;
            buttonOK.Click += buttonOK_Click;
            // 
            // FilterDialog
            // 
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            ClientSize = new Size(1029, 600);
            Controls.Add(splitContainer);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4);
            MinimizeBox = false;
            Name = "FilterDialog";
            StartPosition = FormStartPosition.CenterParent;
            Text = "滤镜效果";
            splitContainer.Panel1.ResumeLayout(false);
            splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer).EndInit();
            splitContainer.ResumeLayout(false);
            panel1.ResumeLayout(false);
            panel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarIntensity).EndInit();
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).EndInit();
            panel2.ResumeLayout(false);
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer;
        private System.Windows.Forms.ListBox listBoxFilters;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.PictureBox pictureBoxPreview;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Button buttonOK;
        private System.Windows.Forms.Label labelIntensity;
        private System.Windows.Forms.TrackBar trackBarIntensity;
        private System.Windows.Forms.Label labelIntensityValue;
        private System.Windows.Forms.Button buttonReset;
    }
}