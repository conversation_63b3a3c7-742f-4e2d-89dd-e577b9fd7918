using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Drawing.Imaging;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace PictureMagic
{
    public partial class BrightnessContrastDialog : Form
    {
        private Image originalImage;
        private Image previewImage;
        private int brightness = 0;
        private int contrast = 0;

        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public Image ResultImage { get; private set; } = null!; // 使用null抑制操作符

        public BrightnessContrastDialog(Image image)
        {
            InitializeComponent();
            originalImage = image;
            previewImage = new Bitmap(originalImage);
            pictureBoxPreview.Image = previewImage;
            pictureBoxPreview.SizeMode = PictureBoxSizeMode.Zoom;

            // 设置亮度滑块
            trackBarBrightness.Minimum = -100;
            trackBarBrightness.Maximum = 100;
            trackBarBrightness.Value = brightness;
            labelBrightnessValue.Text = brightness.ToString();

            // 设置对比度滑块
            trackBarContrast.Minimum = -100;
            trackBarContrast.Maximum = 100;
            trackBarContrast.Value = contrast;
            labelContrastValue.Text = contrast.ToString();
        }



        private void trackBarBrightness_Scroll(object sender, EventArgs e)
        {
            brightness = trackBarBrightness.Value;
            labelBrightnessValue.Text = brightness.ToString();
            UpdatePreview();
        }

        private void trackBarContrast_Scroll(object sender, EventArgs e)
        {
            contrast = trackBarContrast.Value;
            labelContrastValue.Text = contrast.ToString();
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            try
            {
                // 释放之前的预览图像
                if (previewImage != null && previewImage != originalImage)
                {
                    pictureBoxPreview.Image = null;
                    previewImage.Dispose();
                    previewImage = null;
                }

                // 应用亮度和对比度调整
                previewImage = AdjustBrightnessContrast(originalImage, brightness, contrast);
                pictureBoxPreview.Image = previewImage;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新预览时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private Image AdjustBrightnessContrast(Image image, int brightness, int contrast)
        {
            try
            {
                // 创建新的位图
                Bitmap adjustedImage = new Bitmap(image.Width, image.Height);
                float contrastFactor = (100.0f + contrast) / 100.0f;
                contrastFactor *= contrastFactor;

                // 尝试使用LockBits方法进行性能优化
                try
                {
                    BitmapData sourceData = ((Bitmap)image).LockBits(
                        new Rectangle(0, 0, image.Width, image.Height),
                        ImageLockMode.ReadOnly, PixelFormat.Format32bppArgb);

                    BitmapData targetData = adjustedImage.LockBits(
                        new Rectangle(0, 0, adjustedImage.Width, adjustedImage.Height),
                        ImageLockMode.WriteOnly, PixelFormat.Format32bppArgb);

                    unsafe
                    {
                        byte* sourcePtr = (byte*)sourceData.Scan0;
                        byte* targetPtr = (byte*)targetData.Scan0;

                        int sourceStride = sourceData.Stride;
                        int targetStride = targetData.Stride;

                        for (int y = 0; y < image.Height; y++)
                        {
                            for (int x = 0; x < image.Width; x++)
                            {
                                int sourceIndex = y * sourceStride + x * 4;
                                int targetIndex = y * targetStride + x * 4;

                                byte b = sourcePtr[sourceIndex];
                                byte g = sourcePtr[sourceIndex + 1];
                                byte r = sourcePtr[sourceIndex + 2];
                                byte a = sourcePtr[sourceIndex + 3];

                                // 应用亮度
                                r = ClampByte(r + brightness);
                                g = ClampByte(g + brightness);
                                b = ClampByte(b + brightness);

                                // 应用对比度
                                if (contrast != 0)
                                {
                                    float factor = (259.0f * (contrastFactor + 255.0f)) / (255.0f * (259.0f - contrastFactor));
                                    r = ClampByte((int)(factor * (r - 128) + 128));
                                    g = ClampByte((int)(factor * (g - 128) + 128));
                                    b = ClampByte((int)(factor * (b - 128) + 128));
                                }

                                targetPtr[targetIndex] = b;
                                targetPtr[targetIndex + 1] = g;
                                targetPtr[targetIndex + 2] = r;
                                targetPtr[targetIndex + 3] = a;
                            }
                        }
                    }

                    ((Bitmap)image).UnlockBits(sourceData);
                    adjustedImage.UnlockBits(targetData);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"LockBits方法失败，回退到GetPixel/SetPixel: {ex.Message}");

                    // 如果LockBits方法失败，回退到GetPixel/SetPixel方法
                    for (int y = 0; y < image.Height; y++)
                    {
                        for (int x = 0; x < image.Width; x++)
                        {
                            Color pixelColor = ((Bitmap)image).GetPixel(x, y);

                            // 应用亮度
                            int r = ClampByte(pixelColor.R + brightness);
                            int g = ClampByte(pixelColor.G + brightness);
                            int b = ClampByte(pixelColor.B + brightness);

                            // 应用对比度
                            if (contrast != 0)
                            {
                                float factor = (259.0f * (contrastFactor + 255.0f)) / (255.0f * (259.0f - contrastFactor));
                                r = ClampByte((int)(factor * (r - 128) + 128));
                                g = ClampByte((int)(factor * (g - 128) + 128));
                                b = ClampByte((int)(factor * (b - 128) + 128));
                            }

                            adjustedImage.SetPixel(x, y, Color.FromArgb(pixelColor.A, r, g, b));
                        }
                    }
                }

                return adjustedImage;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"调整亮度和对比度时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return new Bitmap(image); // 返回原始图像的副本
            }
        }

        private byte ClampByte(int value)
        {
            return (byte)Math.Max(0, Math.Min(255, value));
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            try
            {
                // 应用亮度和对比度调整到结果图像
                ResultImage = AdjustBrightnessContrast(originalImage, brightness, contrast);

                DialogResult = DialogResult.OK;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用亮度和对比度调整时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void buttonReset_Click(object sender, EventArgs e)
        {
            // 重置亮度和对比度
            trackBarBrightness.Value = 0;
            trackBarContrast.Value = 0;
            brightness = 0;
            contrast = 0;
            labelBrightnessValue.Text = "0";
            labelContrastValue.Text = "0";
            
            // 更新预览
            UpdatePreview();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);



            // 释放资源
            if (previewImage != null && previewImage != originalImage && previewImage != ResultImage)
            {
                previewImage.Dispose();
            }
        }
    }
}