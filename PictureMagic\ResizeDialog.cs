using System.Drawing.Drawing2D;

namespace PictureMagic
{
    public partial class ResizeDialog : Form
    {
        private Image originalImage;
        private bool maintainAspectRatio = true;
        private double aspectRatio;
        private ProgressForm progressForm = null;

        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public Image ResultImage { get; private set; } = null!; // 使用null抑制操作符

        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public int NewWidth { get; private set; }

        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public int NewHeight { get; private set; }

        public ResizeDialog(Image image)
        {
            InitializeComponent();

            // 检查传入的图像是否为null或尺寸是否有效
            if (image == null)
            {
                throw new ArgumentNullException(nameof(image), "图像不能为null");
            }

            originalImage = image;
            aspectRatio = (double)originalImage.Width / originalImage.Height;

            // 设置最大值为原始尺寸的5倍
            numericWidth.Maximum = originalImage.Width * 5;
            numericHeight.Maximum = originalImage.Height * 5;

            // 设置最小值
            numericWidth.Minimum = 1;
            numericHeight.Minimum = 1;

            // 初始化NewWidth和NewHeight属性
            NewWidth = Math.Max(1, originalImage.Width);
            NewHeight = Math.Max(1, originalImage.Height);

            // 设置初始值为图像的原始尺寸（确保不小于最小值）
            numericWidth.Value = NewWidth;
            numericHeight.Value = NewHeight;

            // 初始化预设下拉框
            InitializePresets();

            // 初始化预览控件
            InitializePreviewControl();

            // 注册Application.Idle事件，用于批处理预览更新
            Application.Idle += Application_Idle;

            // 初始化时直接更新预览图像，而不是通过标志
            UpdatePreview();

            // 设置原始尺寸标签，添加原始比例信息
            string ratio = aspectRatio.ToString("F2");
            labelOriginalSize.Text = $"原始尺寸: {originalImage.Width} x {originalImage.Height} 像素 (比例 {ratio}:1)";
        }

        private void InitializePreviewControl()
        {
            // 设置PictureBox属性
            originalPictureBox.SizeMode = PictureBoxSizeMode.AutoSize;
            previewPictureBox.SizeMode = PictureBoxSizeMode.AutoSize;

            // 显示原始图像
            DisplayOriginalImage();
        }

        private void DisplayOriginalImage()
        {
            if (originalImage != null)
            {
                // 创建原始图像的副本
                Bitmap origImage = new Bitmap(originalImage.Width, originalImage.Height);
                using (Graphics g = Graphics.FromImage(origImage))
                {
                    g.DrawImage(originalImage, 0, 0, originalImage.Width, originalImage.Height);

                    // 添加网格线
                    using (Pen gridPen = new Pen(Color.LightGray, 1))
                    {
                        // 绘制水平网格线
                        for (int y = 20; y < originalImage.Height; y += 20)
                        {
                            g.DrawLine(gridPen, 0, y, originalImage.Width, y);
                        }

                        // 绘制垂直网格线
                        for (int x = 20; x < originalImage.Width; x += 20)
                        {
                            g.DrawLine(gridPen, x, 0, x, originalImage.Height);
                        }
                    }

                    // 添加比例尺
                    int scaleBarLength = 100;
                    int scaleBarHeight = 10;
                    int scaleBarY = originalImage.Height - scaleBarHeight - 5;
                    int scaleBarX = 5;

                    g.FillRectangle(Brushes.White, scaleBarX, scaleBarY, scaleBarLength, scaleBarHeight);
                    using (Pen scalePen = new Pen(Color.Black, 1))
                    {
                        g.DrawRectangle(scalePen, scaleBarX, scaleBarY, scaleBarLength, scaleBarHeight);
                        for (int i = 0; i <= 10; i++)
                        {
                            int x = scaleBarX + (i * 10);
                            int tickHeight = (i % 5 == 0) ? scaleBarHeight : scaleBarHeight / 2;
                            g.DrawLine(scalePen, x, scaleBarY, x, scaleBarY + tickHeight);
                        }
                    }

                    using (Font font = new Font("Arial", 7))
                    {
                        g.DrawString("100px", font, Brushes.Black, scaleBarX + scaleBarLength + 2, scaleBarY);
                    }
                }

                // 设置原始图像
                if (originalPictureBox.Image != null)
                {
                    originalPictureBox.Image.Dispose();
                }
                originalPictureBox.Image = origImage;
                originalPictureBox.Size = origImage.Size;

                // 居中显示
                if (originalPictureBox.Parent is Panel panel)
                {
                    if (origImage.Width < panel.ClientSize.Width)
                    {
                        originalPictureBox.Left = (panel.ClientSize.Width - origImage.Width) / 2;
                    }
                    else
                    {
                        originalPictureBox.Left = 0;
                    }

                    if (origImage.Height < panel.ClientSize.Height)
                    {
                        originalPictureBox.Top = (panel.ClientSize.Height - origImage.Height) / 2;
                    }
                    else
                    {
                        originalPictureBox.Top = 0;
                    }
                }
            }
        }

        private void InitializePresets()
        {
            // 设置预设尺寸选项
            comboBoxPresets.Items.Add("自定义");
            comboBoxPresets.Items.Add("50%");
            comboBoxPresets.Items.Add("75%");
            comboBoxPresets.Items.Add("100%");
            comboBoxPresets.Items.Add("125%");
            comboBoxPresets.Items.Add("150%");
            comboBoxPresets.Items.Add("200%");
            comboBoxPresets.Items.Add("640 x 480");
            comboBoxPresets.Items.Add("800 x 600");
            comboBoxPresets.Items.Add("1024 x 768");
            comboBoxPresets.Items.Add("1280 x 720 (HD)");
            comboBoxPresets.Items.Add("1920 x 1080 (Full HD)");
            comboBoxPresets.Items.Add("2560 x 1440 (QHD)");
            comboBoxPresets.Items.Add("3840 x 2160 (4K UHD)");
            comboBoxPresets.Items.Add("7680 x 4320 (8K UHD)");
            comboBoxPresets.SelectedIndex = 0; // 默认选择自定义

            // 设置比例选项
            comboBoxScaleRatio.Items.Add("自由调整");
            comboBoxScaleRatio.Items.Add("1:1");
            comboBoxScaleRatio.Items.Add("4:3");
            comboBoxScaleRatio.Items.Add("16:9");
            comboBoxScaleRatio.Items.Add("16:10");
            comboBoxScaleRatio.Items.Add("3:2");
            comboBoxScaleRatio.Items.Add("2:3");
            comboBoxScaleRatio.Items.Add("5:4");
            comboBoxScaleRatio.Items.Add("9:16");
            comboBoxScaleRatio.SelectedIndex = 0; // 默认选择自由调整

            // 设置调整大小方法选项
            comboBoxResizeMethod.Items.Add("高质量（慢）");
            comboBoxResizeMethod.Items.Add("标准");
            comboBoxResizeMethod.Items.Add("快速（低质量）");
            comboBoxResizeMethod.SelectedIndex = 0; // 默认选择高质量
        }

        private void numericWidth_ValueChanged(object sender, EventArgs e)
        {
            // 如果不是自定义模式，禁止修改
            if (!updatingValues && comboBoxPresets.SelectedIndex != 0)
            {
                updatingValues = true;
                numericWidth.Value = NewWidth; // 恢复原值
                updatingValues = false;
                return;
            }

            // 移除重复的进度窗体显示代码，统一在UpdatePreview方法中处理

            if (maintainAspectRatio && !updatingValues)
            {
                updatingValues = true;
                numericHeight.Value = (decimal)Math.Round((double)numericWidth.Value / aspectRatio);
                updatingValues = false;
            }

            // 无论是否保持纵横比，都更新宽度，确保值大于0
            NewWidth = Math.Max(1, (int)numericWidth.Value);

            // 如果保持纵横比，也更新高度
            if (maintainAspectRatio)
            {
                NewHeight = Math.Max(1, (int)numericHeight.Value);
            }

            // 只有在用户手动修改值时（不是程序设置值时）才更新预设下拉框
            // 如果不是在更新值的过程中，且当前选择的不是自定义，且下拉框已有元素，则将预设设置为自定义
            if (!updatingValues && comboBoxPresets.Items.Count > 0 && comboBoxPresets.SelectedIndex != 0)
            {
                updatingValues = true;
                comboBoxPresets.SelectedIndex = 0; // 设置为自定义
                updatingValues = false;
            }

            // 如果不是在更新比例的过程中，将比例选项设置为自由调整
            if (!updatingValues && !updatingRatio)
            {
                updatingRatio = true;
                comboBoxScaleRatio.SelectedIndex = 0; // 自由调整
                updatingRatio = false;
            }

            // 设置需要更新预览的标志，而不是直接调用UpdatePreview
            previewUpdateNeeded = true;
        }

        private void numericHeight_ValueChanged(object sender, EventArgs e)
        {
            // 如果不是自定义模式，禁止修改
            if (!updatingValues && comboBoxPresets.SelectedIndex != 0)
            {
                updatingValues = true;
                numericHeight.Value = NewHeight; // 恢复原值
                updatingValues = false;
                return;
            }

            // 移除重复的进度窗体显示代码，统一在UpdatePreview方法中处理

            if (maintainAspectRatio && !updatingValues)
            {
                updatingValues = true;
                numericWidth.Value = (decimal)Math.Round((double)numericHeight.Value * aspectRatio);
                updatingValues = false;
            }

            // 无论是否保持纵横比，都更新高度，确保值大于0
            NewHeight = Math.Max(1, (int)numericHeight.Value);

            // 如果保持纵横比，也更新宽度
            if (maintainAspectRatio)
            {
                NewWidth = Math.Max(1, (int)numericWidth.Value);
            }

            // 只有在用户手动修改值时（不是程序设置值时）才更新预设下拉框
            // 如果不是在更新值的过程中，且当前选择的不是自定义，且下拉框已有元素，则将预设设置为自定义
            if (!updatingValues && comboBoxPresets.Items.Count > 0 && comboBoxPresets.SelectedIndex != 0)
            {
                updatingValues = true;
                comboBoxPresets.SelectedIndex = 0; // 设置为自定义
                updatingValues = false;
            }

            // 如果不是在更新比例的过程中，将比例选项设置为自由调整
            if (!updatingValues && !updatingRatio)
            {
                updatingRatio = true;
                comboBoxScaleRatio.SelectedIndex = 0; // 自由调整
                updatingRatio = false;
            }

            // 设置需要更新预览的标志，而不是直接调用UpdatePreview
            previewUpdateNeeded = true;
        }

        private bool updatingValues = false;
        private bool updatingRatio = false;

        // 添加标志，表示是否需要更新预览和是否正在更新预览
        private bool previewUpdateNeeded = false;
        private bool isUpdatingPreview = false;

        private void checkBoxMaintainAspectRatio_CheckedChanged(object sender, EventArgs e)
        {
            // 如果正在更新值，不处理此事件
            if (updatingValues)
            {
                return;
            }

            // 移除重复的进度窗体显示代码，统一在UpdatePreview方法中处理

            maintainAspectRatio = checkBoxMaintainAspectRatio.Checked;

            if (maintainAspectRatio)
            {
                // 重新计算高度以匹配当前宽度的纵横比
                updatingValues = true;
                numericHeight.Value = (decimal)Math.Round((double)numericWidth.Value / aspectRatio);
                updatingValues = false;
            }
            else
            {
                // 取消选中保持纵横比时，保持当前尺寸
                // 但确保更新属性值，并确保值大于0
                NewWidth = Math.Max(1, (int)numericWidth.Value);
                NewHeight = Math.Max(1, (int)numericHeight.Value);

                // 更新预设下拉框为自定义
                if (comboBoxPresets.Items.Count > 0 && comboBoxPresets.SelectedIndex != 0) // 如果下拉框有元素且不是自定义
                {
                    updatingValues = true;
                    comboBoxPresets.SelectedIndex = 0; // 设置为自定义
                    updatingValues = false;
                }
            }

            // 如果启用了保持纵横比，将比例选项设置为自由调整
            if (maintainAspectRatio && !updatingRatio)
            {
                updatingRatio = true;
                comboBoxScaleRatio.SelectedIndex = 0; // 自由调整
                updatingRatio = false;
            }

            // 无论是否选中保持纵横比，都更新预览
            // 设置需要更新预览的标志，而不是直接调用UpdatePreview
            previewUpdateNeeded = true;
        }

        private void comboBoxScaleRatio_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (updatingRatio) return;

            updatingRatio = true;

            // 获取当前选择的比例
            int selectedIndex = comboBoxScaleRatio.SelectedIndex;

            if (selectedIndex == 0) // 自由调整
            {
                // 不做任何调整，允许用户自由设置宽高
                checkBoxMaintainAspectRatio.Checked = false;
            }
            else
            {
                // 设置保持纵横比
                checkBoxMaintainAspectRatio.Checked = true;

                // 根据选择的比例调整宽高
                double ratio = 1.0; // 默认1:1

                switch (selectedIndex)
                {
                    case 1: // 1:1
                        ratio = 1.0;
                        break;
                    case 2: // 4:3
                        ratio = 4.0 / 3.0;
                        break;
                    case 3: // 16:9
                        ratio = 16.0 / 9.0;
                        break;
                    case 4: // 16:10
                        ratio = 16.0 / 10.0;
                        break;
                    case 5: // 3:2
                        ratio = 3.0 / 2.0;
                        break;
                    case 6: // 2:3
                        ratio = 2.0 / 3.0;
                        break;
                    case 7: // 5:4
                        ratio = 5.0 / 4.0;
                        break;
                    case 8: // 9:16
                        ratio = 9.0 / 16.0;
                        break;
                }

                // 使用进度窗体显示进度将在UpdatePreview方法中处理

                // 根据当前宽度和选择的比例计算新的高度
                updatingValues = true;
                int newHeight = (int)Math.Round((double)numericWidth.Value / ratio);
                numericHeight.Value = Math.Min(numericHeight.Maximum, Math.Max(numericHeight.Minimum, newHeight));
                updatingValues = false;

                // 更新预览
                // 设置需要更新预览的标志，而不是直接调用UpdatePreview
                previewUpdateNeeded = true;
            }

            updatingRatio = false;
        }

        private void comboBoxPresets_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 检查是否有选中项
            if (comboBoxPresets.SelectedItem == null)
            {
                updatingValues = false;
                return;
            }

            // 移除重复的进度窗体显示代码，统一在UpdatePreview方法中处理

            updatingValues = true;

            string selectedPreset = comboBoxPresets.SelectedItem.ToString();

            // 根据是否选择"自定义"来启用或禁用宽度和高度输入框
            bool isCustom = selectedPreset == "自定义";
            numericWidth.Enabled = isCustom;
            numericHeight.Enabled = isCustom;

            switch (selectedPreset)
            {
                case "自定义":
                    // 自定义选项不做特殊处理，保持当前输入框的值
                    // 但确保更新属性值，并确保值大于0
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "50%":
                    // 检查是否是放大操作
                    if (originalImage.Width * 0.5 > originalImage.Width && comboBoxResizeMethod.SelectedIndex != 0)
                    {
                        comboBoxResizeMethod.SelectedIndex = 0; // 设置为高质量
                    }

                    int width50 = (int)(originalImage.Width * 0.5);
                    if (maintainAspectRatio)
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width50);
                        numericHeight.Value = Math.Max(1, (decimal)Math.Round((double)width50 / aspectRatio));
                    }
                    else
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width50);
                        numericHeight.Value = Math.Max(1, (decimal)(originalImage.Height * 0.5));
                    }
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "75%":
                    // 检查是否是放大操作
                    if (originalImage.Width * 0.75 > originalImage.Width && comboBoxResizeMethod.SelectedIndex != 0)
                    {
                        comboBoxResizeMethod.SelectedIndex = 0; // 设置为高质量
                    }

                    int width75 = (int)(originalImage.Width * 0.75);
                    if (maintainAspectRatio)
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width75);
                        numericHeight.Value = Math.Max(1, (decimal)Math.Round((double)width75 / aspectRatio));
                    }
                    else
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width75);
                        numericHeight.Value = Math.Max(1, (decimal)(originalImage.Height * 0.75));
                    }
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "100%":
                    int width100 = originalImage.Width;
                    if (maintainAspectRatio)
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width100);
                        numericHeight.Value = Math.Max(1, (decimal)Math.Round((double)width100 / aspectRatio));
                    }
                    else
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width100);
                        numericHeight.Value = Math.Max(1, (decimal)originalImage.Height);
                    }
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "125%":
                    // 检查是否是放大操作
                    if (originalImage.Width * 1.25 > originalImage.Width && comboBoxResizeMethod.SelectedIndex != 0)
                    {
                        comboBoxResizeMethod.SelectedIndex = 0; // 设置为高质量
                    }

                    int width125 = (int)(originalImage.Width * 1.25);
                    if (maintainAspectRatio)
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width125);
                        numericHeight.Value = Math.Max(1, (decimal)Math.Round((double)width125 / aspectRatio));
                    }
                    else
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width125);
                        numericHeight.Value = Math.Max(1, (decimal)(originalImage.Height * 1.25));
                    }
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "150%":
                    // 检查是否是放大操作
                    if (originalImage.Width * 1.5 > originalImage.Width && comboBoxResizeMethod.SelectedIndex != 0)
                    {
                        comboBoxResizeMethod.SelectedIndex = 0; // 设置为高质量
                    }

                    int width150 = (int)(originalImage.Width * 1.5);
                    if (maintainAspectRatio)
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width150);
                        numericHeight.Value = Math.Max(1, (decimal)Math.Round((double)width150 / aspectRatio));
                    }
                    else
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width150);
                        numericHeight.Value = Math.Max(1, (decimal)(originalImage.Height * 1.5));
                    }
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "200%":
                    // 检查是否是放大操作
                    if (originalImage.Width * 2.0 > originalImage.Width && comboBoxResizeMethod.SelectedIndex != 0)
                    {
                        comboBoxResizeMethod.SelectedIndex = 0; // 设置为高质量
                    }

                    int width200 = (int)(originalImage.Width * 2.0);
                    if (maintainAspectRatio)
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width200);
                        numericHeight.Value = Math.Max(1, (decimal)Math.Round((double)width200 / aspectRatio));
                    }
                    else
                    {
                        numericWidth.Value = Math.Max(1, (decimal)width200);
                        numericHeight.Value = Math.Max(1, (decimal)(originalImage.Height * 2.0));
                    }
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "640 x 480":
                    SetPresetSize(640, 480);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "800 x 600":
                    SetPresetSize(800, 600);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "1024 x 768":
                    SetPresetSize(1024, 768);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "1280 x 720 (HD)":
                    SetPresetSize(1280, 720);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "1920 x 1080 (Full HD)":
                    SetPresetSize(1920, 1080);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "2560 x 1440 (QHD)":
                    SetPresetSize(2560, 1440);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "3840 x 2160 (4K UHD)":
                    SetPresetSize(3840, 2160);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
                case "7680 x 4320 (8K UHD)":
                    SetPresetSize(7680, 4320);
                    // 确保更新属性值
                    NewWidth = Math.Max(1, (int)numericWidth.Value);
                    NewHeight = Math.Max(1, (int)numericHeight.Value);
                    break;
            }

            updatingValues = false;
            // 设置需要更新预览的标志，而不是直接调用UpdatePreview
            previewUpdateNeeded = true;
        }

        private void SetPresetSize(int width, int height)
        {
            // 设置updatingValues标志，防止事件循环
            updatingValues = true;

            // 检查是否是放大操作
            bool isEnlarging = width > originalImage.Width || height > originalImage.Height;

            // 如果是放大操作，自动选择高质量调整方法
            if (isEnlarging && comboBoxResizeMethod.SelectedIndex != 0) // 0是高质量选项
            {
                comboBoxResizeMethod.SelectedIndex = 0;
            }

            if (maintainAspectRatio)
            {
                // 始终使用设定的宽度，根据纵横比计算高度
                numericWidth.Value = Math.Max(1, (decimal)width);
                numericHeight.Value = Math.Max(1, (decimal)Math.Round((double)width / aspectRatio));

                // 更新属性值，确保值大于0
                NewWidth = Math.Max(1, (int)numericWidth.Value);
                NewHeight = Math.Max(1, (int)numericHeight.Value);
            }
            else
            {
                // 不保持纵横比时，直接使用设定的宽度和高度
                numericWidth.Value = Math.Max(1, (decimal)width);
                numericHeight.Value = Math.Max(1, (decimal)height);

                // 直接更新属性值，确保值大于0
                NewWidth = Math.Max(1, (int)numericWidth.Value);
                NewHeight = Math.Max(1, (int)numericHeight.Value);
            }

            // 注意：不在这里重置updatingValues标志，而是在调用此方法的地方重置
            // 这样可以确保在设置完值后，调用方可以决定何时重置标志
        }

        // 窗体空闲时检查是否需要更新预览
        private void Application_Idle(object sender, EventArgs e)
        {
            // 如果需要更新预览且当前没有正在进行的更新
            if (previewUpdateNeeded && !isUpdatingPreview)
            {
                previewUpdateNeeded = false;
                UpdatePreview();
            }
        }

        // 在窗体关闭时取消注册Idle事件
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            base.OnFormClosed(e);
            Application.Idle -= Application_Idle;
        }

        private void UpdatePreview()
        {
            // 设置正在更新预览的标志
            isUpdatingPreview = true;

            // 使用独立的进度窗体显示进度
            // 只在第一次调用或进度窗体已关闭时创建新的进度窗体
            if (progressForm == null || progressForm.IsDisposed)
            {
                progressForm = ProgressForm.ShowProgressDialog(this, "正在处理图像预览...", true); // 使用遮罩效果
            }
            else
            {
                // 如果进度窗体已存在，只更新消息
                progressForm.UpdateMessage("正在处理图像预览...");
                progressForm.SetMarqueeStyle();
            }

            // 强制UI更新
            Application.DoEvents();
            this.Refresh();
            Application.DoEvents();

            // 设置updatingValues标志，防止事件循环
            bool wasUpdating = updatingValues;
            updatingValues = true;

            // 在try块外部定义变量，以便在整个方法中使用
            // 初始化为安全的默认值，确保即使try块中出现异常，也能使用有效值
            int newWidth = 1;
            int newHeight = 1;

            try
            {
                // 更新预览信息
                newWidth = Math.Max(1, (int)numericWidth.Value);
                newHeight = Math.Max(1, (int)numericHeight.Value);
                double scalePercentage = Math.Round(((double)newWidth / originalImage.Width) * 100, 1);

                // 更新属性值，确保值大于0
                NewWidth = newWidth;
                NewHeight = newHeight;

                // 更新尺寸信息标签
                labelNewSize.Text = $"新尺寸: {newWidth} x {newHeight} 像素 ({scalePercentage}%)";

                // 释放之前的预览图像
                if (previewPictureBox.Image != null)
                {
                    previewPictureBox.Image.Dispose();
                    previewPictureBox.Image = null;
                }
            }
            catch (Exception ex)
            {
                // 捕获任何异常，确保newWidth和newHeight至少为1
                newWidth = Math.Max(1, newWidth);
                newHeight = Math.Max(1, newHeight);
                MessageBox.Show($"更新预览时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复之前的updatingValues状态
                updatingValues = wasUpdating;
            }

            // 创建预览图像 - 使用实际的调整后尺寸
            // 再次确保宽度和高度至少为1
            newWidth = Math.Max(1, newWidth);
            newHeight = Math.Max(1, newHeight);

            // 检查是否是放大操作
            bool isEnlargement = newWidth > originalImage.Width || newHeight > originalImage.Height;

            // 创建预览图像
            Bitmap previewImage = new Bitmap(newWidth, newHeight);

            // 如果是放大操作且选择了高质量模式，使用超采样技术
            if (isEnlargement && comboBoxResizeMethod.SelectedIndex == 0)
            {
                // 使用超采样技术 - 先放大到目标尺寸的1.5倍，然后缩小到目标尺寸
                int supersampleWidth = (int)(newWidth * 1.5);
                int supersampleHeight = (int)(newHeight * 1.5);

                using (Bitmap supersampleImage = new Bitmap(supersampleWidth, supersampleHeight))
                {
                    // 第一步：放大到超采样尺寸
                    using (Graphics g = Graphics.FromImage(supersampleImage))
                    {
                        g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        g.SmoothingMode = SmoothingMode.HighQuality;
                        g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                        g.CompositingQuality = CompositingQuality.HighQuality;
                        g.CompositingMode = CompositingMode.SourceOver;

                        // 使用高质量参数
                        g.DrawImage(originalImage, 0, 0, supersampleWidth, supersampleHeight);
                    }

                    // 第二步：缩小到目标尺寸（应用锐化）
                    using (Graphics g = Graphics.FromImage(previewImage))
                    {
                        g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                        g.SmoothingMode = SmoothingMode.HighQuality;
                        g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                        g.CompositingQuality = CompositingQuality.HighQuality;
                        g.CompositingMode = CompositingMode.SourceOver;

                        // 应用轻微锐化效果
                        using (System.Drawing.Imaging.ImageAttributes imageAttr = new System.Drawing.Imaging.ImageAttributes())
                        {
                            // 创建锐化矩阵
                            float[][] colorMatrixElements = {
                                new float[] {1.5f,  0,    0,    0,    0},
                                new float[] {0,    1.5f,  0,    0,    0},
                                new float[] {0,     0,   1.5f,  0,    0},
                                new float[] {0,     0,    0,    1,    0},
                                new float[] {-0.15f, -0.15f, -0.15f, 0, 1}
                            };

                            System.Drawing.Imaging.ColorMatrix colorMatrix = new System.Drawing.Imaging.ColorMatrix(colorMatrixElements);
                            imageAttr.SetColorMatrix(colorMatrix, System.Drawing.Imaging.ColorMatrixFlag.Default, System.Drawing.Imaging.ColorAdjustType.Bitmap);

                            g.DrawImage(supersampleImage,
                                new Rectangle(0, 0, newWidth, newHeight),
                                0, 0, supersampleWidth, supersampleHeight,
                                GraphicsUnit.Pixel, imageAttr);
                        }

                        // 添加网格线以便更好地看到图像边界
                        AddGridAndScaleBar(g, newWidth, newHeight, originalImage.Width, originalImage.Height);
                    }
                }
            }
            else
            {
                // 标准调整大小方法
                using (Graphics g = Graphics.FromImage(previewImage))
                {
                    // 设置插值模式（根据选择的调整大小方法）
                    switch (comboBoxResizeMethod.SelectedIndex)
                    {
                        case 0: // 高质量
                            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                            g.SmoothingMode = SmoothingMode.HighQuality;
                            g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                            g.CompositingQuality = CompositingQuality.HighQuality;
                            g.CompositingMode = CompositingMode.SourceOver;
                            break;
                        case 1: // 标准
                            g.InterpolationMode = InterpolationMode.Bilinear;
                            g.SmoothingMode = SmoothingMode.Default;
                            g.PixelOffsetMode = PixelOffsetMode.Default;
                            g.CompositingQuality = CompositingQuality.Default;
                            break;
                        case 2: // 快速
                            g.InterpolationMode = InterpolationMode.NearestNeighbor;
                            g.SmoothingMode = SmoothingMode.HighSpeed;
                            g.PixelOffsetMode = PixelOffsetMode.HighSpeed;
                            g.CompositingQuality = CompositingQuality.HighSpeed;
                            break;
                    }

                    // 绘制调整大小后的图像
                    g.DrawImage(originalImage, 0, 0, newWidth, newHeight);

                    // 添加网格线和比例尺
                    AddGridAndScaleBar(g, newWidth, newHeight, originalImage.Width, originalImage.Height);
                }
            }

            // 估算文件大小
            long estimatedFileSize = EstimateFileSize(previewImage);
            string fileSizeText = FormatFileSize(estimatedFileSize);
            labelFileSize.Text = $"文件大小: {fileSizeText}";

            // 设置预览图像
            previewPictureBox.Image = previewImage;

            // 调整预览图像控件的大小以适应图像
            previewPictureBox.Size = previewImage.Size;

            // 更新进度窗体状态为完成
            if (progressForm != null && !progressForm.IsDisposed)
            {
                progressForm.UpdateMessage("处理完成");
                progressForm.SetContinuousStyle(100, 100);

                // 强制UI更新
                Application.DoEvents();

                // 短暂延迟，让用户看到完成状态
                System.Threading.Thread.Sleep(300);

                // 关闭进度窗体
                progressForm.CloseProgress();
            }

            // 重置正在更新预览的标志
            isUpdatingPreview = false;

            // 将预览图像控件居中显示在面板中
            if (previewPictureBox.Parent is Panel panel)
            {
                // 如果图像小于面板，则居中显示
                if (previewImage.Width < panel.ClientSize.Width)
                {
                    previewPictureBox.Left = (panel.ClientSize.Width - previewImage.Width) / 2;
                }
                else
                {
                    previewPictureBox.Left = 0;
                }

                if (previewImage.Height < panel.ClientSize.Height)
                {
                    previewPictureBox.Top = (panel.ClientSize.Height - previewImage.Height) / 2;
                }
                else
                {
                    previewPictureBox.Top = 0;
                }
            }
        }

        // 估算文件大小
        private long EstimateFileSize(Bitmap image)
        {
            if (image == null) return 0;

            // 使用内存流来估算文件大小
            using (MemoryStream ms = new MemoryStream())
            {
                // 保存为JPEG格式，质量为85%
                image.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                return ms.Length;
            }
        }

        // 格式化文件大小显示
        private string FormatFileSize(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;

            while (number > 1024 && counter < suffixes.Length - 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{Math.Round(number, 2)} {suffixes[counter]}";
        }

        private void buttonOK_Click(object sender, EventArgs e)
        {
            // 保存新的宽度和高度（确保至少为1）
            NewWidth = Math.Max(1, (int)numericWidth.Value);
            NewHeight = Math.Max(1, (int)numericHeight.Value);

            // 生成调整大小后的图像
            ResultImage = GetResultImage();

            // 释放预览图像资源
            if (previewPictureBox != null && previewPictureBox.Image != null)
            {
                previewPictureBox.Image.Dispose();
            }

            DialogResult = DialogResult.OK;
            Close();
        }

        public Image GetResultImage()
        {
            // 确保宽度和高度至少为1
            int width = Math.Max(1, NewWidth);
            int height = Math.Max(1, NewHeight);

            // 检查是否是放大操作
            bool isEnlargement = width > originalImage.Width || height > originalImage.Height;

            // 根据选择的调整大小方法创建新图像
            Bitmap resizedImage = new Bitmap(width, height);

            // 如果是放大操作且选择了高质量模式，使用超采样技术
            if (isEnlargement && comboBoxResizeMethod.SelectedIndex == 0)
            {
                // 使用超采样技术 - 先放大到目标尺寸的1.5倍，然后缩小到目标尺寸
                int supersampleWidth = (int)(width * 1.5);
                int supersampleHeight = (int)(height * 1.5);

                using (Bitmap supersampleImage = new Bitmap(supersampleWidth, supersampleHeight))
                {
                    // 第一步：放大到超采样尺寸
                    using (Graphics g = Graphics.FromImage(supersampleImage))
                    {
                        g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                        g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                        g.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceOver;

                        // 使用高质量参数
                        g.DrawImage(originalImage, 0, 0, supersampleWidth, supersampleHeight);
                    }

                    // 第二步：缩小到目标尺寸（应用锐化）
                    using (Graphics g = Graphics.FromImage(resizedImage))
                    {
                        g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                        g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                        g.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceOver;

                        // 应用轻微锐化效果
                        using (System.Drawing.Imaging.ImageAttributes imageAttr = new System.Drawing.Imaging.ImageAttributes())
                        {
                            // 创建锐化矩阵
                            float[][] colorMatrixElements = {
                                new float[] {1.5f,  0,    0,    0,    0},
                                new float[] {0,    1.5f,  0,    0,    0},
                                new float[] {0,     0,   1.5f,  0,    0},
                                new float[] {0,     0,    0,    1,    0},
                                new float[] {-0.15f, -0.15f, -0.15f, 0, 1}
                            };

                            System.Drawing.Imaging.ColorMatrix colorMatrix = new System.Drawing.Imaging.ColorMatrix(colorMatrixElements);
                            imageAttr.SetColorMatrix(colorMatrix, System.Drawing.Imaging.ColorMatrixFlag.Default, System.Drawing.Imaging.ColorAdjustType.Bitmap);

                            g.DrawImage(supersampleImage,
                                new Rectangle(0, 0, width, height),
                                0, 0, supersampleWidth, supersampleHeight,
                                GraphicsUnit.Pixel, imageAttr);
                        }
                    }
                }
            }
            else
            {
                // 标准调整大小方法
                using (Graphics g = Graphics.FromImage(resizedImage))
                {
                    // 设置插值模式（根据选择的调整大小方法）
                    switch (comboBoxResizeMethod.SelectedIndex)
                    {
                        case 0: // 高质量
                            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                            g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                            g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                            g.CompositingMode = System.Drawing.Drawing2D.CompositingMode.SourceOver;
                            break;
                        case 1: // 标准
                            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.Bilinear;
                            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.Default;
                            g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.Default;
                            g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.Default;
                            break;
                        case 2: // 快速
                            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.NearestNeighbor;
                            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighSpeed;
                            g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighSpeed;
                            g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighSpeed;
                            break;
                    }

                    // 绘制调整大小后的图像
                    g.DrawImage(originalImage, 0, 0, width, height);
                }
            }

            return resizedImage;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            // 释放预览图像资源
            if (previewPictureBox != null && previewPictureBox.Image != null)
            {
                previewPictureBox.Image.Dispose();
            }

            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void comboBoxResizeMethod_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 如果正在更新值，不处理此事件
            if (updatingValues)
            {
                return;
            }

            // 移除重复的进度窗体显示代码，统一在UpdatePreview方法中处理

            // 当调整方法改变时更新预览
            // 设置需要更新预览的标志，而不是直接调用UpdatePreview
            previewUpdateNeeded = true;
        }


        // 添加网格线和比例尺的辅助方法
        private void AddGridAndScaleBar(Graphics g, int width, int height, int origWidth, int origHeight)
        {
            // 添加网格线以便更好地看到图像边界
            using (Pen gridPen = new Pen(Color.LightGray, 1))
            {
                // 绘制水平网格线
                for (int y = 20; y < height; y += 20)
                {
                    g.DrawLine(gridPen, 0, y, width, y);
                }

                // 绘制垂直网格线
                for (int x = 20; x < width; x += 20)
                {
                    g.DrawLine(gridPen, x, 0, x, height);
                }
            }

            // 添加比例尺 - 在图像底部绘制一个100像素的刻度
            int scaleBarLength = 100;
            int scaleBarHeight = 10;
            int scaleBarY = height - scaleBarHeight - 5;
            int scaleBarX = 5;

            // 绘制比例尺背景
            g.FillRectangle(Brushes.White, scaleBarX, scaleBarY, scaleBarLength, scaleBarHeight);

            // 绘制比例尺边框
            using (Pen scalePen = new Pen(Color.Black, 1))
            {
                g.DrawRectangle(scalePen, scaleBarX, scaleBarY, scaleBarLength, scaleBarHeight);

                // 绘制刻度
                for (int i = 0; i <= 10; i++)
                {
                    int x = scaleBarX + (i * 10);
                    int tickHeight = (i % 5 == 0) ? scaleBarHeight : scaleBarHeight / 2;
                    g.DrawLine(scalePen, x, scaleBarY, x, scaleBarY + tickHeight);
                }
            }

            // 添加比例尺文字
            using (Font font = new Font("Arial", 7))
            {
                g.DrawString("100px", font, Brushes.Black, scaleBarX + scaleBarLength + 2, scaleBarY);
            }

            // 如果图像尺寸发生了变化，绘制原始尺寸的轮廓
            if (width != origWidth || height != origHeight)
            {
                // 绘制原始尺寸的轮廓
                using (Pen outlinePen = new Pen(Color.Red, 2))
                {
                    // 如果原始图像比新图像大，绘制新图像在原始图像中的位置
                    if (origWidth > width || origHeight > height)
                    {
                        // 绘制一个红色虚线框，表示新图像相对于原始图像的大小
                        outlinePen.DashStyle = DashStyle.Dash;
                        g.DrawRectangle(outlinePen, 0, 0, Math.Min(width, origWidth), Math.Min(height, origHeight));

                        // 添加文字说明
                        using (Font font = new Font("Arial", 8))
                        {
                            g.DrawString("原始图像边界", font, Brushes.Red, 5, 5);
                        }
                    }
                    else // 原始图像比新图像小
                    {
                        // 绘制一个红色虚线框，表示原始图像相对于新图像的大小
                        outlinePen.DashStyle = DashStyle.Dash;
                        g.DrawRectangle(outlinePen, 0, 0, origWidth, origHeight);

                        // 添加文字说明
                        using (Font font = new Font("Arial", 8))
                        {
                            g.DrawString("原始图像边界", font, Brushes.Red, 5, 5);
                        }
                    }
                }
            }
        }
    }
}