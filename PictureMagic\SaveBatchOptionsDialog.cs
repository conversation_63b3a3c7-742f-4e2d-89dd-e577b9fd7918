using CommonLib;
using PictureMagic.Common;
using System.Drawing.Imaging;

namespace PictureMagic
{
    public partial class SaveBatchOptionsDialog : Form
    {
        // 原始图像引用
        private Image originalImage;
        // 原始文件路径
        private string originalFilePath;
        // 是否防止覆盖原文件
        private bool preventOverwrite;

        // 公共属性，用于获取对话框结果
        public string SelectedDirPath { get; private set; }
        public ImageFormat SelectedFormat { get; private set; }
        public string FileExtension { get; private set; }
        public EncoderParameters EncoderParams { get; private set; }
        public int JpegQuality { get; private set; } = 100;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="image">要保存的图像</param>
        /// <param name="defaultPath">默认保存路径</param>
        /// <param name="preventOverwrite">是否防止覆盖原文件</param>
        /// <param name="isBatchSave">是否为批量保存模式</param>
        public SaveBatchOptionsDialog(Image image, string defaultPath, bool preventOverwrite = false)
        {
            InitializeComponent();

            this.originalImage = image;
            this.originalFilePath = defaultPath;
            this.preventOverwrite = preventOverwrite;

            // 初始化格式下拉框
            comboBoxFormat.Items.AddRange(new object[] { "JPEG (.jpg)", "PNG (.png)", "GIF (.gif)", "BMP (.bmp)" });
            comboBoxFormat.SelectedIndex = 0; // 默认选择JPEG

            var settings = AppSettings.LoadSettings();
            if (!string.IsNullOrEmpty(settings.LastDirectory))
            {
                textBoxFilePath.Text = settings.LastDirectory;
            }
            else
            {
                textBoxFilePath.Text = defaultPath;
            }

            // 设置JPEG质量滑块的初始值
            trackBarQuality.Value = JpegQuality;
            labelQualityValue.Text = $"{JpegQuality}%";

            if (!Tools.IsRegistered)
            {
                trackBarQuality.Value = 10;
                labelQualityValue.Text = "10%";
                trackBarQuality_Scroll(null, null);
                trackBarQuality.Enabled = false; // 未注册时禁用JPEG质量滑块
                comboBoxFormat.Enabled = false; // 未注册时禁用格式下拉框
            }
            else
            {
                trackBarQuality.Enabled = true; // 注册时启用JPEG质量滑块
                trackBarQuality.Value = 100; // 初始值设置为100%
                comboBoxFormat.Enabled = true; // 注册时启用格式下拉框
            }
            // 更新UI以匹配选定的格式
            UpdateUIForSelectedFormat();

            // 如果有原始图像，显示预览和图像信息
            if (originalImage != null)
            {
                // 显示图像尺寸信息
                labelOriginalSize.Text = $"图像尺寸: {originalImage.Width} x {originalImage.Height} 像素";

                // 调整预览框大小
                AdjustPreviewSize();

                // 更新预览图像
                UpdatePreviewImage();

                // 更新文件大小预估
                UpdateFileSizeEstimate();
            }
        }

        private void UpdateUIForSelectedFormat()
        {
            // 根据选择的格式启用或禁用质量控制
            bool isJpeg = comboBoxFormat.SelectedIndex == 0;
            labelQuality.Enabled = isJpeg;
            trackBarQuality.Enabled = isJpeg && Tools.IsRegistered;
            labelQualityValue.Enabled = isJpeg;


            // 更新预览图像以反映新的格式
            UpdatePreviewImage();

            // 更新文件大小预估
            UpdateFileSizeEstimate();
        }

        private string GetExtensionForSelectedFormat()
        {
            switch (comboBoxFormat.SelectedIndex)
            {
                case 0: return ".jpg";
                case 1: return ".png";
                case 2: return ".gif";
                case 3: return ".bmp";
                default: return ".jpg";
            }
        }

        private ImageFormat GetSelectedImageFormat()
        {
            switch (comboBoxFormat.SelectedIndex)
            {
                case 0: return ImageFormat.Jpeg;
                case 1: return ImageFormat.Png;
                case 2: return ImageFormat.Gif;
                case 3: return ImageFormat.Bmp;
                default: return ImageFormat.Jpeg;
            }
        }

        private void UpdateFileSizeEstimate()
        {
            if (originalImage == null) return;

            try
            {
                // 使用内存流估算文件大小
                using (MemoryStream ms = new MemoryStream())
                {
                    // 根据选择的格式和质量设置保存图像
                    if (comboBoxFormat.SelectedIndex == 0) // JPEG
                    {
                        EncoderParameters encoderParams = new EncoderParameters(1);
                        encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, (long)JpegQuality);

                        ImageCodecInfo jpegCodec = GetEncoderInfo("image/jpeg");
                        originalImage.Save(ms, jpegCodec, encoderParams);
                    }
                    else
                    {
                        // 对于其他格式，使用标准保存方法
                        originalImage.Save(ms, GetSelectedImageFormat());
                    }

                    // 显示估计的文件大小
                    labelEstimatedSize.Text = $"预估文件大小: {FormatFileSize(ms.Length)}";
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"估算文件大小出错: {ex.Message}");
                labelEstimatedSize.Text = "预估文件大小: 未知";
            }
            finally
            {
                // 不需要进度提示
            }
        }

        private ImageCodecInfo GetEncoderInfo(string mimeType)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.MimeType == mimeType)
                {
                    return codec;
                }
            }
            return null;
        }

        private string FormatFileSize(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;

            while (number > 1024 && counter < suffixes.Length - 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{Math.Round(number, 2)} {suffixes[counter]}";
        }

        private void buttonBrowse_Click(object sender, EventArgs e)
        {
            using (FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog())
            {
                // 设置对话框属性
                folderBrowserDialog.Description = "请选择保存文件夹";
                folderBrowserDialog.ShowNewFolderButton = true;
                folderBrowserDialog.SelectedPath = textBoxFilePath.Text;
                // 显示对话框并获取选择结果
                DialogResult result = folderBrowserDialog.ShowDialog();
                if (result == DialogResult.OK)
                {
                    textBoxFilePath.Text = folderBrowserDialog.SelectedPath;
                    var settings = AppSettings.LoadSettings();
                    settings.LastDirectory = folderBrowserDialog.SelectedPath;
                    AppSettings.SaveSettings(settings);
                }
            }
        }

        private void comboBoxFormat_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdateUIForSelectedFormat();
        }

        private void trackBarQuality_Scroll(object sender, EventArgs e)
        {
            JpegQuality = trackBarQuality.Value;
            labelQualityValue.Text = $"{JpegQuality}%";

            // 更新预览图像以反映新的质量设置
            UpdatePreviewImage();

            // 更新文件大小预估
            UpdateFileSizeEstimate();
        }

        private void buttonSave_Click(object sender, EventArgs e)
        {
            // 检查文件路径是否有效
            if (string.IsNullOrWhiteSpace(textBoxFilePath.Text))
            {
                MessageBox.Show("请输入有效的文件路径", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }


            // 设置返回值
            SelectedDirPath = textBoxFilePath.Text;
            SelectedFormat = GetSelectedImageFormat();
            FileExtension = GetExtensionForSelectedFormat();

            // 如果是JPEG格式，设置编码参数
            if (SelectedFormat.Equals(ImageFormat.Jpeg))
            {
                EncoderParams = new EncoderParameters(1);
                // 确保JPEG质量值在有效范围内（0-100），并使用long类型而不是byte
                EncoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, (long)JpegQuality);
            }
            else
            {
                EncoderParams = null;
            }

            DialogResult = DialogResult.OK;
            Close();
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void AdjustPreviewSize()
        {
            if (originalImage == null) return;

            // 设置pictureBoxPreview的尺寸以匹配图像的宽高比
            float imageRatio = (float)originalImage.Width / originalImage.Height;

            // 根据panelPreview的尺寸调整pictureBoxPreview的大小
            if (imageRatio > 1) // 宽图像
            {
                // 宽度设为面板宽度，高度按比例计算
                pictureBoxPreview.Width = panelPreview.ClientSize.Width - 4; // 减去边框宽度
                pictureBoxPreview.Height = (int)(pictureBoxPreview.Width / imageRatio);
            }
            else // 高图像或正方形
            {
                // 高度设为面板高度，宽度按比例计算
                pictureBoxPreview.Height = panelPreview.ClientSize.Height - 4; // 减去边框高度
                pictureBoxPreview.Width = (int)(pictureBoxPreview.Height * imageRatio);
            }

            // 居中显示
            pictureBoxPreview.Left = (panelPreview.ClientSize.Width - pictureBoxPreview.Width) / 2;
            pictureBoxPreview.Top = (panelPreview.ClientSize.Height - pictureBoxPreview.Height) / 2;
        }

        private void UpdatePreviewImage()
        {
            if (originalImage == null) return;

            try
            {
                // 释放之前的预览图像（如果不是原始图像）
                if (pictureBoxPreview.Image != null && pictureBoxPreview.Image != originalImage)
                {
                    pictureBoxPreview.Image.Dispose();
                }

                // 对于JPEG格式，应用质量设置以显示压缩效果
                if (comboBoxFormat.SelectedIndex == 0) // JPEG
                {
                    // 使用内存流和当前质量设置创建JPEG预览
                    using (MemoryStream ms = new MemoryStream())
                    {
                        EncoderParameters encoderParams = new EncoderParameters(1);
                        encoderParams.Param[0] = new EncoderParameter(System.Drawing.Imaging.Encoder.Quality, (long)JpegQuality);

                        ImageCodecInfo jpegCodec = GetEncoderInfo("image/jpeg");
                        originalImage.Save(ms, jpegCodec, encoderParams);

                        // 重置流位置并从流创建新图像
                        ms.Position = 0;
                        Image previewImage = Image.FromStream(ms);
                        pictureBoxPreview.Image = previewImage;
                    }
                }
                else
                {
                    // 对于其他格式，直接显示原始图像
                    // 注意：这里简化处理，实际上不同格式可能有不同的视觉效果
                    pictureBoxPreview.Image = originalImage;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新预览图像出错: {ex.Message}");
                // 出错时使用原始图像
                pictureBoxPreview.Image = originalImage;
            }
            finally
            {
                // 不需要进度提示
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);

            // 释放资源
            if (pictureBoxPreview.Image != null && pictureBoxPreview.Image != originalImage)
            {
                pictureBoxPreview.Image.Dispose();
            }
        }


    }
}