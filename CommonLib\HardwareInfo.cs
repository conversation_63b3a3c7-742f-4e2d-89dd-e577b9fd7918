
using System.Management;

namespace CommonLib
{
    public static class HardwareInfo
    {
        // 获取CPU ID
        public static string GetCpuId()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT ProcessorId FROM Win32_Processor"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        return obj["ProcessorId"].ToString().Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取CPU ID失败: {ex.Message}");
            }
            return string.Empty;
        }

        // 获取主板序列号
        public static string GetMotherboardSerialNumber()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        return obj["SerialNumber"].ToString().Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取主板序列号失败: {ex.Message}");
            }
            return string.Empty;
        }

        // 获取硬盘序列号
        public static string GetDiskSerialNumber()
        {
            try
            {
                using (var searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_DiskDrive"))
                {
                    foreach (var obj in searcher.Get())
                    {
                        return obj["SerialNumber"].ToString().Trim();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取硬盘序列号失败: {ex.Message}");
            }
            return string.Empty;
        }

        // 获取MAC地址
        public static string GetMacAddress()
        {
            try
            {
                var nics = System.Net.NetworkInformation.NetworkInterface.GetAllNetworkInterfaces();
                var mac = nics.Where(nic => nic.OperationalStatus == System.Net.NetworkInformation.OperationalStatus.Up)
                              .Select(nic => nic.GetPhysicalAddress().ToString())
                              .FirstOrDefault();
                return mac ?? string.Empty;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"获取MAC地址失败: {ex.Message}");
            }
            return string.Empty;
        }


        public static string GenerateMachineCode()
        {
            // 组合多个硬件信息，提高唯一性
            string hardwareInfo = $"{GetCpuId()}|{GetMotherboardSerialNumber()}|{GetDiskSerialNumber()}|{GetMacAddress()}";

            // 使用SHA256哈希算法生成固定长度的机器码
            using (var sha256 = System.Security.Cryptography.SHA256.Create())
            {
                var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(hardwareInfo));

                // 直接转换为Base64字符串
                return Convert.ToBase64String(hashBytes);
            }
        }
    }
}