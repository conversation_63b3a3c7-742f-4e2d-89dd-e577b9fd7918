using System;
using System.Drawing;
using System.Windows.Forms;

namespace PictureMagic
{
    /// <summary>
    /// 自定义按钮点击事件参数
    /// </summary>
    public class CustomButtonClickEventArgs : EventArgs
    {
        /// <summary>
        /// 获取或设置用户自定义数据
        /// </summary>
        public object UserData { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="userData">用户自定义数据</param>
        public CustomButtonClickEventArgs(object userData = null)
        {
            UserData = userData;
        }
    }

    /// <summary>
    /// 自定义消息框，类似于MessageBox.Show，但支持添加自定义按钮和事件处理
    /// </summary>
    public partial class CustomMessageBox : Form
    {
        private Label lblMessage;
        private Panel panelButtons;
        private Button btnOK;
        private Button btnCancel;
        private Button btnCustom;
        private PictureBox pictureBoxIcon;

        // 自定义按钮的事件处理委托
        public delegate void CustomButtonClickEventHandler(object sender, CustomButtonClickEventArgs e);

        // 自定义按钮点击事件
        private CustomButtonClickEventHandler customButtonClickHandler;
        
        // 用户自定义数据
        private object userData;

        /// <summary>
        /// 获取对话框的结果
        /// </summary>
        public DialogResult Result { get; private set; }

        /// <summary>
        /// 私有构造函数，防止直接实例化
        /// </summary>
        private CustomMessageBox()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 显示一个自定义消息框
        /// </summary>
        /// <param name="text">消息文本</param>
        /// <param name="caption">标题</param>
        /// <param name="buttons">按钮组合</param>
        /// <param name="icon">图标</param>
        /// <param name="customButtonText">自定义按钮文本</param>
        /// <param name="customButtonClickHandler">自定义按钮点击事件处理程序</param>
        /// <param name="userData">用户自定义数据，将传递给自定义按钮点击事件处理程序</param>
        /// <returns>对话框结果</returns>
        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons, MessageBoxIcon icon, string customButtonText, CustomButtonClickEventHandler customButtonClickHandler, object userData = null)
        {
            using (CustomMessageBox messageBox = new CustomMessageBox())
            {
                messageBox.Text = caption;
                messageBox.lblMessage.Text = text;
                messageBox.SetupButtons(buttons);
                messageBox.SetupIcon(icon);

                // 设置自定义按钮
                if (!string.IsNullOrEmpty(customButtonText))
                {
                    messageBox.btnCustom.Text = customButtonText;
                    messageBox.btnCustom.Visible = true;
                    messageBox.customButtonClickHandler = customButtonClickHandler;
                    messageBox.userData = userData;
                }
                else
                {
                    messageBox.btnCustom.Visible = false;
                }

                // 显示对话框
                messageBox.ShowDialog();
                return messageBox.Result;
            }
        }

        /// <summary>
        /// 显示一个自定义消息框（不带自定义按钮）
        /// </summary>
        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons, MessageBoxIcon icon)
        {
            return Show(text, caption, buttons, icon, null, null, null);
        }

        /// <summary>
        /// 显示一个自定义消息框（只带消息文本）
        /// </summary>
        public static DialogResult Show(string text)
        {
            return Show(text, "", MessageBoxButtons.OK, MessageBoxIcon.None, null, null, null);
        }

        /// <summary>
        /// 显示一个自定义消息框（带消息文本和标题）
        /// </summary>
        public static DialogResult Show(string text, string caption)
        {
            return Show(text, caption, MessageBoxButtons.OK, MessageBoxIcon.None, null, null, null);
        }

        /// <summary>
        /// 显示一个自定义消息框（带消息文本、标题和按钮）
        /// </summary>
        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons)
        {
            return Show(text, caption, buttons, MessageBoxIcon.None, null, null, null);
        }

        /// <summary>
        /// 设置按钮
        /// </summary>
        private void SetupButtons(MessageBoxButtons buttons)
        {
            // 按钮间距
            const int buttonGap = 10;
            // 按钮宽度
            int buttonWidth = 90;
            // 按钮Y坐标
            int buttonY = 15;
            // 可用宽度（考虑面板的内边距）
            int availableWidth = panelButtons.Width - panelButtons.Padding.Left - panelButtons.Padding.Right;

            // 根据按钮类型设置按钮的可见性和位置
            switch (buttons)
            {
                case MessageBoxButtons.OK:
                    btnOK.Visible = true;
                    btnCancel.Visible = false;
                    btnCustom.Visible = false;
                    // 单个按钮居中
                    btnOK.Location = new Point(panelButtons.Padding.Left + (availableWidth - buttonWidth) / 2, buttonY);
                    break;

                case MessageBoxButtons.OKCancel:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = false;
                    // 两个按钮，右对齐
                    btnCancel.Location = new Point(panelButtons.Width - panelButtons.Padding.Right - buttonWidth, buttonY);
                    btnOK.Location = new Point(btnCancel.Left - buttonGap - buttonWidth, buttonY);
                    break;

                case MessageBoxButtons.YesNo:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = false;
                    btnOK.Text = "是(&Y)";
                    btnCancel.Text = "否(&N)";
                    btnOK.DialogResult = DialogResult.Yes;
                    btnCancel.DialogResult = DialogResult.No;
                    // 两个按钮，右对齐
                    btnCancel.Location = new Point(panelButtons.Width - panelButtons.Padding.Right - buttonWidth, buttonY);
                    btnOK.Location = new Point(btnCancel.Left - buttonGap - buttonWidth, buttonY);
                    break;

                case MessageBoxButtons.YesNoCancel:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = true;
                    btnOK.Text = "是(&Y)";
                    btnCancel.Text = "否(&N)";
                    btnCustom.Text = "取消";
                    btnOK.DialogResult = DialogResult.Yes;
                    btnCancel.DialogResult = DialogResult.No;
                    btnCustom.DialogResult = DialogResult.Cancel;
                    // 三个按钮，均匀分布
                    int threeButtonTotalWidth = 3 * buttonWidth + 2 * buttonGap;
                    int threeButtonStartX = panelButtons.Padding.Left + (availableWidth - threeButtonTotalWidth) / 2;
                    btnOK.Location = new Point(threeButtonStartX, buttonY);
                    btnCancel.Location = new Point(btnOK.Right + buttonGap, buttonY);
                    btnCustom.Location = new Point(btnCancel.Right + buttonGap, buttonY);
                    break;

                case MessageBoxButtons.RetryCancel:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = false;
                    btnOK.Text = "重试(&R)";
                    btnOK.DialogResult = DialogResult.Retry;
                    // 两个按钮，右对齐
                    btnCancel.Location = new Point(panelButtons.Width - panelButtons.Padding.Right - buttonWidth, buttonY);
                    btnOK.Location = new Point(btnCancel.Left - buttonGap - buttonWidth, buttonY);
                    break;

                case MessageBoxButtons.AbortRetryIgnore:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = true;
                    btnOK.Text = "中止(&A)";
                    btnCancel.Text = "重试(&R)";
                    btnCustom.Text = "忽略(&I)";
                    btnOK.DialogResult = DialogResult.Abort;
                    btnCancel.DialogResult = DialogResult.Retry;
                    btnCustom.DialogResult = DialogResult.Ignore;
                    // 三个按钮，均匀分布
                    int abortRetryIgnoreTotalWidth = 3 * buttonWidth + 2 * buttonGap;
                    int abortRetryIgnoreStartX = panelButtons.Padding.Left + (availableWidth - abortRetryIgnoreTotalWidth) / 2;
                    btnOK.Location = new Point(abortRetryIgnoreStartX, buttonY);
                    btnCancel.Location = new Point(btnOK.Right + buttonGap, buttonY);
                    btnCustom.Location = new Point(btnCancel.Right + buttonGap, buttonY);
                    break;
            }

            // 如果有自定义按钮，调整其他按钮的位置
            if (btnCustom.Visible && buttons != MessageBoxButtons.YesNoCancel && buttons != MessageBoxButtons.AbortRetryIgnore)
            {
                int buttonCount = (btnOK.Visible ? 1 : 0) + (btnCancel.Visible ? 1 : 0) + 1; // +1 for custom button
                int totalWidth = buttonCount * buttonWidth + (buttonCount - 1) * buttonGap;
                int startX = panelButtons.Padding.Left + (availableWidth - totalWidth) / 2;

                // 自定义按钮放在左侧
                btnCustom.Location = new Point(startX, buttonY);
                startX += buttonWidth + buttonGap;

                if (btnOK.Visible)
                {
                    btnOK.Location = new Point(startX, buttonY);
                    startX += buttonWidth + buttonGap;
                }

                if (btnCancel.Visible)
                 {
                     btnCancel.Location = new Point(startX, buttonY);
                 }
             }
        }

        /// <summary>
        /// 设置图标
        /// </summary>
        private void SetupIcon(MessageBoxIcon icon)
        {
            // 根据图标类型设置图标
            switch (icon)
            {
                case MessageBoxIcon.Information:
                    pictureBoxIcon.Image = SystemIcons.Information.ToBitmap();
                    pictureBoxIcon.Visible = true;
                    break;

                case MessageBoxIcon.Warning:
                    pictureBoxIcon.Image = SystemIcons.Warning.ToBitmap();
                    pictureBoxIcon.Visible = true;
                    break;

                case MessageBoxIcon.Error:
                    pictureBoxIcon.Image = SystemIcons.Error.ToBitmap();
                    pictureBoxIcon.Visible = true;
                    break;

                case MessageBoxIcon.Question:
                    pictureBoxIcon.Image = SystemIcons.Question.ToBitmap();
                    pictureBoxIcon.Visible = true;
                    break;

                default:
                    pictureBoxIcon.Visible = false;
                    break;
            }

            // 调整消息标签的位置和大小
            const int messageVerticalMargin = 20; // 消息标签上下边距
            int messageY = messageVerticalMargin; // 顶部边距
            int messageHeight = panelButtons.Location.Y - messageY - messageVerticalMargin; // 留出底部间距

            if (pictureBoxIcon.Visible)
            {
                // 确保图标垂直居中对齐
                pictureBoxIcon.Location = new Point(pictureBoxIcon.Location.X, (panelButtons.Location.Y - pictureBoxIcon.Height) / 2);
                
                // 图标可见时，消息标签位于图标右侧
                int messageX = pictureBoxIcon.Right + 70; // 增加图标和文本之间的间距
                int messageWidth = this.ClientSize.Width - messageX - 20; // 右侧留出间距
                lblMessage.Location = new Point(messageX, messageY);
                lblMessage.Size = new Size(messageWidth, messageHeight);
                
                // 调整消息标签的垂直位置，使其与图标垂直居中对齐
                if (lblMessage.Height < pictureBoxIcon.Height)
                {
                    lblMessage.Location = new Point(messageX, (panelButtons.Location.Y - lblMessage.Height) / 2);
                }
            }
            else
            {
                // 图标不可见时，消息标签占据整个宽度
                int messageX = 20; // 增加左侧边距
                int messageWidth = this.ClientSize.Width - 2 * messageX;
                lblMessage.Location = new Point(messageX, messageY);
                lblMessage.Size = new Size(messageWidth, messageHeight);
            }
        }

        // InitializeComponent 方法已移动到 CustomMessageBox.Designer.cs 文件中

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.Result = btnOK.DialogResult;
            this.Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Result = btnCancel.DialogResult;
            this.Close();
        }

        /// <summary>
        /// 自定义按钮点击事件
        /// </summary>
        private void btnCustom_Click(object sender, EventArgs e)
        {
            // 如果有自定义事件处理程序，则调用它
            if (customButtonClickHandler != null)
            {
                // 创建自定义事件参数并传递用户数据
                CustomButtonClickEventArgs args = new CustomButtonClickEventArgs(userData);
                customButtonClickHandler(this, args);
            }

            // 如果自定义按钮有DialogResult，则设置结果并关闭对话框
            if (btnCustom.DialogResult != DialogResult.None)
            {
                this.Result = btnCustom.DialogResult;
                this.Close();
            }
        }
    }
}