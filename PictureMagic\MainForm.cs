using CommonLib;
using PictureMagic.Common;
using System.Diagnostics;
using System.Drawing.Imaging;

namespace PictureMagic
{
    public partial class MainForm : Form
    {
        private Image? currentImage = null;
        private Image? originalImage = null;
        private string currentImagePath = string.Empty;
        private List<Image> recentImages = new List<Image>(); // 用于撤销功能的图像历史
        private List<Image> redoImages = new List<Image>(); // 用于重做功能的图像历史
        private int maxRecentImages = 20; // 增加历史记录数量
        private bool imageModified = false; // 跟踪图像是否被修改

        // 进度提示相关字段已移除，现在使用ProgressForm
        private MemoryStream currentImageMemoryStream;

        public MainForm()
        {
            InitializeComponent();
            InitializeUI();
            LoadSampleImage();

            // 初始化菜单项状态
            UpdateMenuItemsState();

        }


        private void InitializeUI()
        {
            // 设置窗体图标
            try
            {
                string iconPath = Path.Combine(Application.StartupPath, "Resources", "Icons", "app_icon.ico");
                if (File.Exists(iconPath))
                {
                    this.Icon = new Icon(iconPath);
                }

                // 设置工具栏按钮图标
                SetToolbarIcons();

                // 配置splitContainerMain的左侧面板
                ConfigureLeftPanel();

                // 确保分隔条位置正确
                splitContainerMain.SplitterDistance = 200;

                // 确保左侧面板可见
                splitContainerMain.Panel1Collapsed = false;

                // 初始化滤镜列表（动态加载）
                InitializeFiltersPanel();

                // 初始化进度提示控件
                InitializeProgressControls();

                lblCopyRight.Text = $"©{DateTime.Now.Year} New Army Software Development Studio, All rights reserved.";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载图标出错: {ex.Message}");
            }
        }


        private void SetToolbarIcons()
        {
            try
            {

                // 设置工具栏按钮图标
                SetToolStripButtonIcon(toolStripButtonOpen, "open");
                SetToolStripButtonIcon(toolStripButtonSave, "save");
                SetToolStripButtonIcon(toolStripButtonClose, "close");
                SetToolStripButtonIcon(toolStripButtonUndo, "undo");
                SetToolStripButtonIcon(toolStripButtonRedo, "redo");
                SetToolStripButtonIcon(toolStripButtonRotateLeft, "rotate_left");
                SetToolStripButtonIcon(toolStripButtonRotateRight, "rotate_right");
                SetToolStripButtonIcon(toolStripButtonFlipH, "flip_horizontal");
                SetToolStripButtonIcon(toolStripButtonFlipV, "flip_vertical");
                SetToolStripButtonIcon(toolStripButtonGrayscale, "grayscale");
                SetToolStripButtonIcon(toolStripButtonInvert, "invert");
                SetToolStripButtonIcon(toolStripButtonCrop, "crop");
                SetToolStripButtonIcon(toolStripButtonResize, "resize");
                SetToolStripButtonIcon(toolStripButtonImageMosaic, "image_mosaic");
                SetToolStripButtonIcon(toolStripButtonAbout, "about");


            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置工具栏图标时发生错误: {ex.Message}");
            }
        }



        private void aboutMenuItem_Click(object sender, EventArgs e)
        {
            using (AboutDialog aboutDialog = new AboutDialog())
            {
                aboutDialog.ShowDialog();
            }
        }

        private void SetToolStripButtonIcon(ToolStripButton button, string iconName)
        {
            try
            {
                Console.WriteLine($"设置按钮图标: {button.Name}, 图标名称: {iconName}");

                // 获取工具栏的图标大小
                Size iconSize = toolStripMain.ImageScalingSize;
                Console.WriteLine($"工具栏图标大小: {iconSize.Width}x{iconSize.Height}");

                // 首先尝试从文件加载图标
                Bitmap icon = LoadIconFromSvgFile(iconName);

                // 如果文件加载失败，则使用绘制方法创建图标
                if (icon == null)
                {
                    Console.WriteLine($"从文件加载图标失败，使用绘制方法创建图标: {iconName}");
                    icon = CreateIconByName(iconName);

                    if (icon == null)
                    {
                        Console.WriteLine($"绘制图标也失败: {iconName}");
                        throw new Exception("无法创建图标");
                    }
                    else
                    {
                        Console.WriteLine($"成功绘制图标: {iconName}, 大小: {icon.Width}x{icon.Height}");
                    }
                }
                else
                {
                    Console.WriteLine($"成功从文件加载图标: {iconName}, 大小: {icon.Width}x{icon.Height}");
                }

                // 如果图标大小与工具栏按钮大小不匹配，则调整大小
                if (icon.Size != iconSize)
                {
                    Console.WriteLine($"调整图标大小: {icon.Width}x{icon.Height} -> {iconSize.Width}x{iconSize.Height}");
                    Bitmap resizedIcon = new Bitmap(iconSize.Width, iconSize.Height);
                    using (Graphics g = Graphics.FromImage(resizedIcon))
                    {
                        g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        g.DrawImage(icon, 0, 0, iconSize.Width, iconSize.Height);
                    }
                    button.Image = resizedIcon;
                    icon.Dispose(); // 释放原始图标资源
                    Console.WriteLine($"成功设置调整大小后的图标: {button.Name}");
                }
                else
                {
                    button.Image = icon;
                    Console.WriteLine($"成功设置原始大小的图标: {button.Name}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"创建图标 {iconName} 出错: {ex.Message}");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");

                // 创建一个简单的默认图标
                try
                {
                    Console.WriteLine($"创建默认图标: {iconName}");
                    Size iconSize = toolStripMain.ImageScalingSize;
                    Bitmap defaultIcon = new Bitmap(iconSize.Width, iconSize.Height);
                    using (Graphics g = Graphics.FromImage(defaultIcon))
                    {
                        g.Clear(Color.LightGray);
                        g.DrawRectangle(new Pen(Color.DarkGray), 0, 0, iconSize.Width - 1, iconSize.Height - 1);

                        // 在图标中心绘制图标名称的首字母
                        using (Font font = new Font("Arial", 10, FontStyle.Bold))
                        {
                            string letter = iconName.Length > 0 ? iconName[0].ToString().ToUpper() : "?";
                            SizeF textSize = g.MeasureString(letter, font);
                            g.DrawString(letter, font, Brushes.Black,
                                (iconSize.Width - textSize.Width) / 2,
                                (iconSize.Height - textSize.Height) / 2);
                        }
                    }
                    button.Image = defaultIcon;
                    Console.WriteLine($"成功创建并设置默认图标: {button.Name}");
                }
                catch (Exception innerEx)
                {
                    Console.WriteLine($"创建默认图标时出错: {innerEx.Message}");
                }
            }
        }

        private Bitmap? LoadIconFromSvgFile(string iconName)
        {
            try
            {
                Console.WriteLine($"开始加载图标: {iconName}");

                // 首先尝试加载PNG文件（作为备选方案）
                string pngPath = Path.Combine(Application.StartupPath, "Resources", "Icons", $"{iconName}.png");
                Console.WriteLine($"检查PNG文件路径: {pngPath}");

                if (File.Exists(pngPath))
                {
                    Console.WriteLine($"PNG文件存在: {pngPath}");
                    try
                    {
                        // 删除现有的PNG文件，强制使用绘制方法
                        // 这是一个临时解决方案，以确保我们使用最新的图标设计
                        File.Delete(pngPath);
                        Console.WriteLine($"已删除PNG文件以强制使用绘制方法: {pngPath}");
                        return null;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"删除PNG文件 {pngPath} 出错: {ex.Message}");
                        Console.WriteLine($"异常类型: {ex.GetType().Name}");
                        Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                    }
                }
                else
                {
                    Console.WriteLine($"PNG文件不存在: {pngPath}");
                }

                // 然后尝试加载SVG文件
                string svgPath = Path.Combine(Application.StartupPath, "Resources", "Icons", $"{iconName}.svg");
                Console.WriteLine($"检查SVG文件路径: {svgPath}");

                if (File.Exists(svgPath))
                {
                    Console.WriteLine($"SVG文件存在: {svgPath}，但System.Drawing不直接支持SVG格式");
                    Console.WriteLine($"将使用绘制方法创建图标: {iconName}");
                    // 我们不尝试加载SVG文件，因为System.Drawing不支持SVG格式
                    // 直接返回null，让应用程序使用绘制方法
                    return null;
                }
                else
                {
                    Console.WriteLine($"SVG文件不存在: {svgPath}");
                }

                Console.WriteLine($"无法加载图标: {iconName}，将返回null");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载图标 {iconName} 出错: {ex.Message}");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            return null; // 如果加载失败，返回null
        }

        private Bitmap CreateIconByName(string iconName)
        {
            // 创建一个24x24的图标
            Bitmap icon = new Bitmap(24, 24);
            using (Graphics g = Graphics.FromImage(icon))
            {
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                g.Clear(Color.Transparent);

                // 根据图标名称绘制不同的图标
                switch (iconName)
                {
                    case "open":
                        DrawOpenIcon(g);
                        break;
                    case "save":
                        DrawSaveIcon(g);
                        break;
                    case "close":
                        DrawCloseIcon(g);
                        break;
                    case "undo":
                        DrawUndoIcon(g);
                        break;
                    case "redo":
                        DrawRedoIcon(g);
                        break;
                    case "rotate_left":
                        DrawRotateLeftIcon(g);
                        break;
                    case "rotate_right":
                        DrawRotateRightIcon(g);
                        break;
                    case "flip_horizontal":
                        DrawFlipHorizontalIcon(g);
                        break;
                    case "flip_vertical":
                        DrawFlipVerticalIcon(g);
                        break;
                    case "about":
                        DrawAboutIcon(g);
                        break;
                    case "image_mosaic":
                        DrawImageMosaicIcon(g);
                        break;
                    case "grayscale":
                        DrawGrayscaleIcon(g);
                        break;
                    case "invert":
                        DrawInvertIcon(g);
                        break;
                    case "crop":
                        DrawCropIcon(g);
                        break;
                    case "resize":
                        DrawResizeIcon(g);
                        break;
                    default:
                        // 默认图标
                        g.DrawRectangle(new Pen(Color.DarkGray, 2), 2, 2, 20, 20);
                        break;
                }
            }
            return icon;
        }

        // 绘制打开图标
        private void DrawOpenIcon(Graphics g)
        {
            // 文件夹图标
            using (SolidBrush brush = new SolidBrush(Color.FromArgb(255, 222, 173)))
            using (Pen pen = new Pen(Color.FromArgb(210, 180, 140), 1.5f))
            {
                // 文件夹底部
                g.FillRectangle(brush, 2, 7, 20, 15);
                g.DrawRectangle(pen, 2, 7, 20, 15);

                // 文件夹顶部
                g.FillRectangle(brush, 2, 4, 10, 3);
                g.DrawRectangle(pen, 2, 4, 10, 3);
            }
        }

        // 绘制保存图标
        private void DrawSaveIcon(Graphics g)
        {
            using (SolidBrush brush = new SolidBrush(Color.FromArgb(135, 206, 250)))
            using (Pen pen = new Pen(Color.FromArgb(70, 130, 180), 1.5f))
            {
                // 保存图标主体
                g.FillRectangle(brush, 3, 3, 18, 18);
                g.DrawRectangle(pen, 3, 3, 18, 18);

                // 顶部条
                g.FillRectangle(new SolidBrush(Color.White), 6, 5, 12, 6);
                g.DrawRectangle(pen, 6, 5, 12, 6);

                // 底部条
                g.FillRectangle(brush, 6, 14, 12, 5);
                g.DrawRectangle(pen, 6, 14, 12, 5);
            }
        }

        // 绘制向左旋转图标
        private void DrawRotateLeftIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(51, 51, 51), 2)) // 黑色
            {
                // 画圆
                g.DrawEllipse(pen, 4, 4, 16, 16);

                // 画旋转箭头
                g.DrawArc(pen, 7, 7, 10, 10, 120, 300);

                // 画箭头头部
                g.DrawLine(pen, 7, 14, 4, 12);
                g.DrawLine(pen, 7, 14, 4, 16);
            }
        }

        // 绘制向右旋转图标
        private void DrawRotateRightIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(51, 51, 51), 2)) // 黑色
            {
                // 画圆
                g.DrawEllipse(pen, 4, 4, 16, 16);

                // 画旋转箭头
                g.DrawArc(pen, 7, 7, 10, 10, 120, -300);

                // 画箭头头部
                g.DrawLine(pen, 17, 14, 20, 12);
                g.DrawLine(pen, 17, 14, 20, 16);
            }
        }

        // 绘制水平翻转图标
        private void DrawFlipHorizontalIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(70, 130, 180), 2))
            {
                // 中线
                g.DrawLine(pen, 12, 3, 12, 21);

                // 左箭头
                g.DrawLine(pen, 3, 12, 9, 12);
                g.DrawLine(pen, 3, 12, 6, 9);
                g.DrawLine(pen, 3, 12, 6, 15);

                // 右箭头
                g.DrawLine(pen, 21, 12, 15, 12);
                g.DrawLine(pen, 21, 12, 18, 9);
                g.DrawLine(pen, 21, 12, 18, 15);
            }
        }

        // 绘制垂直翻转图标
        private void DrawFlipVerticalIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(70, 130, 180), 2))
            {
                // 中线
                g.DrawLine(pen, 3, 12, 21, 12);

                // 上箭头
                g.DrawLine(pen, 12, 3, 12, 9);
                g.DrawLine(pen, 12, 3, 9, 6);
                g.DrawLine(pen, 12, 3, 15, 6);

                // 下箭头
                g.DrawLine(pen, 12, 21, 12, 15);
                g.DrawLine(pen, 12, 21, 9, 18);
                g.DrawLine(pen, 12, 21, 15, 18);
            }
        }

        // 绘制撤销图标
        private void DrawUndoIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(65, 105, 225), 2)) // 蓝色 #4169E1
            {
                // 画弧形箭头
                g.DrawArc(pen, 5, 5, 14, 14, 45, 270);

                // 画箭头头部
                g.DrawLine(pen, 5, 12, 9, 9);
                g.DrawLine(pen, 5, 12, 9, 15);

                // 画短线
                g.DrawLine(pen, 14, 5, 19, 5);
            }
        }

        // 绘制重做图标
        private void DrawRedoIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(153, 50, 204), 2)) // 紫色 #9932CC
            {
                // 画弧形箭头
                g.DrawArc(pen, 5, 5, 14, 14, 225, 270);

                // 画箭头头部
                g.DrawLine(pen, 19, 12, 15, 9);
                g.DrawLine(pen, 19, 12, 15, 15);

                // 画短线
                g.DrawLine(pen, 10, 5, 5, 5);
            }
        }

        // 绘制灰度图标
        private void DrawGrayscaleIcon(Graphics g)
        {
            // 绘制四个不同灰度的方块
            g.FillRectangle(new SolidBrush(Color.FromArgb(255, 255, 255)), 3, 3, 9, 9);
            g.FillRectangle(new SolidBrush(Color.FromArgb(180, 180, 180)), 12, 3, 9, 9);
            g.FillRectangle(new SolidBrush(Color.FromArgb(120, 120, 120)), 3, 12, 9, 9);
            g.FillRectangle(new SolidBrush(Color.FromArgb(60, 60, 60)), 12, 12, 9, 9);

            // 边框
            using (Pen pen = new Pen(Color.FromArgb(0, 0, 0), 1))
            {
                g.DrawRectangle(pen, 3, 3, 18, 18);
                g.DrawLine(pen, 12, 3, 12, 21);
                g.DrawLine(pen, 3, 12, 21, 12);
            }
        }

        // 绘制反相图标
        private void DrawInvertIcon(Graphics g)
        {
            // 绘制一个黑白分割的圆
            using (SolidBrush blackBrush = new SolidBrush(Color.Black))
            using (SolidBrush whiteBrush = new SolidBrush(Color.White))
            using (Pen pen = new Pen(Color.Black, 1))
            {
                // 绘制圆
                g.FillEllipse(whiteBrush, 3, 3, 18, 18);

                // 绘制黑色半圆
                System.Drawing.Drawing2D.GraphicsPath path = new System.Drawing.Drawing2D.GraphicsPath();
                path.AddPie(3, 3, 18, 18, 180, 180);
                g.FillPath(blackBrush, path);

                // 绘制圆的轮廓
                g.DrawEllipse(pen, 3, 3, 18, 18);

                // 绘制阴阳符号的小圆点
                g.FillEllipse(blackBrush, 9, 7, 6, 6);
                g.FillEllipse(whiteBrush, 9, 11, 6, 6);
            }
        }

        // 绘制裁剪图标
        private void DrawCropIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(139, 69, 19), 2))
            {
                // 绘制裁剪框
                g.DrawRectangle(pen, 6, 3, 15, 15);

                // 绘制裁剪工具手柄
                g.DrawLine(pen, 3, 6, 6, 6);
                g.DrawLine(pen, 3, 6, 3, 18);
                g.DrawLine(pen, 3, 18, 15, 18);
                g.DrawLine(pen, 15, 18, 15, 15);
            }
        }

        // 绘制调整大小图标
        private void DrawResizeIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(139, 69, 19), 2))
            {
                // 绘制两个不同大小的矩形
                g.DrawRectangle(pen, 3, 3, 12, 12);
                g.DrawRectangle(pen, 9, 9, 12, 12);

                // 绘制调整大小的箭头
                g.DrawLine(pen, 21, 15, 21, 21);
                g.DrawLine(pen, 15, 21, 21, 21);
                g.DrawLine(pen, 21, 21, 17, 17);
            }
        }

        // 绘制图片拼接图标
        private void DrawImageMosaicIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(46, 139, 87), 1.5f)) // 海绿色
            {
                // 绘制四个小方块组成的拼图图标
                g.FillRectangle(new SolidBrush(Color.FromArgb(152, 251, 152)), 3, 3, 8, 8); // 浅绿色
                g.FillRectangle(new SolidBrush(Color.FromArgb(144, 238, 144)), 13, 3, 8, 8); // 淡绿色
                g.FillRectangle(new SolidBrush(Color.FromArgb(143, 188, 143)), 3, 13, 8, 8); // 暗海绿色
                g.FillRectangle(new SolidBrush(Color.FromArgb(60, 179, 113)), 13, 13, 8, 8); // 中海绿色

                // 绘制边框
                g.DrawRectangle(pen, 3, 3, 8, 8);
                g.DrawRectangle(pen, 13, 3, 8, 8);
                g.DrawRectangle(pen, 3, 13, 8, 8);
                g.DrawRectangle(pen, 13, 13, 8, 8);

                // 绘制连接线
                g.DrawLine(pen, 11, 7, 13, 7);
                g.DrawLine(pen, 7, 11, 7, 13);
                g.DrawLine(pen, 17, 11, 17, 13);
                g.DrawLine(pen, 11, 17, 13, 17);
            }
        }

        // 绘制关于图标
        private void DrawAboutIcon(Graphics g)
        {
            using (SolidBrush circleBrush = new SolidBrush(Color.FromArgb(70, 130, 180)))
            using (Pen circlePen = new Pen(Color.FromArgb(25, 25, 112), 1.5f))
            using (SolidBrush textBrush = new SolidBrush(Color.White))
            using (Font font = new Font("Arial", 14, FontStyle.Bold))
            {
                // 绘制圆形背景
                g.FillEllipse(circleBrush, 2, 2, 20, 20);
                g.DrawEllipse(circlePen, 2, 2, 20, 20);
                
                // 绘制字母"i"，使用StringFormat确保居中
                StringFormat sf = new StringFormat();
                sf.Alignment = StringAlignment.Center;
                sf.LineAlignment = StringAlignment.Center;
                g.DrawString("i", font, textBrush, new RectangleF(2, 2, 20, 20), sf);
            }
        }

        // 绘制关闭图标
        private void DrawCloseIcon(Graphics g)
        {
            using (Pen pen = new Pen(Color.FromArgb(220, 20, 60), 2.5f))
            {
                // 绘制X形状
                g.DrawLine(pen, 5, 5, 19, 19);
                g.DrawLine(pen, 19, 5, 5, 19);
            }
        }

        private void LoadSampleImage()
        {
            try
            {
                // 首先尝试加载示例图像 sample.jpg
                string sampleImagePath = Path.Combine(Application.StartupPath, "Resources", "sample.jpg");

                // 如果 sample.jpg 不存在，尝试加载 SampleImages 目录下的 sample1.svg
                if (!File.Exists(sampleImagePath))
                {
                    sampleImagePath = Path.Combine(Application.StartupPath, "Resources", "SampleImages", "sample1.svg");
                }

                if (File.Exists(sampleImagePath))
                {
                    currentImage = Image.FromFile(sampleImagePath);
                    currentImagePath = sampleImagePath;
                    pictureBoxMain.Image = currentImage;
                    UpdateStatusInfo();
                    UpdateMenuItemsState();
                }
                else
                {
                    // 创建一个空白图像
                    currentImage = new Bitmap(800, 600);
                    using (Graphics g = Graphics.FromImage(currentImage))
                    {
                        g.Clear(Color.White);
                        using (Font font = new Font("Arial", 20))
                        using (SolidBrush brush = new SolidBrush(Color.Gray))
                        {
                            string message = "欢迎使用 PictureMagic\n请打开一张图片开始编辑";
                            StringFormat format = new StringFormat
                            {
                                Alignment = StringAlignment.Center,
                                LineAlignment = StringAlignment.Center
                            };
                            g.DrawString(message, font, brush, new RectangleF(0, 0, 800, 600), format);
                        }
                    }
                    pictureBoxMain.Image = currentImage;
                    UpdateStatusInfo();
                    UpdateMenuItemsState();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载示例图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatusInfo()
        {
            if (currentImage != null)
            {
                toolStripStatusFileName.Text = $"文件: {currentImagePath}"; // 显示完整路径
                toolStripStatusDimensions.Text = $"尺寸: {currentImage.Width} x {currentImage.Height}";

                if (!string.IsNullOrEmpty(currentImagePath) && File.Exists(currentImagePath) && !imageModified)
                {
                    // 如果图像未修改且文件存在，显示文件的实际大小
                    FileInfo fileInfo = new FileInfo(currentImagePath);
                    toolStripStatusFileSize.Text = $"大小: {FormatFileSize(fileInfo.Length)}";
                }
                else
                {
                    // 如果图像已修改或文件不存在，估算内存中图像的文件大小
                    long estimatedSize = EstimateFileSize(currentImage as Bitmap);
                    toolStripStatusFileSize.Text = $"大小: {FormatFileSize(estimatedSize)} (估计)";
                }
            }
            else
            {
                toolStripStatusFileName.Text = "文件: -";
                toolStripStatusDimensions.Text = "尺寸: -";
                toolStripStatusFileSize.Text = "大小: -";
            }
        }

        // 估算文件大小
        private long EstimateFileSize(Bitmap image)
        {
            if (image == null) return 0;

            // 使用内存流来估算文件大小
            using (MemoryStream ms = new MemoryStream())
            {
                // 保存为JPEG格式，质量为85%
                image.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                return ms.Length;
            }
        }



        // 更新菜单项状态的方法
        private void UpdateMenuItemsState()
        {
            bool hasImage = currentImage != null;
            bool canUndo = hasImage && recentImages.Count > 0;
            bool canRedo = hasImage && redoImages.Count > 0;

            // 文件菜单
            saveToolStripMenuItem.Enabled = hasImage;
            saveAsToolStripMenuItem.Enabled = hasImage;
            closeToolStripMenuItem.Enabled = hasImage;

            // 编辑菜单
            undoToolStripMenuItem.Enabled = canUndo;
            redoToolStripMenuItem.Enabled = canRedo;
            rotateLeftToolStripMenuItem.Enabled = hasImage;
            rotateRightToolStripMenuItem.Enabled = hasImage;
            flipHorizontalToolStripMenuItem.Enabled = hasImage;
            flipVerticalToolStripMenuItem.Enabled = hasImage;

            // 工具菜单
            resizeToolStripMenuItem.Enabled = hasImage;
            cropToolStripMenuItem.Enabled = hasImage;
            textToolStripMenuItem.Enabled = hasImage;
            drawToolStripMenuItem.Enabled = hasImage;

            // 图片拼接菜单项已删除

            // 特效菜单
            filtersToolStripMenuItem.Enabled = hasImage;
            grayscaleToolStripMenuItem.Enabled = hasImage;
            invertToolStripMenuItem.Enabled = hasImage;
            brightnessContrastToolStripMenuItem.Enabled = hasImage;

            // 工具栏按钮
            toolStripButtonSave.Enabled = hasImage;
            toolStripButtonUndo.Enabled = canUndo;
            toolStripButtonRedo.Enabled = canRedo;
            toolStripButtonRotateLeft.Enabled = hasImage;
            toolStripButtonRotateRight.Enabled = hasImage;
            toolStripButtonFlipH.Enabled = hasImage;
            toolStripButtonFlipV.Enabled = hasImage;
            toolStripButtonGrayscale.Enabled = hasImage;
            toolStripButtonInvert.Enabled = hasImage;
            toolStripButtonCrop.Enabled = hasImage;
            toolStripButtonResize.Enabled = hasImage;
        }

        private string FormatFileSize(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n2} {suffixes[counter]}";
        }

        private void openToolStripMenuItem_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "图像文件|*.jpg;*.jpeg;*.png;*.gif;*.bmp|所有文件|*.*";
                openFileDialog.Title = "打开图像";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"MainForm: 开始打开文件: {openFileDialog.FileName}");

                        // 确保PictureBox处于正常状态
                        pictureBoxMain.Visible = true;
                        pictureBoxMain.Enabled = true;

                        // 先清空PictureBox的图像引用，避免显示问题
                        if (pictureBoxMain.Image != null)
                        {
                            System.Diagnostics.Debug.WriteLine("MainForm: 清空PictureBox当前图像");
                            pictureBoxMain.Image = null;
                        }

                        // 释放当前图像资源
                        if (currentImage != null)
                        {
                            System.Diagnostics.Debug.WriteLine("MainForm: 释放当前图像资源");
                            // 添加到最近使用的图像列表
                            // 检查当前图像是否是样例图片，如果是则不添加到撤销列表
                            if (!currentImagePath.Contains("sample") && !currentImagePath.Contains("SampleImages"))
                            {
                                AddToRecentImages(currentImage);
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("MainForm: 当前图像是样例图片，不添加到撤销列表");
                            }
                            currentImage.Dispose();
                            currentImage = null;
                        }

                        // 释放之前的内存流
                        if (currentImageMemoryStream != null)
                        {
                            currentImageMemoryStream.Dispose();
                            currentImageMemoryStream = null;
                        }

                        // 加载新图像 - 使用FromStream代替FromFile以避免文件锁定
                        System.Diagnostics.Debug.WriteLine("MainForm: 加载新图像");
                        using (FileStream stream = new FileStream(openFileDialog.FileName, FileMode.Open, FileAccess.Read))
                        {
                            // 创建内存流以保存文件内容的副本
                            currentImageMemoryStream = new MemoryStream();
                            stream.CopyTo(currentImageMemoryStream);
                            currentImageMemoryStream.Position = 0;
                            currentImage = Image.FromStream(currentImageMemoryStream, true, true);
                        }
                        currentImagePath = openFileDialog.FileName;

                        // 验证图像是否有效
                        if (currentImage != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"MainForm: 图像加载成功，尺寸: {currentImage.Width}x{currentImage.Height}");

                            // 设置PictureBox图像
                            pictureBoxMain.Image = currentImage;

                            // 强制刷新显示
                            pictureBoxMain.Invalidate();
                            pictureBoxMain.Update();
                            pictureBoxMain.Refresh();

                            System.Diagnostics.Debug.WriteLine("MainForm: PictureBox图像设置完成");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("MainForm: 加载的图像无效");
                            throw new InvalidOperationException("加载的图像无效");
                        }
                        originalImage = currentImage.Clone() as Image; // 保存原始图像的副本
                        // 更新状态栏信息
                        UpdateStatusInfo();
                        // 更新菜单项状态
                        UpdateMenuItemsState();
                        System.Diagnostics.Debug.WriteLine("MainForm: 文件打开完成");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"MainForm: 打开文件出错: {ex.Message}");

                        // 确保清理状态
                        if (currentImage != null)
                        {
                            currentImage.Dispose();
                            currentImage = null;
                        }
                        pictureBoxMain.Image = null;
                        currentImagePath = string.Empty;

                        MessageBox.Show($"打开图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void AddToRecentImages(Image image)
        {
            // 添加到最近使用的图像列表
            if (recentImages.Count >= maxRecentImages)
            {
                // 移除最旧的图像并释放资源
                Image oldestImage = recentImages[0];
                recentImages.RemoveAt(0);
                oldestImage.Dispose();
            }

            // 创建图像的副本
            Bitmap copy = new Bitmap(image);
            recentImages.Add(copy);

            // 添加新的撤销操作后，清空重做列表
            ClearRedoImages();

            // 更新菜单项状态
            UpdateMenuItemsState();
        }

        // 清空重做图像列表
        private void ClearRedoImages()
        {
            // 释放所有重做图像的资源
            foreach (Image img in redoImages)
            {
                img.Dispose();
            }
            redoImages.Clear();
        }

        // 清空撤销图像列表
        private void ClearRecentImages()
        {
            // 释放所有撤销图像的资源
            foreach (Image img in recentImages)
            {
                img.Dispose();
            }
            recentImages.Clear();

            // 更新菜单项状态
            UpdateMenuItemsState();
        }

        // 撤销操作
        private void UndoAction()
        {
            if (recentImages.Count > 0)
            {
                // 获取最近的图像
                Image lastImage = recentImages[recentImages.Count - 1];
                recentImages.RemoveAt(recentImages.Count - 1);

                // 保存当前图像用于重做
                if (currentImage != null)
                {
                    // 添加到重做列表
                    if (redoImages.Count >= maxRecentImages)
                    {
                        // 移除最旧的图像并释放资源
                        Image oldestRedoImage = redoImages[0];
                        redoImages.RemoveAt(0);
                        oldestRedoImage.Dispose();
                    }

                    // 创建当前图像的副本添加到重做列表
                    Bitmap currentCopy = new Bitmap(currentImage);
                    redoImages.Add(currentCopy);

                    // 释放当前图像资源
                    currentImage.Dispose();
                }

                // 更新当前图像
                currentImage = lastImage;
                pictureBoxMain.Image = currentImage;

                // 更新originalImage为当前图像
                if (originalImage != null)
                {
                    originalImage.Dispose();
                }
                originalImage = currentImage.Clone() as Image;

                // 检查是否回到了样例图片或空白图像
                string sampleImagePath = Path.Combine(Application.StartupPath, "Resources", "sample.jpg");
                string sampleImagePath2 = Path.Combine(Application.StartupPath, "Resources", "SampleImages", "sample1.svg");

                // 如果撤销到了第一张图片，可能是样例图片或空白图像，需要更新路径
                if (recentImages.Count == 0)
                {
                    // 检查是否是空白图像（带有欢迎文字的图像）
                    if (currentImage.Width == 800 && currentImage.Height == 600)
                    {
                        // 判断是否为空白图像的更可靠方法是检查图像的特征
                        // 这里简单地通过尺寸判断，实际应用中可能需要更复杂的检测
                        currentImagePath = string.Empty; // 空白图像没有文件路径
                        imageModified = false;
                    }
                    // 检查是否与样例图片尺寸相同
                    else if (File.Exists(sampleImagePath))
                    {
                        using (Image sampleImg = Image.FromFile(sampleImagePath))
                        {
                            if (currentImage.Width == sampleImg.Width && currentImage.Height == sampleImg.Height)
                            {
                                currentImagePath = sampleImagePath;
                                imageModified = false;
                            }
                        }
                    }
                    else if (File.Exists(sampleImagePath2))
                    {
                        using (Image sampleImg = Image.FromFile(sampleImagePath2))
                        {
                            if (currentImage.Width == sampleImg.Width && currentImage.Height == sampleImg.Height)
                            {
                                currentImagePath = sampleImagePath2;
                                imageModified = false;
                            }
                        }
                    }
                }
                else
                {
                    // 设置图像已修改标志
                    imageModified = true;
                }

                // 更新状态栏信息
                UpdateStatusInfo();

                // 更新菜单项状态
                UpdateMenuItemsState();
            }
        }

        // 重做操作
        private void RedoAction()
        {
            if (redoImages.Count > 0)
            {
                // 获取最近的重做图像
                Image redoImage = redoImages[redoImages.Count - 1];
                redoImages.RemoveAt(redoImages.Count - 1);

                // 保存当前图像用于撤销
                if (currentImage != null)
                {
                    // 添加到撤销列表
                    AddToRecentImages(currentImage);

                    // 释放当前图像资源
                    currentImage.Dispose();
                }

                // 更新当前图像
                currentImage = redoImage;
                pictureBoxMain.Image = currentImage;

                // 更新originalImage为当前图像
                if (originalImage != null)
                {
                    originalImage.Dispose();
                }
                originalImage = currentImage.Clone() as Image;

                // 检查是否回到了样例图片或空白图像
                string sampleImagePath = Path.Combine(Application.StartupPath, "Resources", "sample.jpg");
                string sampleImagePath2 = Path.Combine(Application.StartupPath, "Resources", "SampleImages", "sample1.svg");

                // 检查是否是空白图像（带有欢迎文字的图像）
                if (currentImage.Width == 800 && currentImage.Height == 600)
                {
                    // 判断是否为空白图像的更可靠方法是检查图像的特征
                    // 这里简单地通过尺寸判断，实际应用中可能需要更复杂的检测
                    currentImagePath = string.Empty; // 空白图像没有文件路径
                    imageModified = false;
                }
                // 如果重做后的图像与样例图片尺寸相同，可能是样例图片
                else if (File.Exists(sampleImagePath))
                {
                    using (Image sampleImg = Image.FromFile(sampleImagePath))
                    {
                        if (currentImage.Width == sampleImg.Width && currentImage.Height == sampleImg.Height)
                        {
                            currentImagePath = sampleImagePath;
                            imageModified = false;
                        }
                    }
                }
                else if (File.Exists(sampleImagePath2))
                {
                    using (Image sampleImg = Image.FromFile(sampleImagePath2))
                    {
                        if (currentImage.Width == sampleImg.Width && currentImage.Height == sampleImg.Height)
                        {
                            currentImagePath = sampleImagePath2;
                            imageModified = false;
                        }
                    }
                }
                else
                {
                    // 设置图像已修改标志
                    imageModified = true;
                }

                // 更新状态栏信息
                UpdateStatusInfo();

                // 更新菜单项状态
                UpdateMenuItemsState();
            }
        }

        // 撤销菜单项点击事件
        private void undoToolStripMenuItem_Click(object sender, EventArgs e)
        {
            UndoAction();
        }

        // 重做菜单项点击事件
        private void redoToolStripMenuItem_Click(object sender, EventArgs e)
        {
            RedoAction();
        }

        private void saveToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("没有图像可保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (string.IsNullOrEmpty(currentImagePath) || !File.Exists(currentImagePath))
            {
                saveAsToolStripMenuItem_Click(sender, e);
                return;
            }

            // 使用保存选项对话框
            using (SaveOptionsDialog saveOptionsDialog = new SaveOptionsDialog(currentImage, currentImagePath, false))
            {
                if (saveOptionsDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        SaveImageWithOptions(saveOptionsDialog);
                        imageModified = false; // 保存后重置修改状态
                        MessageBox.Show($"图像已保存至【{saveOptionsDialog.SelectedFilePath}】", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void saveAsToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("没有图像可保存", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 使用保存选项对话框，并设置preventOverwrite为true，防止覆盖原文件
            using (SaveOptionsDialog saveOptionsDialog = new SaveOptionsDialog(currentImage, currentImagePath, true))
            {
                if (saveOptionsDialog.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        SaveImageWithOptions(saveOptionsDialog);
                        currentImagePath = saveOptionsDialog.SelectedFilePath;
                        imageModified = false; // 保存后重置修改状态
                        MessageBox.Show($"图像已保存至【{saveOptionsDialog.SelectedFilePath}】", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // 更新状态栏信息
                        UpdateStatusInfo();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"保存图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private ImageFormat GetImageFormat(string extension)
        {
            switch (extension.ToLower())
            {
                case ".jpg":
                case ".jpeg":
                    return ImageFormat.Jpeg;
                case ".png":
                    return ImageFormat.Png;
                case ".gif":
                    return ImageFormat.Gif;
                case ".bmp":
                    return ImageFormat.Bmp;
                default:
                    return ImageFormat.Jpeg; // 默认使用JPEG格式
            }
        }

        // 使用保存选项保存图像
        private void SaveImageWithOptions(SaveOptionsDialog saveOptionsDialog)
        {
            if (currentImage == null || saveOptionsDialog == null)
                return;

            // 获取保存选项
            string filePath = saveOptionsDialog.SelectedFilePath;
            ImageFormat format = saveOptionsDialog.SelectedFormat;
            EncoderParameters encoderParams = saveOptionsDialog.EncoderParams;

            // 创建要保存的图像副本
            Image imageToSave = new Bitmap(currentImage);

            // 如果未注册，添加水印
            if (!PictureMagic.Common.Tools.IsRegistered)
            {
                // 水印文字
                string[] watermarkText = new string[] {
                    "PictureMagic",
                    "未注册版本生成低画质图片",
                    "<EMAIL>"
                };

                using (Graphics g = Graphics.FromImage(imageToSave))
                {
                    // 创建不透明的画笔，使水印在低质量图片中也能清晰显示
                    using (Font watermarkFont = new Font("Arial", 24, FontStyle.Bold))
                    using (SolidBrush brush = new SolidBrush(Color.FromArgb(255, 255, 0, 0)))
                    {
                        // 计算水印位置（居中）
                        StringFormat format2 = new StringFormat();
                        format2.Alignment = StringAlignment.Center;
                        format2.LineAlignment = StringAlignment.Center;

                        // 计算三行文字的总高度
                        float lineHeight = watermarkFont.GetHeight(g) * 1.2f;
                        float watermarkTotalHeight = lineHeight * watermarkText.Length;

                        // 计算起始Y坐标（居中）
                        float startY = (watermarkTotalHeight / 2) * -1 + (watermarkTotalHeight / 2);

                        // 绘制三行水印文字（带白色边框以增强可见性）
                        for (int i = 0; i < watermarkText.Length; i++)
                        {
                            float y = (watermarkTotalHeight / 2) * -1 + (i * lineHeight) + (watermarkTotalHeight / 2);
                            PointF textPosition = new PointF(imageToSave.Width / 2, imageToSave.Height / 2 + y - startY);

                            // 先绘制白色边框（通过在不同位置绘制白色文字实现）
                            using (SolidBrush whiteBrush = new SolidBrush(Color.White))
                            {
                                // 绘制四周的白色文字作为边框
                                for (int offsetX = -2; offsetX <= 2; offsetX += 2)
                                {
                                    for (int offsetY = -2; offsetY <= 2; offsetY += 2)
                                    {
                                        if (offsetX != 0 || offsetY != 0) // 跳过中心点
                                        {
                                            g.DrawString(watermarkText[i], watermarkFont, whiteBrush,
                                                new PointF(textPosition.X + offsetX, textPosition.Y + offsetY),
                                                format2);
                                        }
                                    }
                                }
                            }

                            // 然后绘制红色文字
                            g.DrawString(watermarkText[i], watermarkFont, brush, textPosition, format2);
                        }
                    }
                }
            }

            // 如果是JPEG格式且有编码参数，使用编码参数保存
            if (format.Equals(ImageFormat.Jpeg) && encoderParams != null)
            {
                ImageCodecInfo jpegCodec = GetEncoderInfo("image/jpeg");
                if (jpegCodec != null)
                {
                    imageToSave.Save(filePath, jpegCodec, encoderParams);
                    imageToSave.Dispose();
                    return;
                }
            }

            // 其他格式或没有编码参数，使用默认方式保存
            imageToSave.Save(filePath, format);
            imageToSave.Dispose();
        }

        // 获取指定MIME类型的编码器信息
        private ImageCodecInfo GetEncoderInfo(string mimeType)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.MimeType == mimeType)
                {
                    return codec;
                }
            }
            return null;
        }

        private void exitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Close();
        }

        private async void rotateLeftToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 显示进度提示
                ShowProgress("正在向左旋转图像...");

                // 保存当前图像用于撤销
                AddToRecentImages(currentImage);

                // 异步旋转图像
                await Task.Run(() =>
                {
                    // 向左旋转图像（逆时针90度）
                    currentImage.RotateFlip(RotateFlipType.Rotate270FlipNone);
                });

                pictureBoxMain.Image = currentImage;

                // 更新originalImage为当前图像
                if (originalImage != null)
                {
                    originalImage.Dispose();
                }
                originalImage = currentImage.Clone() as Image;

                // 设置图像已修改标志
                imageModified = true;

                // 更新状态栏信息
                UpdateStatusInfo();

                // 更新菜单项状态
                UpdateMenuItemsState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"旋转图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                HideProgress();
            }
        }

        private async void rotateRightToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 显示进度提示
                ShowProgress("正在向右旋转图像...");

                // 保存当前图像用于撤销
                AddToRecentImages(currentImage);

                // 异步旋转图像
                await Task.Run(() =>
                {
                    // 向右旋转图像（顺时针90度）
                    currentImage.RotateFlip(RotateFlipType.Rotate90FlipNone);
                });

                pictureBoxMain.Image = currentImage;

                // 更新originalImage为当前图像
                if (originalImage != null)
                {
                    originalImage.Dispose();
                }
                originalImage = currentImage.Clone() as Image;

                // 设置图像已修改标志
                imageModified = true;

                // 更新状态栏信息
                UpdateStatusInfo();

                // 更新菜单项状态
                UpdateMenuItemsState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"旋转图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                HideProgress();
            }
        }

        private void flipHorizontalToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 保存当前图像用于撤销
                AddToRecentImages(currentImage);

                // 水平翻转图像
                currentImage.RotateFlip(RotateFlipType.RotateNoneFlipX);
                pictureBoxMain.Image = currentImage;

                // 更新originalImage为当前图像
                if (originalImage != null)
                {
                    originalImage.Dispose();
                }
                originalImage = currentImage.Clone() as Image;

                // 设置图像已修改标志
                imageModified = true;

                // 更新状态栏信息
                UpdateStatusInfo();

                // 更新菜单项状态
                UpdateMenuItemsState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"翻转图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void flipVerticalToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 保存当前图像用于撤销
                AddToRecentImages(currentImage);

                // 垂直翻转图像
                currentImage.RotateFlip(RotateFlipType.RotateNoneFlipY);
                pictureBoxMain.Image = currentImage;

                // 更新originalImage为当前图像
                if (originalImage != null)
                {
                    originalImage.Dispose();
                }
                originalImage = currentImage.Clone() as Image;

                // 设置图像已修改标志
                imageModified = true;

                // 更新状态栏信息
                UpdateStatusInfo();

                // 更新菜单项状态
                UpdateMenuItemsState();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"翻转图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void resizeToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 显示调整大小对话框
                using (ResizeDialog dialog = new ResizeDialog(currentImage))
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        int newWidth = dialog.NewWidth;
                        int newHeight = dialog.NewHeight;

                        // 显示进度提示
                        ShowProgress("正在调整图像大小...");

                        // 异步创建新的调整大小后的图像
                        Bitmap resizedImage = await Task.Run(() =>
                        {
                            Bitmap newImage = new Bitmap(newWidth, newHeight);
                            using (Graphics g = Graphics.FromImage(newImage))
                            {
                                g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                                g.DrawImage(currentImage, 0, 0, newWidth, newHeight);
                            }
                            return newImage;
                        });

                        // 释放当前图像资源
                        if (currentImage != null)
                        {
                            // 添加到最近使用的图像列表
                            AddToRecentImages(currentImage);
                            currentImage.Dispose();
                        }

                        // 更新当前图像
                        currentImage = resizedImage;
                        pictureBoxMain.Image = currentImage;

                        // 更新originalImage为当前图像
                        if (originalImage != null)
                        {
                            originalImage.Dispose();
                        }
                        originalImage = currentImage.Clone() as Image;

                        // 设置图像已修改标志
                        imageModified = true;

                        // 更新状态栏信息
                        UpdateStatusInfo();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"调整图像大小出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                HideProgress();
            }
        }

        private void invertToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 应用反相滤镜
                Bitmap filteredImage = FilterEffects.ApplyFilter(currentImage, FilterEffects.FilterType.Invert);
                if (filteredImage != null)
                {
                    // 释放当前图像资源
                    if (currentImage != null)
                    {
                        // 添加到最近使用的图像列表
                        AddToRecentImages(currentImage);
                        currentImage.Dispose();
                    }

                    // 更新当前图像
                    currentImage = filteredImage;
                    pictureBoxMain.Image = currentImage;

                    // 更新originalImage为当前图像
                    if (originalImage != null)
                    {
                        originalImage.Dispose();
                    }
                    originalImage = currentImage.Clone() as Image;

                    // 设置图像已修改标志
                    imageModified = true;

                    // 更新状态栏信息
                    UpdateStatusInfo();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用反相滤镜出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void cropToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            CropTool? cropTool = null;
            try
            {
                // 验证当前图像的有效性
                if (currentImage.Width <= 0 || currentImage.Height <= 0)
                {
                    MessageBox.Show("当前图像无效，无法进行裁剪", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 创建裁剪工具实例
                cropTool = new CropTool(pictureBoxMain, currentImage);

                // 订阅裁剪完成事件
                cropTool.CropCompleted += (s, cropResult) =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"MainForm: 收到CropCompleted事件");

                        if (cropResult?.CroppedImage != null)
                        {
                            System.Diagnostics.Debug.WriteLine($"MainForm: 接收到新图像 {cropResult.CroppedImage.Width}x{cropResult.CroppedImage.Height}");

                            // 保存对裁剪图像的引用，防止被GC回收
                            Bitmap croppedImage = (Bitmap)cropResult.CroppedImage;

                            // 在UI线程中更新图像
                            Action updateUI = () =>
                            {
                                try
                                {
                                    System.Diagnostics.Debug.WriteLine("MainForm: 开始在UI线程更新图像");

                                    // 验证裁剪图像是否有效
                                    if (croppedImage == null || croppedImage.Width <= 0 || croppedImage.Height <= 0)
                                    {
                                        System.Diagnostics.Debug.WriteLine("MainForm: 裁剪图像无效，中止UI更新");
                                        MessageBox.Show("裁剪图像无效，无法更新UI", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                        return;
                                    }

                                    // 创建裁剪图像的副本，避免使用可能已被释放的图像
                                    Bitmap safeCroppedImage = null;
                                    try
                                    {
                                        safeCroppedImage = new Bitmap(croppedImage);
                                        System.Diagnostics.Debug.WriteLine($"MainForm: 创建了裁剪图像的安全副本，尺寸: {safeCroppedImage.Width}x{safeCroppedImage.Height}");
                                    }
                                    catch (Exception copyEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"MainForm: 创建裁剪图像副本失败: {copyEx.Message}");
                                        MessageBox.Show($"无法创建裁剪图像副本: {copyEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                        return;
                                    }

                                    // 释放当前图像资源
                                    if (currentImage != null)
                                    {
                                        try
                                        {
                                            System.Diagnostics.Debug.WriteLine("MainForm: 释放旧图像资源");
                                            // 添加到最近使用的图像列表
                                            AddToRecentImages(currentImage);
                                            currentImage.Dispose();
                                            currentImage = null; // 确保引用被清除
                                        }
                                        catch (Exception disposeEx)
                                        {
                                            System.Diagnostics.Debug.WriteLine($"MainForm: 释放旧图像资源时出错: {disposeEx.Message}");
                                            // 继续执行，不中断流程
                                        }
                                    }

                                    // 更新当前图像
                                    currentImage = safeCroppedImage;
                                    System.Diagnostics.Debug.WriteLine($"MainForm: 已更新currentImage引用，尺寸: {currentImage.Width}x{currentImage.Height}");

                                    // 更新originalImage为当前图像
                                    if (originalImage != null)
                                    {
                                        originalImage.Dispose();
                                    }
                                    originalImage = currentImage.Clone() as Image;
                                    System.Diagnostics.Debug.WriteLine($"MainForm: 已更新originalImage引用，尺寸: {originalImage.Width}x{originalImage.Height}");

                                    // 设置图像已修改标志
                                    imageModified = true;
                                    System.Diagnostics.Debug.WriteLine("MainForm: 已设置imageModified = true");

                                    // 先清空PictureBox的旧图像引用
                                    try
                                    {
                                        if (pictureBoxMain.Image != null && pictureBoxMain.Image != currentImage)
                                        {
                                            System.Diagnostics.Debug.WriteLine("MainForm: 清空pictureBoxMain的旧图像引用");
                                            pictureBoxMain.Image = null;
                                        }
                                    }
                                    catch (Exception clearEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"MainForm: 清空PictureBox图像引用时出错: {clearEx.Message}");
                                        // 继续执行，不中断流程
                                    }

                                    try
                                    {
                                        // 更新PictureBox
                                        pictureBoxMain.Image = currentImage;
                                        System.Diagnostics.Debug.WriteLine($"MainForm: 已设置pictureBoxMain.Image，当前SizeMode: {pictureBoxMain.SizeMode}");

                                        // 确保PictureBox可见
                                        pictureBoxMain.Visible = true;
                                        System.Diagnostics.Debug.WriteLine($"MainForm: pictureBoxMain可见性: {pictureBoxMain.Visible}");

                                        // 强制重绘 - 使用更强的刷新机制
                                        pictureBoxMain.Invalidate(true);
                                        pictureBoxMain.Update();
                                        pictureBoxMain.Refresh();

                                        // 添加额外的刷新机制
                                        this.Invalidate(true);
                                        this.Update();
                                        this.Refresh();

                                        // 强制处理所有待处理的Windows消息
                                        Application.DoEvents();

                                        System.Diagnostics.Debug.WriteLine("MainForm: 已强制刷新pictureBoxMain和主窗体");
                                    }
                                    catch (Exception uiEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"MainForm: 更新PictureBox时出错: {uiEx.Message}");
                                        MessageBox.Show($"更新图像显示时出错: {uiEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                        // 继续执行，尝试完成剩余操作
                                    }

                                    try
                                    {
                                        // 更新状态栏信息
                                        UpdateStatusInfo();
                                        System.Diagnostics.Debug.WriteLine("MainForm: 已更新状态栏信息");
                                    }
                                    catch (Exception statusEx)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"MainForm: 更新状态栏时出错: {statusEx.Message}");
                                        // 继续执行，不中断流程
                                    }

                                    // 检查PictureBox的图像是否正确设置
                                    if (pictureBoxMain.Image != null)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"MainForm: pictureBoxMain.Image尺寸: {pictureBoxMain.Image.Width}x{pictureBoxMain.Image.Height}");
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine("MainForm: 警告 - pictureBoxMain.Image为null");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"MainForm: UI更新过程中出错: {ex.Message}\n{ex.StackTrace}");
                                    MessageBox.Show($"更新UI时出错: {ex.Message}\n\n如果问题持续出现，请重启应用程序。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            };

                            // 确保在UI线程执行
                            try
                            {
                                if (pictureBoxMain.InvokeRequired)
                                {
                                    System.Diagnostics.Debug.WriteLine("MainForm: 需要Invoke到UI线程");
                                    if (!pictureBoxMain.IsDisposed && pictureBoxMain.IsHandleCreated)
                                    {
                                        // 使用BeginInvoke而不是Invoke，避免死锁
                                        pictureBoxMain.BeginInvoke(new MethodInvoker(() =>
                                        {
                                            try
                                            {
                                                System.Diagnostics.Debug.WriteLine("MainForm: BeginInvoke回调开始执行");
                                                updateUI();

                                                // 确保所有UI更新都被处理
                                                Application.DoEvents();

                                                System.Diagnostics.Debug.WriteLine("MainForm: BeginInvoke回调执行完成");
                                            }
                                            catch (Exception callbackEx)
                                            {
                                                System.Diagnostics.Debug.WriteLine($"MainForm: BeginInvoke回调中出错: {callbackEx.Message}");
                                                MessageBox.Show($"UI更新回调中出错: {callbackEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                            }
                                        }));

                                        System.Diagnostics.Debug.WriteLine("MainForm: 已安排BeginInvoke回调");
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine("MainForm: 无法Invoke - 控件已释放或句柄未创建");
                                        MessageBox.Show("无法更新UI - 应用程序窗口状态异常", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                    }
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine("MainForm: 已在UI线程，直接执行更新");
                                    updateUI();

                                    // 确保所有UI更新都被处理
                                    Application.DoEvents();
                                }
                            }
                            catch (InvalidOperationException invokeEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"MainForm: Invoke操作失败: {invokeEx.Message}");
                                MessageBox.Show($"无法在UI线程更新图像: {invokeEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"MainForm: 线程调度时出错: {ex.Message}");
                                MessageBox.Show($"更新UI时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }

                            System.Diagnostics.Debug.WriteLine("MainForm: 图像更新过程完成");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("MainForm: 接收到的图像为null");
                            MessageBox.Show("裁剪结果无效", "警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    catch (Exception cropEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"MainForm: 应用裁剪结果时出错: {cropEx.Message}\n{cropEx.StackTrace}");

                        // 剪裁操作出错时清空主窗口内容
                        try
                        {
                            System.Diagnostics.Debug.WriteLine("MainForm: 剪裁出错，清空pictureBoxMain内容");

                            // 清空PictureBox显示
                            pictureBoxMain.Image = null;

                            // 释放当前图像资源
                            if (currentImage != null)
                            {
                                currentImage.Dispose();
                                currentImage = null;
                            }

                            // 清空路径
                            currentImagePath = string.Empty;

                            // 更新状态栏
                            UpdateStatusInfo();

                            // 强制刷新界面
                            pictureBoxMain.Invalidate();
                            pictureBoxMain.Update();
                            pictureBoxMain.Refresh();

                            System.Diagnostics.Debug.WriteLine("MainForm: 已清空pictureBoxMain内容");
                        }
                        catch (Exception clearEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"MainForm: 清空界面时出错: {clearEx.Message}");
                        }

                        MessageBox.Show($"应用裁剪结果时出错: {cropEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    finally
                    {
                        // 清理裁剪工具
                        if (s is CropTool tool)
                        {
                            System.Diagnostics.Debug.WriteLine("MainForm: 释放CropTool资源");
                            tool.Dispose();
                        }
                    }
                };

                // 订阅裁剪取消事件
                cropTool.CropCancelled += (s, args) =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("MainForm: 剪裁操作被取消，清空pictureBoxMain内容");

                        // 清空PictureBox显示
                        pictureBoxMain.Image = null;

                        // 释放当前图像资源
                        if (currentImage != null)
                        {
                            currentImage.Dispose();
                            currentImage = null;
                        }

                        // 清空路径
                        currentImagePath = string.Empty;

                        // 更新状态栏
                        UpdateStatusInfo();

                        // 强制刷新界面
                        pictureBoxMain.Invalidate();
                        pictureBoxMain.Update();
                        pictureBoxMain.Refresh();

                        System.Diagnostics.Debug.WriteLine("MainForm: 已清空pictureBoxMain内容");
                    }
                    catch (Exception clearEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"MainForm: 清空界面时出错: {clearEx.Message}");
                    }
                    finally
                    {
                        // 清理裁剪工具
                        if (s is CropTool tool)
                        {
                            tool.Dispose();
                        }
                    }
                };

                // 启用裁剪模式
                cropTool.Activate();
            }
            catch (Exception ex)
            {
                try
                {
                    System.Diagnostics.Debug.WriteLine("MainForm: 启动剪裁工具出错，清空pictureBoxMain内容");

                    // 清空PictureBox显示
                    pictureBoxMain.Image = null;

                    // 释放当前图像资源
                    if (currentImage != null)
                    {
                        currentImage.Dispose();
                        currentImage = null;
                    }

                    // 清空路径
                    currentImagePath = string.Empty;

                    // 更新状态栏
                    UpdateStatusInfo();

                    // 强制刷新界面
                    pictureBoxMain.Invalidate();
                    pictureBoxMain.Update();
                    pictureBoxMain.Refresh();

                    System.Diagnostics.Debug.WriteLine("MainForm: 已清空pictureBoxMain内容");
                }
                catch (Exception clearEx)
                {
                    System.Diagnostics.Debug.WriteLine($"MainForm: 清空界面时出错: {clearEx.Message}");
                }

                MessageBox.Show($"启动裁剪工具出错: {ex.Message}\n详细信息: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                // 清理资源
                cropTool?.Dispose();
            }
        }

        private void drawingToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 显示绘图对话框
                using (DrawingDialog dialog = new DrawingDialog(currentImage))
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        // 获取绘制后的图像
                        Bitmap drawnImage = (Bitmap)dialog.ResultImage;

                        // 释放当前图像资源
                        if (currentImage != null)
                        {
                            // 添加到最近使用的图像列表
                            AddToRecentImages(currentImage);
                            currentImage.Dispose();
                        }

                        // 更新当前图像
                        currentImage = drawnImage;
                        pictureBoxMain.Image = currentImage;

                        // 更新originalImage为当前图像
                        if (originalImage != null)
                        {
                            originalImage.Dispose();
                        }
                        originalImage = currentImage.Clone() as Image;

                        // 设置图像已修改标志
                        imageModified = true;

                        // 更新状态栏信息
                        UpdateStatusInfo();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"绘图工具出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void drawToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 调用已实现的绘图工具方法
            drawingToolStripMenuItem_Click(sender, e);
        }

        private void textToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 显示文本对话框
                using (TextDialog dialog = new TextDialog(currentImage))
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        // 获取添加文本后的图像
                        Bitmap textImage = (Bitmap)dialog.ResultImage;

                        // 释放当前图像资源
                        if (currentImage != null)
                        {
                            // 添加到最近使用的图像列表
                            AddToRecentImages(currentImage);
                            currentImage.Dispose();
                        }

                        // 更新当前图像
                        currentImage = textImage;
                        pictureBoxMain.Image = currentImage;

                        // 更新originalImage为当前图像
                        if (originalImage != null)
                        {
                            originalImage.Dispose();
                        }
                        originalImage = currentImage.Clone() as Image;

                        // 设置图像已修改标志
                        imageModified = true;

                        // 更新状态栏信息
                        UpdateStatusInfo();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"文本工具出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void brightnessContrastToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 显示进度提示
                ShowProgress("正在打开亮度/对比度调整对话框...");

                // 检查图像是否有效
                if (currentImage.Width <= 0 || currentImage.Height <= 0)
                {
                    throw new ArgumentException("图像尺寸无效");
                }

                // 创建图像副本以避免直接修改原图
                Bitmap imageCopy = null;
                try
                {
                    imageCopy = new Bitmap(currentImage);
                }
                catch (Exception ex)
                {
                    throw new ArgumentException($"无法创建图像副本: {ex.Message}");
                }

                // 异步显示亮度/对比度对话框
                await Task.Run(() => Thread.Sleep(100)); // 短暂延迟确保UI更新

                HideProgress(); // 隐藏进度提示，显示对话框

                using (BrightnessContrastDialog dialog = new BrightnessContrastDialog(imageCopy))
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        // 显示处理进度
                        ShowProgress("正在应用亮度/对比度调整...");

                        // 异步获取调整后的图像
                        Bitmap adjustedImage = null;
                        try
                        {
                            adjustedImage = await Task.Run(() => (Bitmap)dialog.ResultImage);

                            if (adjustedImage == null || adjustedImage.Width <= 0 || adjustedImage.Height <= 0)
                            {
                                throw new ArgumentException("调整后的图像无效");
                            }
                        }
                        catch (Exception ex)
                        {
                            // 如果获取调整后的图像失败，释放资源并抛出异常
                            if (adjustedImage != null)
                            {
                                adjustedImage.Dispose();
                            }
                            throw new InvalidOperationException($"获取调整后的图像失败: {ex.Message}", ex);
                        }

                        // 释放当前图像资源
                        if (currentImage != null)
                        {
                            // 添加到最近使用的图像列表
                            AddToRecentImages(currentImage);
                            currentImage.Dispose();
                        }

                        // 更新当前图像
                        currentImage = adjustedImage;
                        pictureBoxMain.Image = currentImage;

                        // 更新originalImage为当前图像
                        if (originalImage != null)
                        {
                            originalImage.Dispose();
                        }
                        originalImage = currentImage.Clone() as Image;

                        // 设置图像已修改标志
                        imageModified = true;

                        // 更新状态栏信息
                        UpdateStatusInfo();

                        HideProgress();
                    }
                    else
                    {
                        // 如果用户取消，释放创建的副本
                        imageCopy.Dispose();
                    }
                }
            }
            catch (OutOfMemoryException)
            {
                HideProgress();
                MessageBox.Show("内存不足，无法完成亮度/对比度调整。请尝试使用较小的图像。", "内存错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (ArgumentException argEx)
            {
                HideProgress();
                MessageBox.Show($"参数无效: {argEx.Message}", "参数错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                HideProgress();
                MessageBox.Show($"亮度/对比度调整出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void filtersToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 直接打开滤镜对话框
                using (FilterDialog dialog = new FilterDialog(currentImage))
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        // 获取应用滤镜后的图像
                        Image filteredImage = dialog.ResultImage;

                        // 释放当前图像资源
                        if (currentImage != null)
                        {
                            // 添加到最近使用的图像列表
                            AddToRecentImages(currentImage);
                            currentImage.Dispose();
                        }

                        // 更新当前图像
                        currentImage = filteredImage;
                        pictureBoxMain.Image = currentImage;

                        // 更新originalImage为当前图像
                        if (originalImage != null)
                        {
                            originalImage.Dispose();
                        }
                        originalImage = currentImage.Clone() as Image;

                        // 设置图像已修改标志
                        imageModified = true;

                        // 更新状态栏信息
                        UpdateStatusInfo();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用滤镜出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 检查是否有未保存的图像修改
            if (currentImage != null && imageModified)
            {
                DialogResult result = MessageBox.Show("当前图像有未保存的修改，是否保存？", "保存确认",
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 用户选择保存
                    saveToolStripMenuItem_Click(sender, e);
                }
                else if (result == DialogResult.Cancel)
                {
                    // 用户取消关闭操作
                    e.Cancel = true;
                    return;
                }
            }

            // 释放图像资源
            if (currentImage != null)
            {
                currentImage.Dispose();
                currentImage = null;
            }

            // 释放最近使用的图像资源
            foreach (Image img in recentImages)
            {
                img.Dispose();
            }
            recentImages.Clear();
        }

        // 关闭当前图像的方法
        private void CloseCurrentImage()
        {
            if (currentImage != null)
            {
                // 检查是否有未保存的修改
                if (imageModified)
                {
                    DialogResult result = MessageBox.Show("当前图像有未保存的修改，是否保存？", "保存确认",
                        MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // 用户选择保存
                        saveToolStripMenuItem_Click(null, EventArgs.Empty);
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        // 用户取消关闭操作
                        return;
                    }
                }

                // 释放图像资源
                pictureBoxMain.Image = null;
                currentImage.Dispose();
                currentImage = null;
                originalImage = null;
                currentImagePath = string.Empty;
                imageModified = false;

                // 清空撤销和重做列表
                ClearRecentImages();
                ClearRedoImages();

                // 更新状态栏信息
                UpdateStatusInfo();

                // 禁用需要图像的菜单项
                UpdateMenuItemsState();

                System.Diagnostics.Debug.WriteLine("MainForm: 已关闭当前图像");
            }
        }



        private void ToolsListBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem != null)
            {
                string? selectedTool = listBox.SelectedItem.ToString();
                if (selectedTool != null)
                {
                    switch (selectedTool)
                    {
                        case "选择工具":
                            // 实现选择工具功能
                            break;
                        case "裁剪工具":
                            cropToolStripMenuItem_Click(sender, e);
                            break;
                        case "绘图工具":
                            drawToolStripMenuItem_Click(sender, e);
                            break;
                        case "文本工具":
                            textToolStripMenuItem_Click(sender, e);
                            break;
                        case "调整大小":
                            resizeToolStripMenuItem_Click(sender, e);
                            break;
                        case "图片拼接":
                            imageMosaicToolStripMenuItem_Click(sender, e);
                            break;
                    }
                }
            }
        }

        private void InitializeFiltersPanel()
        {
            // 清空现有项目
            listBoxFilters.Items.Clear();

            // 获取所有滤镜名称并添加到ListBox
            try
            {
                List<string> filterNames = FilterEffects.GetFilterNames();
                foreach (string filterName in filterNames)
                {
                    listBoxFilters.Items.Add(filterName);
                }
            }
            catch
            {
                // 如果获取滤镜失败，添加默认项
                listBoxFilters.Items.Add("灰度");
                listBoxFilters.Items.Add("模糊");
                listBoxFilters.Items.Add("锐化");
            }
        }

        private void FiltersListBox_SelectedIndexChanged(object? sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (sender is ListBox listBox && listBox.SelectedItem != null)
            {
                string? selectedFilter = listBox.SelectedItem.ToString();
                if (selectedFilter != null)
                {
                    try
                    {
                        // 显示进度提示
                        ShowProgress($"正在应用 {listBoxFilters.SelectedItem} 滤镜...");

                        // 在应用滤镜前保存当前状态到撤销历史
                        if (currentImage != null)
                        {
                            // 添加到最近使用的图像列表，以支持撤销操作
                            AddToRecentImages(currentImage);
                        }

                        // 释放之前的预览图像
                        if (currentImage != null && currentImage != originalImage)
                        {
                            pictureBoxMain.Image = null;
                            currentImage.Dispose();
                        }

                        FilterEffects.FilterType filterType = FilterEffects.GetFilterTypeByName(selectedFilter);
                        // 应用滤镜创建新的预览图像，传递null作为ProgressForm参数
                        var previewImage = FilterEffects.ApplyFilter(originalImage, filterType, 100, null);
                        pictureBoxMain.Image = previewImage;
                        currentImage = previewImage;

                        // 设置图像已修改标志
                        imageModified = true;

                        // 更新状态栏信息
                        UpdateStatusInfo();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"应用滤镜时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                    finally
                    {
                        // 隐藏进度提示
                        HideProgress();
                    }
                }
            }
        }


        private void EffectsListBox_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (sender is ListBox listBox && listBox.SelectedItem != null)
            {
                string? selectedEffect = listBox.SelectedItem.ToString();
                if (selectedEffect != null)
                {
                    switch (selectedEffect)
                    {
                        case "亮度/对比度":
                            brightnessContrastToolStripMenuItem_Click(sender, e);
                            break;
                        case "向左旋转":
                            rotateLeftToolStripMenuItem_Click(sender, e);
                            break;
                        case "向右旋转":
                            rotateRightToolStripMenuItem_Click(sender, e);
                            break;
                        case "水平翻转":
                            flipHorizontalToolStripMenuItem_Click(sender, e);
                            break;
                        case "垂直翻转":
                            flipVerticalToolStripMenuItem_Click(sender, e);
                            break;
                    }
                }
            }
        }

        private void ConfigureLeftPanel()
        {
            // TableLayoutPanel已经在设计器中配置好了，只需要确保面板设置正确
            splitContainerMain.Panel1MinSize = 200;
            splitContainerMain.SplitterDistance = 200;
        }

        private void InitializeProgressControls()
        {
            // 不再需要初始化进度面板，因为现在使用ProgressForm
        }

        // CenterProgressPanel方法已移除，因为现在使用ProgressForm

        private ProgressForm progressForm = null;

        private void ShowProgress(string message = "正在处理...")
        {
            // 如果已经有进度窗体在显示，则更新消息
            if (progressForm != null && !progressForm.IsDisposed)
            {
                progressForm.UpdateMessage(message);
                progressForm.BringToFront();
            }
            else
            {
                // 创建并显示新的进度窗体
                progressForm = ProgressForm.ShowProgressDialog(this, message, true); // 使用遮罩效果
                progressForm.SetMarqueeStyle(); // 设置为Marquee样式
            }

            Application.DoEvents(); // 强制刷新UI
        }

        private void HideProgress()
        {
            if (progressForm != null && !progressForm.IsDisposed)
            {
                progressForm.CloseProgress();
                progressForm = null;
            }
        }

        private void closeToolStripMenuItem_Click(object sender, EventArgs e)
        {
            CloseCurrentImage();
        }

        private void aboutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 显示关于对话框
            MessageBox.Show(
                "PictureMagic 图像编辑器 v1.0\n\n" +
                "一个简单而功能强大的图像编辑工具，提供基本的图像编辑功能，\n" +
                "包括滤镜、特效、裁剪、调整大小等。\n\n" +
                $"© {DateTime.Now.Year} New Army software development studio",
                "关于 PictureMagic",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information
            );
        }

        private void grayscaleToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (currentImage == null)
            {
                MessageBox.Show("请先打开一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // 应用灰度滤镜
                Bitmap filteredImage = FilterEffects.ApplyFilter(currentImage, FilterEffects.FilterType.Grayscale);
                if (filteredImage != null)
                {
                    // 释放当前图像资源
                    if (currentImage != null)
                    {
                        // 添加到最近使用的图像列表
                        AddToRecentImages(currentImage);
                        currentImage.Dispose();
                    }

                    // 更新当前图像
                    currentImage = filteredImage;
                    pictureBoxMain.Image = currentImage;

                    // 更新originalImage为当前图像
                    if (originalImage != null)
                    {
                        originalImage.Dispose();
                    }
                    originalImage = currentImage.Clone() as Image;

                    // 设置图像已修改标志
                    imageModified = true;

                    // 更新状态栏信息
                    UpdateStatusInfo();

                    // 更新菜单项状态
                    UpdateMenuItemsState();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用灰度滤镜出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // 图片拼接菜单项点击事件处理程序
        private void imageMosaicToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 显示图片拼接对话框
                using (ImageMosaicDialog dialog = new ImageMosaicDialog())
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        // 获取拼接后的图像
                        Bitmap mosaicImage = (Bitmap)dialog.ResultImage;
                        currentImagePath = dialog.OutputFilePath;
                        if (mosaicImage != null)
                        {
                            // 释放当前图像资源
                            if (currentImage != null)
                            {
                                // 添加到最近使用的图像列表
                                AddToRecentImages(currentImage);
                                currentImage.Dispose();
                            }

                            // 更新当前图像
                            currentImage = mosaicImage;
                            pictureBoxMain.Image = currentImage;

                            // 更新originalImage为当前图像
                            if (originalImage != null)
                            {
                                originalImage.Dispose();
                            }
                            originalImage = currentImage.Clone() as Image;

                            // 设置图像已修改标志
                            imageModified = true;

                            // 更新状态栏信息
                            UpdateStatusInfo();

                            // 更新菜单项状态
                            UpdateMenuItemsState();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"图片拼接出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void lblEmail_Click(object sender, EventArgs e)
        {
            try
            {
                Process.Start(new ProcessStartInfo($"mailto:{lblEmail.Text}") { UseShellExecute = true });
            }
            catch (Exception ex)
            {
                MessageBox.Show("无法启动邮件客户端：" + ex.Message);
            }
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            string machineCode = HardwareInfo.GenerateMachineCode();
            var settings = AppSettings.LoadSettings();
            string inputLicense = settings.LicenseKey;
            // 验证注册码
            if (LicenseGenerator.ValidateLicenseKey(machineCode, inputLicense, "PictureMagicSecretKey"))
            {
                Tools.IsRegistered = true;
            }
            else
            {
                Tools.IsRegistered = false;
            }
            string formTitle = $"PictureMagic 图像编辑器 {Tools.GetVersion()}" + (Tools.IsRegistered ? "-已注册" : " - 未注册");
            this.Text = formTitle;
        }

        private void MainForm_Activated(object sender, EventArgs e)
        {
            string formTitle = $"PictureMagic 图像编辑器 {Tools.GetVersion()}" + (Tools.IsRegistered ? "-已注册" : " - 未注册");
            this.Text = formTitle;
        }
    }

}