namespace PictureMagic
{
    partial class CustomMessageBox
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.lblMessage = new Label();
            this.panelButtons = new Panel();
            this.btnOK = new Button();
            this.btnCancel = new Button();
            this.btnCustom = new Button();
            this.pictureBoxIcon = new PictureBox();
            this.panelButtons.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxIcon)).BeginInit();
            this.SuspendLayout();

            // txtMessages
            this.lblMessage.AutoSize = false;
            this.lblMessage.Location = new Point(70, 20);
            this.lblMessage.Name = "txtMessages";
            this.lblMessage.Size = new Size(310, 60);
            this.lblMessage.TabIndex = 0;
            this.lblMessage.Text = "消息";
            this.lblMessage.TextAlign = ContentAlignment.MiddleLeft;
            this.lblMessage.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            // panelButtons
            this.panelButtons.Dock = DockStyle.Bottom;
            this.panelButtons.Location = new Point(0, 90);
            this.panelButtons.Name = "panelButtons";
            this.panelButtons.Size = new Size(400, 60);
            this.panelButtons.TabIndex = 1;
            this.panelButtons.Padding = new Padding(10, 0, 10, 0);

            // btnOK
            this.btnOK.DialogResult = DialogResult.OK;
            this.btnOK.Location = new Point(150, 15);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new Size(90, 30);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "确定";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new EventHandler(this.btnOK_Click);
            this.btnOK.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // btnCancel
            this.btnCancel.DialogResult = DialogResult.Cancel;
            this.btnCancel.Location = new Point(250, 15);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(90, 30);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "取消";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);
            this.btnCancel.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            // btnCustom
            this.btnCustom.Location = new Point(50, 15);
            this.btnCustom.Name = "btnCustom";
            this.btnCustom.Size = new Size(90, 30);
            this.btnCustom.TabIndex = 2;
            this.btnCustom.Text = "自定义";
            this.btnCustom.UseVisualStyleBackColor = true;
            this.btnCustom.Visible = false;
            this.btnCustom.Click += new EventHandler(this.btnCustom_Click);
            this.btnCustom.Anchor = AnchorStyles.Top | AnchorStyles.Left;

            // pictureBoxIcon
            this.pictureBoxIcon.Location = new Point(15, 20);
            this.pictureBoxIcon.Name = "pictureBoxIcon";
            this.pictureBoxIcon.Size = new Size(32, 32);
            this.pictureBoxIcon.SizeMode = PictureBoxSizeMode.AutoSize;
            this.pictureBoxIcon.TabIndex = 2;
            this.pictureBoxIcon.TabStop = false;
            this.pictureBoxIcon.Padding = new Padding(0);
            this.pictureBoxIcon.Anchor = AnchorStyles.Left;

            // CustomMessageBox
            this.AutoScaleDimensions = new SizeF(120F, 120F);
            this.AutoScaleMode = AutoScaleMode.Dpi;
            this.ClientSize = new Size(400, 160);
            this.Controls.Add(this.pictureBoxIcon);
            this.Controls.Add(this.panelButtons);
            this.Controls.Add(this.lblMessage);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "CustomMessageBox";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "消息";
            this.panelButtons.Controls.Add(this.btnCustom);
            this.panelButtons.Controls.Add(this.btnCancel);
            this.panelButtons.Controls.Add(this.btnOK);
            this.panelButtons.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxIcon)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion
    }
}