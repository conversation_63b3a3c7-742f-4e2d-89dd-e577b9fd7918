namespace PictureMagic
{
    /// <summary>
    /// 标准自定义消息框，可在设计器中编辑
    /// </summary>
    public partial class StandardCustomMessageBox : Form
    {
        // 自定义按钮的事件处理委托
        public delegate void CustomButtonClickEventHandler(object sender, CustomButtonClickEventArgs e);

        // 自定义按钮点击事件
        public event CustomButtonClickEventHandler CustomButtonClick;

        // 自定义按钮点击事件处理程序（内部使用）
        private CustomButtonClickEventHandler customButtonClickHandler;

        // 用户自定义数据
        private object userData;

        /// <summary>
        /// 获取对话框的结果
        /// </summary>
        public DialogResult Result { get; private set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public StandardCustomMessageBox()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 配置消息框
        /// </summary>
        /// <param name="text">消息文本</param>
        /// <param name="caption">标题</param>
        /// <param name="buttons">按钮组合</param>
        /// <param name="icon">图标</param>
        /// <param name="customButtonText">自定义按钮文本</param>
        /// <param name="customButtonClickHandler">自定义按钮点击事件处理程序</param>
        /// <param name="userData">用户自定义数据，将传递给自定义按钮点击事件处理程序</param>
        public void Configure(string text, string caption, MessageBoxButtons buttons, MessageBoxIcon icon, string? customButtonText = null, CustomButtonClickEventHandler? customButtonClickHandler = null, object? userData = null)
        {
            this.Text = caption;
            this.txtMessages.Text = text;
            SetupButtons(buttons);
            SetupIcon(icon);

            // 设置自定义按钮
            if (!string.IsNullOrEmpty(customButtonText))
            {
                this.btnCustom.Text = customButtonText;
                this.btnCustom.Visible = true;
                this.customButtonClickHandler = customButtonClickHandler;
                this.userData = userData;
            }
            else
            {
                this.btnCustom.Visible = false;
            }
        }

        /// <summary>
        /// 显示一个自定义消息框
        /// </summary>
        /// <param name="text">消息文本</param>
        /// <param name="caption">标题</param>
        /// <param name="buttons">按钮组合</param>
        /// <param name="icon">图标</param>
        /// <param name="customButtonText">自定义按钮文本</param>
        /// <param name="customButtonClickHandler">自定义按钮点击事件处理程序</param>
        /// <param name="userData">用户自定义数据，将传递给自定义按钮点击事件处理程序</param>
        /// <returns>对话框结果</returns>
        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons, MessageBoxIcon icon, string? customButtonText = null, CustomButtonClickEventHandler? customButtonClickHandler = null, object? userData = null)
        {
            using (StandardCustomMessageBox messageBox = new StandardCustomMessageBox())
            {
                // 如果提供了事件处理程序，则添加到CustomButtonClick事件
                if (customButtonClickHandler != null)
                {
                    messageBox.CustomButtonClick -= customButtonClickHandler;
                    messageBox.CustomButtonClick += customButtonClickHandler;
                }

                messageBox.Configure(text, caption, buttons, icon, customButtonText, customButtonClickHandler, userData);
                messageBox.ShowDialog();
                return messageBox.Result;
            }
        }

        /// <summary>
        /// 显示一个自定义消息框（不带自定义按钮）
        /// </summary>
        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons, MessageBoxIcon icon)
        {
            return Show(text, caption, buttons, icon, null, null, null);
        }

        /// <summary>
        /// 显示一个自定义消息框（只带消息文本）
        /// </summary>
        public static DialogResult Show(string text)
        {
            return Show(text, "", MessageBoxButtons.OK, MessageBoxIcon.None, null, null, null);
        }

        /// <summary>
        /// 显示一个自定义消息框（带消息文本和标题）
        /// </summary>
        public static DialogResult Show(string text, string caption)
        {
            return Show(text, caption, MessageBoxButtons.OK, MessageBoxIcon.None, null, null, null);
        }

        /// <summary>
        /// 显示一个自定义消息框（带消息文本、标题和按钮）
        /// </summary>
        public static DialogResult Show(string text, string caption, MessageBoxButtons buttons)
        {
            return Show(text, caption, buttons, MessageBoxIcon.None, null, null, null);
        }

        /// <summary>
        /// 设置按钮
        /// </summary>
        private void SetupButtons(MessageBoxButtons buttons)
        {
            // 按钮间距
            const int buttonGap = 10;
            // 按钮宽度
            int buttonWidth = 90;
            // 按钮Y坐标
            int buttonY = 15;
            // 可用宽度（考虑面板的内边距）
            int availableWidth = panelButtons.Width - panelButtons.Padding.Left - panelButtons.Padding.Right;

            // 根据按钮类型设置按钮的可见性和位置
            switch (buttons)
            {
                case MessageBoxButtons.OK:
                    btnOK.Visible = true;
                    btnCancel.Visible = false;
                    btnCustom.Visible = false;
                    // 单个按钮居中
                    btnOK.Location = new Point(panelButtons.Padding.Left + (availableWidth - buttonWidth) / 2, buttonY);
                    break;

                case MessageBoxButtons.OKCancel:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = false;
                    // 两个按钮，右对齐
                    btnCancel.Location = new Point(panelButtons.Width - panelButtons.Padding.Right - buttonWidth, buttonY);
                    btnOK.Location = new Point(btnCancel.Left - buttonGap - buttonWidth, buttonY);
                    break;

                case MessageBoxButtons.YesNo:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = false;
                    btnOK.Text = "是(&Y)";
                    btnCancel.Text = "否(&N)";
                    btnOK.DialogResult = DialogResult.Yes;
                    btnCancel.DialogResult = DialogResult.No;
                    // 两个按钮，右对齐
                    btnCancel.Location = new Point(panelButtons.Width - panelButtons.Padding.Right - buttonWidth, buttonY);
                    btnOK.Location = new Point(btnCancel.Left - buttonGap - buttonWidth, buttonY);
                    break;

                case MessageBoxButtons.YesNoCancel:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = true;
                    btnOK.Text = "是(&Y)";
                    btnCancel.Text = "否(&N)";
                    btnCustom.Text = "取消";
                    btnOK.DialogResult = DialogResult.Yes;
                    btnCancel.DialogResult = DialogResult.No;
                    btnCustom.DialogResult = DialogResult.Cancel;
                    // 三个按钮，均匀分布
                    int threeButtonTotalWidth = 3 * buttonWidth + 2 * buttonGap;
                    int threeButtonStartX = panelButtons.Padding.Left + (availableWidth - threeButtonTotalWidth) / 2;
                    btnOK.Location = new Point(threeButtonStartX, buttonY);
                    btnCancel.Location = new Point(btnOK.Right + buttonGap, buttonY);
                    btnCustom.Location = new Point(btnCancel.Right + buttonGap, buttonY);
                    break;

                case MessageBoxButtons.RetryCancel:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = false;
                    btnOK.Text = "重试(&R)";
                    btnOK.DialogResult = DialogResult.Retry;
                    // 两个按钮，右对齐
                    btnCancel.Location = new Point(panelButtons.Width - panelButtons.Padding.Right - buttonWidth, buttonY);
                    btnOK.Location = new Point(btnCancel.Left - buttonGap - buttonWidth, buttonY);
                    break;

                case MessageBoxButtons.AbortRetryIgnore:
                    btnOK.Visible = true;
                    btnCancel.Visible = true;
                    btnCustom.Visible = true;
                    btnOK.Text = "中止(&A)";
                    btnCancel.Text = "重试(&R)";
                    btnCustom.Text = "忽略(&I)";
                    btnOK.DialogResult = DialogResult.Abort;
                    btnCancel.DialogResult = DialogResult.Retry;
                    btnCustom.DialogResult = DialogResult.Ignore;
                    // 三个按钮，均匀分布
                    int abortRetryIgnoreTotalWidth = 3 * buttonWidth + 2 * buttonGap;
                    int abortRetryIgnoreStartX = panelButtons.Padding.Left + (availableWidth - abortRetryIgnoreTotalWidth) / 2;
                    btnOK.Location = new Point(abortRetryIgnoreStartX, buttonY);
                    btnCancel.Location = new Point(btnOK.Right + buttonGap, buttonY);
                    btnCustom.Location = new Point(btnCancel.Right + buttonGap, buttonY);
                    break;
            }

            // 如果有自定义按钮，调整其他按钮的位置
            if (btnCustom.Visible && buttons != MessageBoxButtons.YesNoCancel && buttons != MessageBoxButtons.AbortRetryIgnore)
            {
                int buttonCount = (btnOK.Visible ? 1 : 0) + (btnCancel.Visible ? 1 : 0) + 1; // +1 for custom button
                int totalWidth = buttonCount * buttonWidth + (buttonCount - 1) * buttonGap;
                int startX = panelButtons.Padding.Left + (availableWidth - totalWidth) / 2;

                // 自定义按钮放在左侧
                btnCustom.Location = new Point(startX, buttonY);
                startX += buttonWidth + buttonGap;

                if (btnOK.Visible)
                {
                    btnOK.Location = new Point(startX, buttonY);
                    startX += buttonWidth + buttonGap;
                }

                if (btnCancel.Visible)
                {
                    btnCancel.Location = new Point(startX, buttonY);
                }
            }
        }

        /// <summary>
        /// 设置图标
        /// </summary>
        private void SetupIcon(MessageBoxIcon icon)
        {
            // 根据图标类型设置图标
            switch (icon)
            {
                case MessageBoxIcon.Asterisk:
                    pictureBoxIcon.Image = SystemIcons.Information.ToBitmap();
                    break;

                case MessageBoxIcon.Exclamation:
                    pictureBoxIcon.Image = SystemIcons.Warning.ToBitmap();
                    break;

                case MessageBoxIcon.Stop:
                    pictureBoxIcon.Image = SystemIcons.Error.ToBitmap();
                    break;

                case MessageBoxIcon.Question:
                    pictureBoxIcon.Image = SystemIcons.Question.ToBitmap();
                    break;

                default: // None or any other value
                    pictureBoxIcon.Visible = false;
                    break;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void btnOK_Click(object sender, EventArgs e)
        {
            this.Result = btnOK.DialogResult;
            this.Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Result = btnCancel.DialogResult;
            this.Close();
        }

        /// <summary>
        /// 自定义按钮点击事件
        /// </summary>
        private void btnCustom_Click(object sender, EventArgs e)
        {
            // 创建自定义事件参数并传递用户数据
            CustomButtonClickEventArgs args = new CustomButtonClickEventArgs(userData);

            // 触发CustomButtonClick事件
            CustomButtonClick?.Invoke(this, args);


            // 如果自定义按钮有DialogResult，则设置结果并关闭对话框
            if (btnCustom.DialogResult != DialogResult.None)
            {
                this.Result = btnCustom.DialogResult;
                this.Close();
            }
        }
    }
}