using System;
using System.Drawing;
using System.Windows.Forms;

namespace PictureMagic
{
    public class CropTool
    {
        private PictureBox pictureBox;
        private Rectangle cropRectangle;
        private bool isSelecting;
        private Point startPoint;
        private Pen cropPen;
        private Image originalImage;
        private Image displayImage;
        private bool isActive;
        private bool isDisposed;

        // 事件处理
        public event EventHandler<CropCompletedEventArgs> CropCompleted = null!;
        public event EventHandler CropCancelled = null!;

        public CropTool(PictureBox pictureBox, Image image)
        {
            this.pictureBox = pictureBox ?? throw new ArgumentNullException(nameof(pictureBox));
            
            // 验证输入图像
            if (image == null)
                throw new ArgumentNullException(nameof(image));
            
            // 创建原始图像的副本，避免外部释放导致的问题
            try
            {
                this.originalImage = new Bitmap(image);
                System.Diagnostics.Debug.WriteLine($"CropTool: 创建原始图像副本，尺寸: {originalImage.Width}x{originalImage.Height}");
            }
            catch (Exception ex)
            {
                throw new ArgumentException($"无法创建图像副本: {ex.Message}", nameof(image));
            }
            
            // 创建显示图像副本
            try
            {
                this.displayImage = new Bitmap(originalImage);
                System.Diagnostics.Debug.WriteLine($"CropTool: 创建显示图像副本，尺寸: {displayImage.Width}x{displayImage.Height}");
            }
            catch (Exception ex)
            {
                // 如果创建显示图像失败，清理已创建的原始图像副本
                originalImage?.Dispose();
                throw new ArgumentException($"无法创建显示图像副本: {ex.Message}", nameof(image));
            }
            
            this.cropPen = new Pen(Color.White, 2)
            {
                DashStyle = System.Drawing.Drawing2D.DashStyle.Dash
            };

            isSelecting = false;
            isActive = false;
            cropRectangle = Rectangle.Empty;
            
            System.Diagnostics.Debug.WriteLine("CropTool: 构造函数完成，图像副本已创建");
        }

        public void Activate()
        {
            if (!isActive)
            {
                try
                {
                    // 验证原始图像是否有效
                    if (originalImage == null)
                    {
                        throw new InvalidOperationException("原始图像为空，无法启动裁剪工具");
                    }

                    if (originalImage.Width <= 0 || originalImage.Height <= 0)
                    {
                        throw new InvalidOperationException("原始图像尺寸无效，无法启动裁剪工具");
                    }

                    // 保存原始图像并创建副本用于显示
                    // 使用标准的32位ARGB格式避免像素格式兼容性问题
                    displayImage = new Bitmap(originalImage.Width, originalImage.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                    using (Graphics g = Graphics.FromImage(displayImage))
                    {
                        g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                        g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        g.DrawImage(originalImage, 0, 0, originalImage.Width, originalImage.Height);
                    }
                    
                    pictureBox.Image = displayImage;

                    // 添加事件处理程序
                    pictureBox.MouseDown += PictureBox_MouseDown;
                    pictureBox.MouseMove += PictureBox_MouseMove;
                    pictureBox.MouseUp += PictureBox_MouseUp;
                    pictureBox.Paint += PictureBox_Paint;

                    // 设置鼠标指针
                    pictureBox.Cursor = Cursors.Cross;

                    isActive = true;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"启动裁剪工具失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    // 确保清理资源
                    if (displayImage != null)
                    {
                        displayImage.Dispose();
                        displayImage = null;
                    }
                }
            }
        }

        public void Deactivate()
        {
            System.Diagnostics.Debug.WriteLine("CropTool.Deactivate: 开始停用裁剪工具");
            
            if (isActive)
            {
                // 移除事件处理程序
                pictureBox.MouseDown -= PictureBox_MouseDown;
                pictureBox.MouseMove -= PictureBox_MouseMove;
                pictureBox.MouseUp -= PictureBox_MouseUp;
                pictureBox.Paint -= PictureBox_Paint;

                // 恢复鼠标指针
                pictureBox.Cursor = Cursors.Default;

                // 在Deactivate方法中，不再尝试设置pictureBox.Image
                // 因为在裁剪完成后，MainForm已经处理了图像更新
                // 这里只需要清理事件和恢复鼠标指针
                try
                {
                    // 不再设置pictureBox.Image，避免ArgumentException
                    // 只记录日志，不进行任何图像操作
                    System.Diagnostics.Debug.WriteLine("CropTool.Deactivate: 跳过图像设置，避免潜在的ArgumentException");
                    
                    // 清除裁剪区域
                    cropRectangle = Rectangle.Empty;
                    
                    // 强制刷新PictureBox，清除裁剪区域的显示
                    if (!pictureBox.IsDisposed && pictureBox.IsHandleCreated)
                    {
                        if (pictureBox.InvokeRequired)
                        {
                            pictureBox.BeginInvoke(new MethodInvoker(() => {
                                pictureBox.Invalidate(true);
                                pictureBox.Update();
                                pictureBox.Refresh();
                                Application.DoEvents();
                            }));
                        }
                        else
                        {
                            pictureBox.Invalidate(true);
                            pictureBox.Update();
                            pictureBox.Refresh();
                            Application.DoEvents();
                        }
                        System.Diagnostics.Debug.WriteLine("CropTool.Deactivate: 已强制刷新PictureBox");
                    }
                }
                catch (Exception ex)
                {
                    // 处理所有可能的异常
                    System.Diagnostics.Debug.WriteLine($"CropTool.Deactivate: 处理过程中发生异常: {ex.Message}");
                }

                // 现在不需要在Deactivate中释放图像资源
                // 因为它们都是我们创建的副本，会在Dispose中统一释放
                System.Diagnostics.Debug.WriteLine("CropTool.Deactivate: 图像资源将在Dispose时释放");

                isActive = false;
                isSelecting = false;
                System.Diagnostics.Debug.WriteLine("CropTool.Deactivate: 裁剪工具已停用");
            }
        }

        private void PictureBox_MouseDown(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                // 确保图像有效
                if (displayImage == null || IsImageDisposed(displayImage))
                {
                    System.Diagnostics.Debug.WriteLine("PictureBox_MouseDown: displayImage为null或已释放");
                    return;
                }

                isSelecting = true;
                startPoint = e.Location;
                cropRectangle = Rectangle.Empty;
                pictureBox.Invalidate();
                System.Diagnostics.Debug.WriteLine("PictureBox_MouseDown: 开始选择裁剪区域");
            }
        }

        private void PictureBox_MouseMove(object? sender, MouseEventArgs e)
        {
            // 确保图像有效
            if (displayImage == null || IsImageDisposed(displayImage))
            {
                return;
            }

            if (isSelecting)
            {
                // 计算选择矩形
                int x = Math.Min(startPoint.X, e.X);
                int y = Math.Min(startPoint.Y, e.Y);
                int width = Math.Abs(e.X - startPoint.X);
                int height = Math.Abs(e.Y - startPoint.Y);

                cropRectangle = new Rectangle(x, y, width, height);
                pictureBox.Invalidate();
            }
        }

        private void PictureBox_MouseUp(object? sender, MouseEventArgs e)
        {
            // 确保图像有效
            if (displayImage == null || IsImageDisposed(displayImage))
            {
                System.Diagnostics.Debug.WriteLine("PictureBox_MouseUp: displayImage为null或已释放");
                isSelecting = false;
                cropRectangle = Rectangle.Empty;
                return;
            }

            if (isSelecting && e.Button == MouseButtons.Left)
            {
                isSelecting = false;
                System.Diagnostics.Debug.WriteLine($"PictureBox_MouseUp: 完成选择裁剪区域 {cropRectangle.Width}x{cropRectangle.Height}");

                // 确保选择区域有效
                if (cropRectangle.Width > 10 && cropRectangle.Height > 10)
                {
                    // 显示确认对话框
                    DialogResult result = MessageBox.Show(
                        "是否裁剪所选区域？",
                        "确认裁剪",
                        MessageBoxButtons.YesNoCancel,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        try
                        {
                            // 执行裁剪
                            PerformCrop();
                            
                            // 注意：不在这里调用Deactivate，因为PerformCrop的finally块中已经调用了
                            // 这样可以确保即使裁剪过程中出现异常，也会正确停用裁剪工具
                        }
                        catch (Exception cropEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"PictureBox_MouseUp: 裁剪过程中出错: {cropEx.Message}");
                            MessageBox.Show($"裁剪过程中出错: {cropEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            
                            // 确保在出错时也停用裁剪工具
                            Deactivate();
                        }
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        // 取消裁剪工具
                        try
                        {
                            CropCancelled?.Invoke(this, EventArgs.Empty);
                        }
                        catch (Exception cancelEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"PictureBox_MouseUp: 取消裁剪时出错: {cancelEx.Message}");
                        }
                        finally
                        {
                            Deactivate();
                        }
                    }
                    else
                    {
                        // 清除选择区域，允许重新选择
                        cropRectangle = Rectangle.Empty;
                        pictureBox.Invalidate();
                    }
                }
                else
                {
                    // 选择区域太小，清除并允许重新选择
                    cropRectangle = Rectangle.Empty;
                    pictureBox.Invalidate();
                }
            }
        }

        private void PictureBox_Paint(object? sender, PaintEventArgs e)
        {
            // 确保图像有效
            if (displayImage == null || IsImageDisposed(displayImage))
            {
                System.Diagnostics.Debug.WriteLine("PictureBox_Paint: displayImage为null或已释放");
                return;
            }

            if (cropRectangle != Rectangle.Empty)
            {
                try
                {
                    // 绘制选择矩形
                    e.Graphics.DrawRectangle(cropPen, cropRectangle);

                    // 绘制半透明遮罩
                    using (SolidBrush brush = new SolidBrush(Color.FromArgb(128, 0, 0, 0)))
                    {
                        // 上方区域
                        e.Graphics.FillRectangle(brush, 0, 0, pictureBox.Width, cropRectangle.Top);
                        // 下方区域
                        e.Graphics.FillRectangle(brush, 0, cropRectangle.Bottom, pictureBox.Width, pictureBox.Height - cropRectangle.Bottom);
                        // 左侧区域
                        e.Graphics.FillRectangle(brush, 0, cropRectangle.Top, cropRectangle.Left, cropRectangle.Height);
                        // 右侧区域
                        e.Graphics.FillRectangle(brush, cropRectangle.Right, cropRectangle.Top, pictureBox.Width - cropRectangle.Right, cropRectangle.Height);
                    }
                    
                    // 显示尺寸信息
                    string sizeInfo = $"{cropRectangle.Width} x {cropRectangle.Height}";
                    using (Font font = new Font("Arial", 8))
                    using (SolidBrush textBrush = new SolidBrush(Color.White))
                    using (SolidBrush bgBrush = new SolidBrush(Color.FromArgb(128, 0, 0, 0)))
                    {
                        SizeF textSize = e.Graphics.MeasureString(sizeInfo, font);
                        float textX = cropRectangle.Right - textSize.Width - 5;
                        float textY = cropRectangle.Bottom + 5;

                        // 确保文本在PictureBox范围内
                        if (textY + textSize.Height > pictureBox.Height)
                        {
                            textY = cropRectangle.Top - textSize.Height - 5;
                        }

                        // 绘制背景和文本
                        e.Graphics.FillRectangle(bgBrush, textX - 2, textY - 2, textSize.Width + 4, textSize.Height + 4);
                        e.Graphics.DrawString(sizeInfo, font, textBrush, textX, textY);
                    }
                    
                    System.Diagnostics.Debug.WriteLine("PictureBox_Paint: 绘制裁剪区域和尺寸信息");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"PictureBox_Paint: 绘制裁剪区域时出错: {ex.Message}");
                }
            }
        }

        private void PerformCrop()
        {
            try
            {
                // 验证原始图像是否有效
                if (originalImage == null || IsImageDisposed(originalImage))
                {
                    MessageBox.Show("没有可裁剪的图像或图像已被释放", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    System.Diagnostics.Debug.WriteLine("PerformCrop: 图像为null或已释放");
                    return;
                }

                // 将PictureBox坐标转换为图像坐标
                Rectangle imageRect = GetImageRectangleInPictureBox();
                Rectangle cropImageRect = ConvertToImageCoordinates(cropRectangle, imageRect);
                System.Diagnostics.Debug.WriteLine($"PerformCrop: 转换后的图像坐标: {cropImageRect}");
                
                // 检查转换结果是否有效
                if (cropImageRect.IsEmpty)
                {
                    MessageBox.Show("无法计算有效的裁剪区域，请重新选择", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 确保裁剪区域在图像范围内
                cropImageRect.Intersect(new Rectangle(0, 0, originalImage.Width, originalImage.Height));

                // 验证裁剪区域的有效性
                if (cropImageRect.Width <= 0 || cropImageRect.Height <= 0)
                {
                    MessageBox.Show("裁剪区域无效，请重新选择", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 验证裁剪区域不能超出图像边界
                if (cropImageRect.X < 0 || cropImageRect.Y < 0 || 
                    cropImageRect.Right > originalImage.Width || cropImageRect.Bottom > originalImage.Height)
                {
                    // 重新调整裁剪区域到图像边界内
                    cropImageRect.X = Math.Max(0, cropImageRect.X);
                    cropImageRect.Y = Math.Max(0, cropImageRect.Y);
                    cropImageRect.Width = Math.Min(cropImageRect.Width, originalImage.Width - cropImageRect.X);
                    cropImageRect.Height = Math.Min(cropImageRect.Height, originalImage.Height - cropImageRect.Y);
                }

                // 最终验证裁剪区域
                if (cropImageRect.Width > 0 && cropImageRect.Height > 0)
                {
                    try
                    {
                        // 额外的参数验证，确保尺寸在合理范围内
                        if (cropImageRect.Width > 32767 || cropImageRect.Height > 32767)
                        {
                            MessageBox.Show("裁剪区域过大，请选择较小的区域", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                        
                        if (cropImageRect.Width < 1 || cropImageRect.Height < 1)
                        {
                            MessageBox.Show("裁剪区域太小，请选择较大的区域", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            return;
                        }
                        
                        // 创建裁剪后的图像，使用32位ARGB格式确保兼容性
                        System.Diagnostics.Debug.WriteLine($"PerformCrop: 开始创建裁剪图像，尺寸: {cropImageRect.Width}x{cropImageRect.Height}");
                        Bitmap croppedImage = new Bitmap(cropImageRect.Width, cropImageRect.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                        System.Diagnostics.Debug.WriteLine($"PerformCrop: 裁剪图像对象已创建，像素格式: {croppedImage.PixelFormat}");
                        Graphics g = Graphics.FromImage(croppedImage);
                        try
                        {
                            // 设置高质量绘图参数
                            g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                            g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                            g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                            
                            // 绘制裁剪区域到新图像前，再次验证源矩形区域
                            System.Diagnostics.Debug.WriteLine($"PerformCrop: 开始绘制，源区域: {cropImageRect}, 目标区域: {new Rectangle(0, 0, cropImageRect.Width, cropImageRect.Height)}");
                            System.Diagnostics.Debug.WriteLine($"PerformCrop: 原始图像尺寸: {originalImage.Width}x{originalImage.Height}");
                            
                            // 确保源矩形完全在原始图像范围内
                            if (cropImageRect.X < 0 || cropImageRect.Y < 0 || 
                                cropImageRect.Right > originalImage.Width || cropImageRect.Bottom > originalImage.Height)
                            {
                                System.Diagnostics.Debug.WriteLine($"PerformCrop: 源矩形超出图像范围，调整中...");
                                // 调整源矩形以确保在图像范围内
                                int adjustedX = Math.Max(0, cropImageRect.X);
                                int adjustedY = Math.Max(0, cropImageRect.Y);
                                int adjustedWidth = Math.Min(cropImageRect.Width, originalImage.Width - adjustedX);
                                int adjustedHeight = Math.Min(cropImageRect.Height, originalImage.Height - adjustedY);
                                
                                if (adjustedWidth <= 0 || adjustedHeight <= 0)
                                {
                                    throw new ArgumentException("调整后的裁剪区域无效");
                                }
                                
                                cropImageRect = new Rectangle(adjustedX, adjustedY, adjustedWidth, adjustedHeight);
                                System.Diagnostics.Debug.WriteLine($"PerformCrop: 调整后的源区域: {cropImageRect}");
                                
                                // 重新创建目标图像以匹配调整后的尺寸
                                croppedImage.Dispose();
                                croppedImage = new Bitmap(cropImageRect.Width, cropImageRect.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                                g.Dispose();
                                g = Graphics.FromImage(croppedImage);
                                g.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                                g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                                g.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                                                    }
                            // 绘制图像
                            try
                            {
                                // 再次验证所有参数
                                if (originalImage == null || IsImageDisposed(originalImage))
                                {
                                    throw new ArgumentException("原始图像无效或已释放");
                                }
                                
                                // 确保源矩形完全有效
                                if (cropImageRect.Width <= 0 || cropImageRect.Height <= 0 ||
                                    cropImageRect.X < 0 || cropImageRect.Y < 0 ||
                                    cropImageRect.Right > originalImage.Width || cropImageRect.Bottom > originalImage.Height)
                                {
                                    throw new ArgumentException("裁剪区域参数无效");
                                }
                                
                                // 直接使用Clone方法裁剪图像，避免多次复制和绘制
                                try
                                {
                                    // 释放之前创建的空白图像
                                    if (croppedImage != null)
                                    {
                                        croppedImage.Dispose();
                                        croppedImage = null;
                                    }
                                    
                                    // 直接从原始图像克隆裁剪区域
                                // 使用正确的Clone方法重载
                                if (originalImage is Bitmap bmpOriginal)
                                {
                                    croppedImage = bmpOriginal.Clone(cropImageRect, bmpOriginal.PixelFormat);
                                }
                                else
                                {
                                    // 如果不是Bitmap类型，先转换为Bitmap
                                    using (Bitmap tempBitmap = new Bitmap(originalImage))
                                    {
                                        croppedImage = tempBitmap.Clone(cropImageRect, tempBitmap.PixelFormat);
                                    }
                                }
                                    System.Diagnostics.Debug.WriteLine($"PerformCrop: 直接使用Clone方法裁剪图像成功，尺寸: {croppedImage.Width}x{croppedImage.Height}");
                                }
                                catch (OutOfMemoryException memEx)
                                {
                                    System.Diagnostics.Debug.WriteLine($"PerformCrop: 内存不足，尝试替代方法: {memEx.Message}");
                                    
                                    // 如果直接克隆失败，尝试使用两步法
                                    // 1. 创建新的空白图像
                                    croppedImage = new Bitmap(cropImageRect.Width, cropImageRect.Height, System.Drawing.Imaging.PixelFormat.Format32bppArgb);
                                    
                                    // 2. 使用Graphics绘制
                                    using (Graphics gDest = Graphics.FromImage(croppedImage))
                                    {
                                        gDest.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                                        gDest.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                                        gDest.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                                        gDest.PixelOffsetMode = System.Drawing.Drawing2D.PixelOffsetMode.HighQuality;
                                        gDest.DrawImage(originalImage, 
                                                       new Rectangle(0, 0, cropImageRect.Width, cropImageRect.Height),
                                                       cropImageRect,
                                                       GraphicsUnit.Pixel);
                                    }
                                    System.Diagnostics.Debug.WriteLine("PerformCrop: 使用替代方法裁剪图像成功");
                                }
                                
                                System.Diagnostics.Debug.WriteLine("PerformCrop: 图像裁剪完成（使用Clone方法）");
                            }
                            catch (Exception drawEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"PerformCrop: 图像裁剪失败: {drawEx.Message}");
                                throw new InvalidOperationException($"裁剪图像时出错: {drawEx.Message}", drawEx);
                            }
                        }
                        finally
                        {
                            if (g != null)
                            {
                                g.Dispose();
                            }
                        }

                        System.Diagnostics.Debug.WriteLine($"PerformCrop: 成功创建裁剪图像 {croppedImage.Width}x{croppedImage.Height}");
                        
                        // 确保裁剪图像有效
                        if (croppedImage != null && !IsImageDisposed(croppedImage))
                        {
                            System.Diagnostics.Debug.WriteLine($"PerformCrop: 准备触发CropCompleted事件，图像尺寸: {croppedImage.Width}x{croppedImage.Height}");
                            
                            try
                            {
                                // 创建一个新的Bitmap作为事件参数，避免资源释放问题
                                Bitmap resultImage = new Bitmap(croppedImage);
                                System.Diagnostics.Debug.WriteLine($"PerformCrop: 创建了裁剪结果的副本，尺寸: {resultImage.Width}x{resultImage.Height}");
                                
                                // 创建事件参数并保存对裁剪图像的引用
                                CropCompletedEventArgs args = new CropCompletedEventArgs(resultImage);
                                
                                // 触发裁剪完成事件
                                System.Diagnostics.Debug.WriteLine("PerformCrop: 准备触发CropCompleted事件");
                                CropCompleted?.Invoke(this, args);
                                
                                // 给UI线程一些时间来处理事件
                                System.Threading.Thread.Sleep(100);
                                
                                System.Diagnostics.Debug.WriteLine("PerformCrop: CropCompleted事件已触发，等待UI更新");
                                
                                // 确保在返回前所有挂起的消息都被处理
                                if (pictureBox.InvokeRequired)
                                {
                                    System.Diagnostics.Debug.WriteLine("PerformCrop: 在非UI线程，无法直接处理消息队列");
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine("PerformCrop: 在UI线程，处理挂起的消息");
                                    Application.DoEvents();
                                }
                            }
                            catch (Exception eventEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"PerformCrop: 触发事件时出错: {eventEx.Message}");
                                MessageBox.Show($"无法完成裁剪操作: {eventEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("PerformCrop: 裁剪图像无效，无法触发事件");
                            MessageBox.Show("裁剪后的图像无效", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                }
                catch (Exception ex)
                {
                        System.Diagnostics.Debug.WriteLine($"PerformCrop: 创建裁剪图像时出错: {ex.Message}");
                        MessageBox.Show($"创建裁剪图像时出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                else
                {
                    MessageBox.Show("裁剪区域太小，无法执行裁剪", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"裁剪过程中出错: {ex.Message}\n详细信息: {ex.StackTrace}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 停用裁剪工具
                Deactivate();
            }

        Rectangle GetImageRectangleInPictureBox()
        {
            // 计算图像在PictureBox中的实际显示区域
            if (originalImage == null || IsImageDisposed(originalImage) || pictureBox.Width <= 0 || pictureBox.Height <= 0)
            {
                System.Diagnostics.Debug.WriteLine("GetImageRectangleInPictureBox: 图像为null或已释放");
                return Rectangle.Empty;
            }

            Rectangle result = new Rectangle(0, 0, pictureBox.Width, pictureBox.Height);

            if (pictureBox.SizeMode == PictureBoxSizeMode.Zoom)
            {
                try
                {
                    if (originalImage.Width <= 0 || originalImage.Height <= 0)
                    {
                        System.Diagnostics.Debug.WriteLine("GetImageRectangleInPictureBox: 图像尺寸无效");
                        return result;
                    }

                    float imageRatio = (float)originalImage.Width / originalImage.Height;
                    float boxRatio = (float)pictureBox.Width / pictureBox.Height;

                    if (imageRatio > boxRatio)
                    {
                        // 图像宽度适应PictureBox宽度
                        int height = (int)(pictureBox.Width / imageRatio);
                        int y = (pictureBox.Height - height) / 2;
                        result = new Rectangle(0, y, pictureBox.Width, height);
                    }
                    else
                    {
                        // 图像高度适应PictureBox高度
                        int width = (int)(pictureBox.Height * imageRatio);
                        int x = (pictureBox.Width - width) / 2;
                        result = new Rectangle(x, 0, width, pictureBox.Height);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"GetImageRectangleInPictureBox异常: {ex.Message}");
                    return result;
                }
            }

            return result;
        }
        }

        private Rectangle ConvertToImageCoordinates(Rectangle rectPictureBox, Rectangle imageRect)
        {
            // 将PictureBox中的坐标转换为原始图像中的坐标
            if (imageRect.Width <= 0 || imageRect.Height <= 0 || originalImage == null || IsImageDisposed(originalImage))
            {
                System.Diagnostics.Debug.WriteLine("ConvertToImageCoordinates: 图像无效或已释放");
                return Rectangle.Empty;
            }

            try
            {
                float xRatio = (float)originalImage.Width / imageRect.Width;
                float yRatio = (float)originalImage.Height / imageRect.Height;

                int x = (int)((rectPictureBox.X - imageRect.X) * xRatio);
                int y = (int)((rectPictureBox.Y - imageRect.Y) * yRatio);
                int width = (int)(rectPictureBox.Width * xRatio);
                int height = (int)(rectPictureBox.Height * yRatio);

                // 确保返回的坐标不为负数
                x = Math.Max(0, x);
                y = Math.Max(0, y);
                width = Math.Max(1, width);
                height = Math.Max(1, height);
                
                // 确保坐标和尺寸在合理范围内
                if (x >= originalImage.Width || y >= originalImage.Height)
                {
                    System.Diagnostics.Debug.WriteLine($"ConvertToImageCoordinates: 坐标超出图像范围 x={x}, y={y}, 图像尺寸={originalImage.Width}x{originalImage.Height}");
                    return Rectangle.Empty;
                }
                
                // 调整宽度和高度，确保不超出图像边界
                width = Math.Min(width, originalImage.Width - x);
                height = Math.Min(height, originalImage.Height - y);
                
                // 最终验证
                if (width <= 0 || height <= 0)
                {
                    System.Diagnostics.Debug.WriteLine($"ConvertToImageCoordinates: 计算出的尺寸无效 width={width}, height={height}");
                    return Rectangle.Empty;
                }

                Rectangle result = new Rectangle(x, y, width, height);
                System.Diagnostics.Debug.WriteLine($"ConvertToImageCoordinates: 转换结果 {result}");
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ConvertToImageCoordinates异常: {ex.Message}");
                return Rectangle.Empty;
            }
        }

        public void Dispose()
        {
            System.Diagnostics.Debug.WriteLine("CropTool.Dispose: 开始释放资源");
            
            if (isDisposed)
            {
                System.Diagnostics.Debug.WriteLine("CropTool.Dispose: 已经释放过，跳过");
                return;
            }
            
            try
            {
                // 停用裁剪工具
                try
                {
                    Deactivate();
                    System.Diagnostics.Debug.WriteLine("CropTool.Dispose: 已调用Deactivate");
                }
                catch (ArgumentException argEx)
                {
                    System.Diagnostics.Debug.WriteLine($"CropTool.Dispose: Deactivate时发生ArgumentException - {argEx.Message}");
                }
                catch (Exception deactivateEx)
                {
                    System.Diagnostics.Debug.WriteLine($"CropTool.Dispose: Deactivate时发生异常 - {deactivateEx.Message}");
                }

                // 释放画笔资源
                if (cropPen != null)
                {
                    System.Diagnostics.Debug.WriteLine("CropTool.Dispose: 释放cropPen资源");
                    cropPen.Dispose();
                    cropPen = null;
                }
                
                // 现在originalImage是我们创建的副本，需要释放
                if (originalImage != null)
                {
                    System.Diagnostics.Debug.WriteLine("CropTool.Dispose: 释放原始图像副本");
                    originalImage.Dispose();
                    originalImage = null;
                }
                
                // 释放显示图像副本
                if (displayImage != null)
                {
                    System.Diagnostics.Debug.WriteLine("CropTool.Dispose: 释放显示图像副本");
                    displayImage.Dispose();
                    displayImage = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CropTool.Dispose: 释放资源时出错: {ex.Message}");
            }
            finally
            {
                // 标记为已释放
                isDisposed = true;
                System.Diagnostics.Debug.WriteLine("CropTool.Dispose: 资源释放完成");
            }
        }
        
        // 检查图像是否已被释放
        private bool IsImageDisposed(Image image)
        {
            try
            {
                // 尝试访问图像的属性，如果图像已被释放，会抛出异常
                var width = image.Width;
                var height = image.Height;
                return width <= 0 || height <= 0;
            }
            catch
            {
                // 如果访问属性时抛出异常，说明图像已被释放
                return true;
            }
        }
    }

    public class CropCompletedEventArgs : EventArgs
    {
        public Image CroppedImage { get; private set; }

        public CropCompletedEventArgs(Image croppedImage)
        {
            CroppedImage = croppedImage;
        }
    }
}