namespace PictureMagic
{
    partial class TextDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(TextDialog));
            splitContainer = new SplitContainer();
            labelHint = new Label();
            groupBoxEffects = new GroupBox();
            labelRotationValue = new Label();
            trackBarRotation = new TrackBar();
            labelRotation = new Label();
            labelOpacityValue = new Label();
            trackBarOpacity = new TrackBar();
            labelOpacity = new Label();
            groupBoxAlignment = new GroupBox();
            comboBoxVAlign = new ComboBox();
            labelVAlign = new Label();
            comboBoxHAlign = new ComboBox();
            labelHAlign = new Label();
            groupBoxTextProperties = new GroupBox();
            buttonOutlineColor = new Button();
            labelOutlineColor = new Label();
            labelOutlineWidthValue = new Label();
            trackBarOutlineWidth = new TrackBar();
            labelOutlineWidth = new Label();
            checkBoxOutline = new CheckBox();
            buttonTextColor = new Button();
            labelTextColor = new Label();
            flowLayoutPanelFontStyle = new FlowLayoutPanel();
            buttonBold = new Button();
            buttonItalic = new Button();
            buttonUnderline = new Button();
            comboBoxFontSize = new ComboBox();
            labelFontSize = new Label();
            comboBoxFont = new ComboBox();
            labelFont = new Label();
            textBoxContent = new TextBox();
            labelContent = new Label();
            pictureBoxPreview = new PictureBox();
            panel2 = new Panel();
            buttonCancel = new Button();
            buttonOK = new Button();
            ((System.ComponentModel.ISupportInitialize)splitContainer).BeginInit();
            splitContainer.Panel1.SuspendLayout();
            splitContainer.Panel2.SuspendLayout();
            splitContainer.SuspendLayout();
            groupBoxEffects.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarRotation).BeginInit();
            ((System.ComponentModel.ISupportInitialize)trackBarOpacity).BeginInit();
            groupBoxAlignment.SuspendLayout();
            groupBoxTextProperties.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarOutlineWidth).BeginInit();
            flowLayoutPanelFontStyle.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).BeginInit();
            panel2.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer
            // 
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Location = new Point(0, 0);
            splitContainer.Margin = new Padding(4);
            splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            splitContainer.Panel1.Controls.Add(labelHint);
            splitContainer.Panel1.Controls.Add(groupBoxEffects);
            splitContainer.Panel1.Controls.Add(groupBoxAlignment);
            splitContainer.Panel1.Controls.Add(groupBoxTextProperties);
            splitContainer.Panel1MinSize = 250;
            // 
            // splitContainer.Panel2
            // 
            splitContainer.Panel2.Controls.Add(pictureBoxPreview);
            splitContainer.Panel2.Controls.Add(panel2);
            splitContainer.Size = new Size(1157, 800);
            splitContainer.SplitterDistance = 385;
            splitContainer.SplitterWidth = 5;
            splitContainer.TabIndex = 0;
            // 
            // labelHint
            // 
            labelHint.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            labelHint.ForeColor = SystemColors.GrayText;
            labelHint.Location = new Point(15, 683);
            labelHint.Margin = new Padding(4, 0, 4, 0);
            labelHint.Name = "labelHint";
            labelHint.Size = new Size(354, 51);
            labelHint.TabIndex = 3;
            labelHint.Text = "提示：点击图像可以设置文本位置，拖动可以移动文本。";
            // 
            // groupBoxEffects
            // 
            groupBoxEffects.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxEffects.Controls.Add(labelRotationValue);
            groupBoxEffects.Controls.Add(trackBarRotation);
            groupBoxEffects.Controls.Add(labelRotation);
            groupBoxEffects.Controls.Add(labelOpacityValue);
            groupBoxEffects.Controls.Add(trackBarOpacity);
            groupBoxEffects.Controls.Add(labelOpacity);
            groupBoxEffects.Location = new Point(15, 519);
            groupBoxEffects.Margin = new Padding(4);
            groupBoxEffects.Name = "groupBoxEffects";
            groupBoxEffects.Padding = new Padding(4);
            groupBoxEffects.Size = new Size(354, 160);
            groupBoxEffects.TabIndex = 2;
            groupBoxEffects.TabStop = false;
            groupBoxEffects.Text = "特效";
            // 
            // labelRotationValue
            // 
            labelRotationValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            labelRotationValue.AutoSize = true;
            labelRotationValue.Location = new Point(302, 93);
            labelRotationValue.Margin = new Padding(4, 0, 4, 0);
            labelRotationValue.Name = "labelRotationValue";
            labelRotationValue.Size = new Size(24, 20);
            labelRotationValue.TabIndex = 5;
            labelRotationValue.Text = "0°";
            // 
            // trackBarRotation
            // 
            trackBarRotation.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            trackBarRotation.Location = new Point(103, 94);
            trackBarRotation.Margin = new Padding(4);
            trackBarRotation.Maximum = 360;
            trackBarRotation.Name = "trackBarRotation";
            trackBarRotation.Size = new Size(192, 56);
            trackBarRotation.TabIndex = 4;
            trackBarRotation.Scroll += trackBarRotation_Scroll;
            // 
            // labelRotation
            // 
            labelRotation.AutoSize = true;
            labelRotation.Location = new Point(8, 95);
            labelRotation.Margin = new Padding(4, 0, 4, 0);
            labelRotation.Name = "labelRotation";
            labelRotation.Size = new Size(84, 20);
            labelRotation.TabIndex = 3;
            labelRotation.Text = "旋转角度：";
            // 
            // labelOpacityValue
            // 
            labelOpacityValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            labelOpacityValue.AutoSize = true;
            labelOpacityValue.Location = new Point(306, 33);
            labelOpacityValue.Margin = new Padding(4, 0, 4, 0);
            labelOpacityValue.Name = "labelOpacityValue";
            labelOpacityValue.Size = new Size(36, 20);
            labelOpacityValue.TabIndex = 2;
            labelOpacityValue.Text = "100";
            // 
            // trackBarOpacity
            // 
            trackBarOpacity.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            trackBarOpacity.Location = new Point(103, 29);
            trackBarOpacity.Margin = new Padding(4);
            trackBarOpacity.Maximum = 100;
            trackBarOpacity.Name = "trackBarOpacity";
            trackBarOpacity.Size = new Size(192, 56);
            trackBarOpacity.TabIndex = 1;
            trackBarOpacity.Value = 100;
            trackBarOpacity.Scroll += trackBarOpacity_Scroll;
            // 
            // labelOpacity
            // 
            labelOpacity.AutoSize = true;
            labelOpacity.Location = new Point(8, 33);
            labelOpacity.Margin = new Padding(4, 0, 4, 0);
            labelOpacity.Name = "labelOpacity";
            labelOpacity.Size = new Size(84, 20);
            labelOpacity.TabIndex = 0;
            labelOpacity.Text = "不透明度：";
            // 
            // groupBoxAlignment
            // 
            groupBoxAlignment.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxAlignment.Controls.Add(comboBoxVAlign);
            groupBoxAlignment.Controls.Add(labelVAlign);
            groupBoxAlignment.Controls.Add(comboBoxHAlign);
            groupBoxAlignment.Controls.Add(labelHAlign);
            groupBoxAlignment.Location = new Point(15, 397);
            groupBoxAlignment.Margin = new Padding(4);
            groupBoxAlignment.Name = "groupBoxAlignment";
            groupBoxAlignment.Padding = new Padding(4);
            groupBoxAlignment.Size = new Size(354, 113);
            groupBoxAlignment.TabIndex = 1;
            groupBoxAlignment.TabStop = false;
            groupBoxAlignment.Text = "对齐方式";
            // 
            // comboBoxVAlign
            // 
            comboBoxVAlign.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            comboBoxVAlign.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxVAlign.FormattingEnabled = true;
            comboBoxVAlign.Location = new Point(103, 68);
            comboBoxVAlign.Margin = new Padding(4);
            comboBoxVAlign.Name = "comboBoxVAlign";
            comboBoxVAlign.Size = new Size(242, 28);
            comboBoxVAlign.TabIndex = 3;
            comboBoxVAlign.SelectedIndexChanged += comboBoxVAlign_SelectedIndexChanged;
            // 
            // labelVAlign
            // 
            labelVAlign.AutoSize = true;
            labelVAlign.Location = new Point(8, 72);
            labelVAlign.Margin = new Padding(4, 0, 4, 0);
            labelVAlign.Name = "labelVAlign";
            labelVAlign.Size = new Size(84, 20);
            labelVAlign.TabIndex = 2;
            labelVAlign.Text = "垂直对齐：";
            // 
            // comboBoxHAlign
            // 
            comboBoxHAlign.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            comboBoxHAlign.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxHAlign.FormattingEnabled = true;
            comboBoxHAlign.Location = new Point(103, 29);
            comboBoxHAlign.Margin = new Padding(4);
            comboBoxHAlign.Name = "comboBoxHAlign";
            comboBoxHAlign.Size = new Size(242, 28);
            comboBoxHAlign.TabIndex = 1;
            comboBoxHAlign.SelectedIndexChanged += comboBoxHAlign_SelectedIndexChanged;
            // 
            // labelHAlign
            // 
            labelHAlign.AutoSize = true;
            labelHAlign.Location = new Point(8, 33);
            labelHAlign.Margin = new Padding(4, 0, 4, 0);
            labelHAlign.Name = "labelHAlign";
            labelHAlign.Size = new Size(84, 20);
            labelHAlign.TabIndex = 0;
            labelHAlign.Text = "水平对齐：";
            // 
            // groupBoxTextProperties
            // 
            groupBoxTextProperties.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            groupBoxTextProperties.Controls.Add(buttonOutlineColor);
            groupBoxTextProperties.Controls.Add(labelOutlineColor);
            groupBoxTextProperties.Controls.Add(labelOutlineWidthValue);
            groupBoxTextProperties.Controls.Add(trackBarOutlineWidth);
            groupBoxTextProperties.Controls.Add(labelOutlineWidth);
            groupBoxTextProperties.Controls.Add(checkBoxOutline);
            groupBoxTextProperties.Controls.Add(buttonTextColor);
            groupBoxTextProperties.Controls.Add(labelTextColor);
            groupBoxTextProperties.Controls.Add(flowLayoutPanelFontStyle);
            groupBoxTextProperties.Controls.Add(comboBoxFontSize);
            groupBoxTextProperties.Controls.Add(labelFontSize);
            groupBoxTextProperties.Controls.Add(comboBoxFont);
            groupBoxTextProperties.Controls.Add(labelFont);
            groupBoxTextProperties.Controls.Add(textBoxContent);
            groupBoxTextProperties.Controls.Add(labelContent);
            groupBoxTextProperties.Location = new Point(15, 16);
            groupBoxTextProperties.Margin = new Padding(4);
            groupBoxTextProperties.Name = "groupBoxTextProperties";
            groupBoxTextProperties.Padding = new Padding(4);
            groupBoxTextProperties.Size = new Size(354, 373);
            groupBoxTextProperties.TabIndex = 0;
            groupBoxTextProperties.TabStop = false;
            groupBoxTextProperties.Text = "文本属性";
            // 
            // buttonOutlineColor
            // 
            buttonOutlineColor.BackColor = Color.White;
            buttonOutlineColor.Location = new Point(103, 318);
            buttonOutlineColor.Margin = new Padding(4);
            buttonOutlineColor.Name = "buttonOutlineColor";
            buttonOutlineColor.Size = new Size(90, 31);
            buttonOutlineColor.TabIndex = 14;
            buttonOutlineColor.UseVisualStyleBackColor = false;
            buttonOutlineColor.Click += buttonOutlineColor_Click;
            // 
            // labelOutlineColor
            // 
            labelOutlineColor.AutoSize = true;
            labelOutlineColor.Location = new Point(8, 324);
            labelOutlineColor.Margin = new Padding(4, 0, 4, 0);
            labelOutlineColor.Name = "labelOutlineColor";
            labelOutlineColor.Size = new Size(84, 20);
            labelOutlineColor.TabIndex = 13;
            labelOutlineColor.Text = "轮廓颜色：";
            // 
            // labelOutlineWidthValue
            // 
            labelOutlineWidthValue.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            labelOutlineWidthValue.AutoSize = true;
            labelOutlineWidthValue.Location = new Point(302, 259);
            labelOutlineWidthValue.Margin = new Padding(4, 0, 4, 0);
            labelOutlineWidthValue.Name = "labelOutlineWidthValue";
            labelOutlineWidthValue.Size = new Size(36, 20);
            labelOutlineWidthValue.TabIndex = 12;
            labelOutlineWidthValue.Text = "2px";
            // 
            // trackBarOutlineWidth
            // 
            trackBarOutlineWidth.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            trackBarOutlineWidth.Location = new Point(103, 258);
            trackBarOutlineWidth.Margin = new Padding(4);
            trackBarOutlineWidth.Minimum = 1;
            trackBarOutlineWidth.Name = "trackBarOutlineWidth";
            trackBarOutlineWidth.Size = new Size(192, 56);
            trackBarOutlineWidth.TabIndex = 11;
            trackBarOutlineWidth.Value = 2;
            trackBarOutlineWidth.Scroll += trackBarOutlineWidth_Scroll;
            // 
            // labelOutlineWidth
            // 
            labelOutlineWidth.AutoSize = true;
            labelOutlineWidth.Location = new Point(8, 263);
            labelOutlineWidth.Margin = new Padding(4, 0, 4, 0);
            labelOutlineWidth.Name = "labelOutlineWidth";
            labelOutlineWidth.Size = new Size(84, 20);
            labelOutlineWidth.TabIndex = 10;
            labelOutlineWidth.Text = "轮廓宽度：";
            // 
            // checkBoxOutline
            // 
            checkBoxOutline.AutoSize = true;
            checkBoxOutline.Location = new Point(8, 223);
            checkBoxOutline.Margin = new Padding(4);
            checkBoxOutline.Name = "checkBoxOutline";
            checkBoxOutline.Size = new Size(91, 24);
            checkBoxOutline.TabIndex = 9;
            checkBoxOutline.Text = "添加轮廓";
            checkBoxOutline.UseVisualStyleBackColor = true;
            checkBoxOutline.CheckedChanged += checkBoxOutline_CheckedChanged;
            // 
            // buttonTextColor
            // 
            buttonTextColor.BackColor = Color.Black;
            buttonTextColor.Location = new Point(71, 182);
            buttonTextColor.Margin = new Padding(4);
            buttonTextColor.Name = "buttonTextColor";
            buttonTextColor.Size = new Size(90, 31);
            buttonTextColor.TabIndex = 8;
            buttonTextColor.UseVisualStyleBackColor = false;
            buttonTextColor.Click += buttonTextColor_Click;
            // 
            // labelTextColor
            // 
            labelTextColor.AutoSize = true;
            labelTextColor.Location = new Point(8, 186);
            labelTextColor.Margin = new Padding(4, 0, 4, 0);
            labelTextColor.Name = "labelTextColor";
            labelTextColor.Size = new Size(54, 20);
            labelTextColor.TabIndex = 7;
            labelTextColor.Text = "颜色：";
            // 
            // flowLayoutPanelFontStyle
            // 
            flowLayoutPanelFontStyle.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            flowLayoutPanelFontStyle.Controls.Add(buttonBold);
            flowLayoutPanelFontStyle.Controls.Add(buttonItalic);
            flowLayoutPanelFontStyle.Controls.Add(buttonUnderline);
            flowLayoutPanelFontStyle.Location = new Point(169, 128);
            flowLayoutPanelFontStyle.Margin = new Padding(4);
            flowLayoutPanelFontStyle.Name = "flowLayoutPanelFontStyle";
            flowLayoutPanelFontStyle.Size = new Size(177, 43);
            flowLayoutPanelFontStyle.TabIndex = 6;
            // 
            // buttonBold
            // 
            buttonBold.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            buttonBold.Location = new Point(4, 4);
            buttonBold.Margin = new Padding(4);
            buttonBold.Name = "buttonBold";
            buttonBold.Size = new Size(51, 31);
            buttonBold.TabIndex = 0;
            buttonBold.Text = "B";
            buttonBold.UseVisualStyleBackColor = true;
            buttonBold.Click += buttonBold_Click;
            // 
            // buttonItalic
            // 
            buttonItalic.Font = new Font("Segoe UI", 9F, FontStyle.Italic);
            buttonItalic.Location = new Point(63, 4);
            buttonItalic.Margin = new Padding(4);
            buttonItalic.Name = "buttonItalic";
            buttonItalic.Size = new Size(51, 31);
            buttonItalic.TabIndex = 1;
            buttonItalic.Text = "I";
            buttonItalic.UseVisualStyleBackColor = true;
            buttonItalic.Click += buttonItalic_Click;
            // 
            // buttonUnderline
            // 
            buttonUnderline.Font = new Font("Segoe UI", 9F, FontStyle.Underline);
            buttonUnderline.Location = new Point(122, 4);
            buttonUnderline.Margin = new Padding(4);
            buttonUnderline.Name = "buttonUnderline";
            buttonUnderline.Size = new Size(51, 31);
            buttonUnderline.TabIndex = 2;
            buttonUnderline.Text = "U";
            buttonUnderline.UseVisualStyleBackColor = true;
            buttonUnderline.Click += buttonUnderline_Click;
            // 
            // comboBoxFontSize
            // 
            comboBoxFontSize.FormattingEnabled = true;
            comboBoxFontSize.Location = new Point(72, 136);
            comboBoxFontSize.Margin = new Padding(4);
            comboBoxFontSize.Name = "comboBoxFontSize";
            comboBoxFontSize.Size = new Size(89, 28);
            comboBoxFontSize.TabIndex = 5;
            comboBoxFontSize.SelectedIndexChanged += comboBoxFontSize_SelectedIndexChanged;
            comboBoxFontSize.TextChanged += comboBoxFontSize_TextChanged;
            // 
            // labelFontSize
            // 
            labelFontSize.AutoSize = true;
            labelFontSize.Location = new Point(8, 140);
            labelFontSize.Margin = new Padding(4, 0, 4, 0);
            labelFontSize.Name = "labelFontSize";
            labelFontSize.Size = new Size(54, 20);
            labelFontSize.TabIndex = 4;
            labelFontSize.Text = "大小：";
            // 
            // comboBoxFont
            // 
            comboBoxFont.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            comboBoxFont.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxFont.FormattingEnabled = true;
            comboBoxFont.Location = new Point(72, 89);
            comboBoxFont.Margin = new Padding(4);
            comboBoxFont.Name = "comboBoxFont";
            comboBoxFont.Size = new Size(273, 28);
            comboBoxFont.TabIndex = 3;
            comboBoxFont.SelectedIndexChanged += comboBoxFont_SelectedIndexChanged;
            // 
            // labelFont
            // 
            labelFont.AutoSize = true;
            labelFont.Location = new Point(8, 93);
            labelFont.Margin = new Padding(4, 0, 4, 0);
            labelFont.Name = "labelFont";
            labelFont.Size = new Size(54, 20);
            labelFont.TabIndex = 2;
            labelFont.Text = "字体：";
            // 
            // textBoxContent
            // 
            textBoxContent.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            textBoxContent.Location = new Point(8, 53);
            textBoxContent.Margin = new Padding(4);
            textBoxContent.Name = "textBoxContent";
            textBoxContent.Size = new Size(337, 27);
            textBoxContent.TabIndex = 1;
            textBoxContent.Text = "输入文本";
            textBoxContent.TextChanged += textBoxContent_TextChanged;
            // 
            // labelContent
            // 
            labelContent.AutoSize = true;
            labelContent.Location = new Point(8, 29);
            labelContent.Margin = new Padding(4, 0, 4, 0);
            labelContent.Name = "labelContent";
            labelContent.Size = new Size(84, 20);
            labelContent.TabIndex = 0;
            labelContent.Text = "文本内容：";
            // 
            // pictureBoxPreview
            // 
            pictureBoxPreview.BackColor = Color.White;
            pictureBoxPreview.Dock = DockStyle.Fill;
            pictureBoxPreview.Location = new Point(0, 0);
            pictureBoxPreview.Margin = new Padding(4);
            pictureBoxPreview.Name = "pictureBoxPreview";
            pictureBoxPreview.Size = new Size(767, 733);
            pictureBoxPreview.TabIndex = 0;
            pictureBoxPreview.TabStop = false;
            pictureBoxPreview.MouseDown += pictureBoxPreview_MouseDown;
            pictureBoxPreview.MouseMove += pictureBoxPreview_MouseMove;
            pictureBoxPreview.MouseUp += pictureBoxPreview_MouseUp;
            // 
            // panel2
            // 
            panel2.Controls.Add(buttonCancel);
            panel2.Controls.Add(buttonOK);
            panel2.Dock = DockStyle.Bottom;
            panel2.Location = new Point(0, 733);
            panel2.Margin = new Padding(4);
            panel2.Name = "panel2";
            panel2.Size = new Size(767, 67);
            panel2.TabIndex = 1;
            // 
            // buttonCancel
            // 
            buttonCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonCancel.Location = new Point(655, 19);
            buttonCancel.Margin = new Padding(4);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new Size(96, 31);
            buttonCancel.TabIndex = 1;
            buttonCancel.Text = "取消";
            buttonCancel.UseVisualStyleBackColor = true;
            buttonCancel.Click += buttonCancel_Click;
            // 
            // buttonOK
            // 
            buttonOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonOK.Location = new Point(551, 19);
            buttonOK.Margin = new Padding(4);
            buttonOK.Name = "buttonOK";
            buttonOK.Size = new Size(96, 31);
            buttonOK.TabIndex = 0;
            buttonOK.Text = "确定";
            buttonOK.UseVisualStyleBackColor = true;
            buttonOK.Click += buttonOK_Click;
            // 
            // TextDialog
            // 
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            ClientSize = new Size(1157, 800);
            Controls.Add(splitContainer);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4);
            MinimizeBox = false;
            Name = "TextDialog";
            StartPosition = FormStartPosition.CenterParent;
            Text = "添加文本";
            splitContainer.Panel1.ResumeLayout(false);
            splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer).EndInit();
            splitContainer.ResumeLayout(false);
            groupBoxEffects.ResumeLayout(false);
            groupBoxEffects.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarRotation).EndInit();
            ((System.ComponentModel.ISupportInitialize)trackBarOpacity).EndInit();
            groupBoxAlignment.ResumeLayout(false);
            groupBoxAlignment.PerformLayout();
            groupBoxTextProperties.ResumeLayout(false);
            groupBoxTextProperties.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarOutlineWidth).EndInit();
            flowLayoutPanelFontStyle.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).EndInit();
            panel2.ResumeLayout(false);
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer;
        private System.Windows.Forms.GroupBox groupBoxTextProperties;
        private System.Windows.Forms.Label labelContent;
        private System.Windows.Forms.TextBox textBoxContent;
        private System.Windows.Forms.Label labelFont;
        private System.Windows.Forms.ComboBox comboBoxFont;
        private System.Windows.Forms.Label labelFontSize;
        private System.Windows.Forms.ComboBox comboBoxFontSize;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanelFontStyle;
        private System.Windows.Forms.Button buttonBold;
        private System.Windows.Forms.Button buttonItalic;
        private System.Windows.Forms.Button buttonUnderline;
        private System.Windows.Forms.Label labelTextColor;
        private System.Windows.Forms.Button buttonTextColor;
        private System.Windows.Forms.CheckBox checkBoxOutline;
        private System.Windows.Forms.Label labelOutlineWidth;
        private System.Windows.Forms.TrackBar trackBarOutlineWidth;
        private System.Windows.Forms.Label labelOutlineWidthValue;
        private System.Windows.Forms.Label labelOutlineColor;
        private System.Windows.Forms.Button buttonOutlineColor;
        private System.Windows.Forms.GroupBox groupBoxAlignment;
        private System.Windows.Forms.ComboBox comboBoxVAlign;
        private System.Windows.Forms.Label labelVAlign;
        private System.Windows.Forms.ComboBox comboBoxHAlign;
        private System.Windows.Forms.Label labelHAlign;
        private System.Windows.Forms.GroupBox groupBoxEffects;
        private System.Windows.Forms.Label labelRotationValue;
        private System.Windows.Forms.TrackBar trackBarRotation;
        private System.Windows.Forms.Label labelRotation;
        private System.Windows.Forms.Label labelOpacityValue;
        private System.Windows.Forms.TrackBar trackBarOpacity;
        private System.Windows.Forms.Label labelOpacity;
        private System.Windows.Forms.Label labelHint;
        private System.Windows.Forms.PictureBox pictureBoxPreview;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Button buttonOK;
    }
}