namespace PictureMagic
{
    partial class DrawingDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(DrawingDialog));
            splitContainer = new SplitContainer();
            pictureBoxPreview = new PictureBox();
            panel1 = new Panel();
            buttonCancel = new Button();
            buttonOK = new Button();
            groupBoxTools = new GroupBox();
            buttonClear = new Button();
            buttonRedo = new Button();
            buttonUndo = new Button();
            label2 = new Label();
            comboBoxSize = new ComboBox();
            buttonColor = new Button();
            label1 = new Label();
            comboBoxTool = new ComboBox();
            labelTool = new Label();
            ((System.ComponentModel.ISupportInitialize)splitContainer).BeginInit();
            splitContainer.Panel1.SuspendLayout();
            splitContainer.Panel2.SuspendLayout();
            splitContainer.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).BeginInit();
            panel1.SuspendLayout();
            groupBoxTools.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer
            // 
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.FixedPanel = FixedPanel.Panel2;
            splitContainer.Location = new Point(0, 0);
            splitContainer.Margin = new Padding(4, 5, 4, 5);
            splitContainer.Name = "splitContainer";
            // 
            // splitContainer.Panel1
            // 
            splitContainer.Panel1.Controls.Add(pictureBoxPreview);
            // 
            // splitContainer.Panel2
            // 
            splitContainer.Panel2.Controls.Add(panel1);
            splitContainer.Panel2.Controls.Add(groupBoxTools);
            splitContainer.Size = new Size(1476, 1102);
            splitContainer.SplitterDistance = 1048;
            splitContainer.SplitterWidth = 6;
            splitContainer.TabIndex = 0;
            // 
            // pictureBoxPreview
            // 
            pictureBoxPreview.BackColor = Color.White;
            pictureBoxPreview.Dock = DockStyle.Fill;
            pictureBoxPreview.Location = new Point(0, 0);
            pictureBoxPreview.Margin = new Padding(4, 5, 4, 5);
            pictureBoxPreview.Name = "pictureBoxPreview";
            pictureBoxPreview.Size = new Size(1048, 1102);
            pictureBoxPreview.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBoxPreview.TabIndex = 0;
            pictureBoxPreview.TabStop = false;
            // 
            // panel1
            // 
            panel1.Controls.Add(buttonCancel);
            panel1.Controls.Add(buttonOK);
            panel1.Dock = DockStyle.Bottom;
            panel1.Location = new Point(0, 1019);
            panel1.Margin = new Padding(4, 5, 4, 5);
            panel1.Name = "panel1";
            panel1.Size = new Size(422, 83);
            panel1.TabIndex = 1;
            // 
            // buttonCancel
            // 
            buttonCancel.Location = new Point(225, 23);
            buttonCancel.Margin = new Padding(4, 5, 4, 5);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new Size(112, 38);
            buttonCancel.TabIndex = 1;
            buttonCancel.Text = "取消";
            buttonCancel.UseVisualStyleBackColor = true;
            buttonCancel.Click += buttonCancel_Click;
            // 
            // buttonOK
            // 
            buttonOK.Location = new Point(82, 23);
            buttonOK.Margin = new Padding(4, 5, 4, 5);
            buttonOK.Name = "buttonOK";
            buttonOK.Size = new Size(112, 38);
            buttonOK.TabIndex = 0;
            buttonOK.Text = "确定";
            buttonOK.UseVisualStyleBackColor = true;
            buttonOK.Click += buttonOK_Click;
            // 
            // groupBoxTools
            // 
            groupBoxTools.Controls.Add(buttonClear);
            groupBoxTools.Controls.Add(buttonRedo);
            groupBoxTools.Controls.Add(buttonUndo);
            groupBoxTools.Controls.Add(label2);
            groupBoxTools.Controls.Add(comboBoxSize);
            groupBoxTools.Controls.Add(buttonColor);
            groupBoxTools.Controls.Add(label1);
            groupBoxTools.Controls.Add(comboBoxTool);
            groupBoxTools.Controls.Add(labelTool);
            groupBoxTools.Dock = DockStyle.Top;
            groupBoxTools.Location = new Point(0, 0);
            groupBoxTools.Margin = new Padding(4, 5, 4, 5);
            groupBoxTools.Name = "groupBoxTools";
            groupBoxTools.Padding = new Padding(4, 5, 4, 5);
            groupBoxTools.Size = new Size(422, 417);
            groupBoxTools.TabIndex = 0;
            groupBoxTools.TabStop = false;
            groupBoxTools.Text = "绘图工具";
            // 
            // buttonClear
            // 
            buttonClear.Location = new Point(280, 333);
            buttonClear.Margin = new Padding(4, 5, 4, 5);
            buttonClear.Name = "buttonClear";
            buttonClear.Size = new Size(112, 38);
            buttonClear.TabIndex = 8;
            buttonClear.Text = "清除";
            buttonClear.UseVisualStyleBackColor = true;
            buttonClear.Click += buttonClear_Click;
            // 
            // buttonRedo
            // 
            buttonRedo.Location = new Point(159, 333);
            buttonRedo.Margin = new Padding(4, 5, 4, 5);
            buttonRedo.Name = "buttonRedo";
            buttonRedo.Size = new Size(112, 38);
            buttonRedo.TabIndex = 7;
            buttonRedo.Text = "重做";
            buttonRedo.UseVisualStyleBackColor = true;
            buttonRedo.Click += buttonRedo_Click;
            // 
            // buttonUndo
            // 
            buttonUndo.Location = new Point(38, 333);
            buttonUndo.Margin = new Padding(4, 5, 4, 5);
            buttonUndo.Name = "buttonUndo";
            buttonUndo.Size = new Size(112, 38);
            buttonUndo.TabIndex = 6;
            buttonUndo.Text = "撤销";
            buttonUndo.UseVisualStyleBackColor = true;
            buttonUndo.Click += buttonUndo_Click;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Location = new Point(33, 250);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new Size(84, 20);
            label2.TabIndex = 5;
            label2.Text = "绘图大小：";
            // 
            // comboBoxSize
            // 
            comboBoxSize.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxSize.FormattingEnabled = true;
            comboBoxSize.Location = new Point(140, 245);
            comboBoxSize.Margin = new Padding(4, 5, 4, 5);
            comboBoxSize.Name = "comboBoxSize";
            comboBoxSize.Size = new Size(180, 28);
            comboBoxSize.TabIndex = 4;
            comboBoxSize.SelectedIndexChanged += comboBoxSize_SelectedIndexChanged;
            // 
            // buttonColor
            // 
            buttonColor.Location = new Point(140, 167);
            buttonColor.Margin = new Padding(4, 5, 4, 5);
            buttonColor.Name = "buttonColor";
            buttonColor.Size = new Size(112, 38);
            buttonColor.TabIndex = 3;
            buttonColor.UseVisualStyleBackColor = true;
            buttonColor.Click += buttonColor_Click;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Location = new Point(33, 175);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(84, 20);
            label1.TabIndex = 2;
            label1.Text = "绘图颜色：";
            // 
            // comboBoxTool
            // 
            comboBoxTool.DropDownStyle = ComboBoxStyle.DropDownList;
            comboBoxTool.FormattingEnabled = true;
            comboBoxTool.Location = new Point(140, 83);
            comboBoxTool.Margin = new Padding(4, 5, 4, 5);
            comboBoxTool.Name = "comboBoxTool";
            comboBoxTool.Size = new Size(180, 28);
            comboBoxTool.TabIndex = 1;
            comboBoxTool.SelectedIndexChanged += comboBoxTool_SelectedIndexChanged;
            // 
            // labelTool
            // 
            labelTool.AutoSize = true;
            labelTool.Location = new Point(33, 88);
            labelTool.Margin = new Padding(4, 0, 4, 0);
            labelTool.Name = "labelTool";
            labelTool.Size = new Size(84, 20);
            labelTool.TabIndex = 0;
            labelTool.Text = "绘图工具：";
            // 
            // DrawingDialog
            // 
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            ClientSize = new Size(1476, 1102);
            Controls.Add(splitContainer);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4, 5, 4, 5);
            MinimizeBox = false;
            Name = "DrawingDialog";
            StartPosition = FormStartPosition.CenterParent;
            Text = "绘图工具";
            FormClosing += DrawingDialog_FormClosing;
            splitContainer.Panel1.ResumeLayout(false);
            splitContainer.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer).EndInit();
            splitContainer.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).EndInit();
            panel1.ResumeLayout(false);
            groupBoxTools.ResumeLayout(false);
            groupBoxTools.PerformLayout();
            ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.SplitContainer splitContainer;
        private System.Windows.Forms.PictureBox pictureBoxPreview;
        private System.Windows.Forms.GroupBox groupBoxTools;
        private System.Windows.Forms.ComboBox comboBoxTool;
        private System.Windows.Forms.Label labelTool;
        private System.Windows.Forms.Button buttonColor;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox comboBoxSize;
        private System.Windows.Forms.Button buttonClear;
        private System.Windows.Forms.Button buttonRedo;
        private System.Windows.Forms.Button buttonUndo;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Button buttonOK;
    }
}