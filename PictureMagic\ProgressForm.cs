using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;
using System.Windows.Forms;
using System.ComponentModel;

namespace PictureMagic
{
    /// <summary>
    /// 遮罩窗体，用于创建半透明遮罩效果
    /// </summary>
    public class MaskForm : Form
    {
        private Form owner;

        /// <summary>
        /// 初始化遮罩窗体
        /// </summary>
        /// <param name="owner">父窗体</param>
        public MaskForm(Form owner)
        {
            this.owner = owner;
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.StartPosition = FormStartPosition.Manual;
            this.BackColor = Color.Black;
            this.Opacity = 0.5; // 半透明效果
            
            // 设置窗体样式为工具窗口，不在Alt+Tab中显示
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.MinimizeBox = false;
            this.MaximizeBox = false;
            this.TopMost = false; // 不要设置为最顶层，否则会覆盖进度窗体
            
            // 设置窗体大小和位置
            UpdateFormBounds();
            
            // 订阅父窗体的大小和位置变化事件
            owner.LocationChanged += Owner_LocationChanged;
            owner.SizeChanged += Owner_SizeChanged;
            owner.VisibleChanged += Owner_VisibleChanged;
        }
        
        /// <summary>
        /// 更新窗体大小和位置
        /// </summary>
        private void UpdateFormBounds()
        {
            // 获取父窗体的位置和大小
            Rectangle bounds = owner.Bounds;
            this.Bounds = bounds;
        }

        /// <summary>
        /// 处理父窗体位置变化事件
        /// </summary>
        private void Owner_LocationChanged(object sender, EventArgs e)
        {
            UpdateFormBounds();
        }

        /// <summary>
        /// 处理父窗体大小变化事件
        /// </summary>
        private void Owner_SizeChanged(object sender, EventArgs e)
        {
            UpdateFormBounds();
        }

        /// <summary>
        /// 处理父窗体可见性变化事件
        /// </summary>
        private void Owner_VisibleChanged(object sender, EventArgs e)
        {
            this.Visible = owner.Visible;
        }

        /// <summary>
        /// 重写WndProc方法，处理窗体消息
        /// </summary>
        protected override void WndProc(ref Message m)
        {
            const int WM_NCHITTEST = 0x0084;
            const int HTCLIENT = 1;
            
            // 捕获鼠标事件，不让它们穿透到父窗体
            // 这样可以防止用户点击被遮罩的控件
            if (m.Msg == WM_NCHITTEST)
            {
                m.Result = (IntPtr)HTCLIENT;
                return;
            }
            
            base.WndProc(ref m);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && owner != null)
            {
                // 取消订阅事件
                owner.LocationChanged -= Owner_LocationChanged;
                owner.SizeChanged -= Owner_SizeChanged;
                owner.VisibleChanged -= Owner_VisibleChanged;
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// 独立的进度窗体，总是显示在最前面
    /// </summary>
    public partial class ProgressForm : Form
    {
        private bool cancelled = false;
        private MaskForm maskForm = null;
        private bool useMask = false;

        /// <summary>
        /// 初始化进度窗体
        /// </summary>
        /// <param name="useMask">是否使用遮罩效果</param>
        public ProgressForm(bool useMask = false)
        {
            InitializeComponent();
            this.useMask = useMask;

            // 添加圆角和阴影效果
            this.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, this.Width, this.Height, 15, 15));
        }


        
        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void cancelButton_Click(object sender, EventArgs e)
        {
            cancelled = true;
            UpdateMessage("正在取消操作...");
            cancelButton.Enabled = false;
        }
        
        /// <summary>
        /// 检查用户是否取消了操作
        /// </summary>
        /// <returns>如果用户取消了操作，则为true；否则为false</returns>
        public bool IsCancelled()
        {
            return cancelled;
        }

        /// <summary>
        /// 设置进度条样式为Marquee（不确定进度）
        /// </summary>
        public void SetMarqueeStyle()
        {
            if (progressBar.InvokeRequired)
            {
                progressBar.Invoke(new Action(SetMarqueeStyle));
                return;
            }

            progressBar.Style = ProgressBarStyle.Marquee;
            progressBar.MarqueeAnimationSpeed = 50;
        }

        /// <summary>
        /// 设置进度条样式为Continuous（确定进度）
        /// </summary>
        /// <param name="value">当前进度值</param>
        /// <param name="maximum">最大进度值</param>
        public void SetContinuousStyle(int value, int maximum)
        {
            if (progressBar.InvokeRequired)
            {
                progressBar.Invoke(new Action<int, int>(SetContinuousStyle), value, maximum);
                return;
            }

            progressBar.Style = ProgressBarStyle.Continuous;
            progressBar.Maximum = maximum;
            progressBar.Value = Math.Min(value, maximum);
        }

        /// <summary>
        /// 更新进度值
        /// </summary>
        /// <param name="value">当前进度值</param>
        public void UpdateProgress(int value)
        {
            if (progressBar.InvokeRequired)
            {
                progressBar.Invoke(new Action<int>(UpdateProgress), value);
                return;
            }

            if (progressBar.Style == ProgressBarStyle.Continuous)
            {
                progressBar.Value = Math.Min(value, progressBar.Maximum);
                Application.DoEvents(); // 强制刷新UI
            }
        }

        /// <summary>
        /// 获取当前进度值
        /// </summary>
        /// <returns>当前进度值</returns>
        public int GetCurrentProgress()
        {
            if (progressBar.InvokeRequired)
            {
                return (int)progressBar.Invoke(new Func<int>(GetCurrentProgress));
            }

            if (progressBar.Style == ProgressBarStyle.Continuous)
            {
                return progressBar.Value;
            }
            return 0; // 如果是Marquee样式，返回0
        }

        /// <summary>
        /// 更新进度消息
        /// </summary>
        /// <param name="message">进度消息</param>
        public void UpdateMessage(string message)
        {
            if (progressLabel.InvokeRequired)
            {
                progressLabel.Invoke(new Action<string>(UpdateMessage), message);
                return;
            }

            progressLabel.Text = message;
            Application.DoEvents(); // 强制刷新UI
        }

        /// <summary>
        /// 显示进度窗体
        /// </summary>
        /// <param name="message">进度消息</param>
        /// <param name="useMask">是否使用遮罩效果</param>
        public static ProgressForm ShowProgress(string message = "正在处理...", bool useMask = false)
        {
            ProgressForm form = new ProgressForm(useMask);
            form.UpdateMessage(message);
            form.Show();
            form.BringToFront();
            Application.DoEvents(); // 强制刷新UI
            return form;
        }

        /// <summary>
        /// 显示进度窗体（模态）
        /// </summary>
        /// <param name="owner">父窗体</param>
        /// <param name="message">进度消息</param>
        /// <param name="useMask">是否使用遮罩效果</param>
        public static ProgressForm ShowProgressDialog(IWin32Window owner, string message = "正在处理...", bool useMask = false)
        {
            ProgressForm form = new ProgressForm(useMask);
            form.UpdateMessage(message);
            
            // 如果启用遮罩效果且owner是Form类型
            if (useMask && owner is Form ownerForm)
            {
                try
                {
                    // 创建遮罩窗体
                    form.maskForm = new MaskForm(ownerForm);
                    
                    // 确保遮罩窗体显示在父窗体之上，但在进度窗体之下
                    form.maskForm.Owner = ownerForm;
                    form.maskForm.Show();
                    
                    // 禁用父窗体上的控件，防止用户操作
                    ownerForm.Enabled = false;
                    
                    // 强制刷新UI，确保遮罩窗体显示
                    Application.DoEvents();
                }
                catch (Exception ex)
                {
                    // 遮罩窗体创建失败，记录错误但继续显示进度窗体
                    System.Diagnostics.Debug.WriteLine("创建遮罩窗体失败: " + ex.Message);
                }
            }
            
            // 显示进度窗体
            form.Show(owner);
            form.TopMost = true; // 确保进度窗体显示在最前面
            form.BringToFront();
            return form;
        }

        /// <summary>
        /// 关闭进度窗体
        /// </summary>
        public void CloseProgress()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(CloseProgress));
                return;
            }

            // 如果有遮罩窗体，先关闭遮罩窗体
            if (maskForm != null && !maskForm.IsDisposed)
            {
                try
                {
                    // 如果遮罩窗体有父窗体，重新启用父窗体的控件
                    if (maskForm.Owner != null && !maskForm.Owner.IsDisposed)
                    {
                        maskForm.Owner.Enabled = true;
                    }
                    
                    // 关闭并释放遮罩窗体
                    maskForm.Close();
                    maskForm.Dispose();
                    maskForm = null;
                }
                catch (Exception ex)
                {
                    // 关闭遮罩窗体失败，记录错误但继续关闭进度窗体
                    System.Diagnostics.Debug.WriteLine("关闭遮罩窗体失败: " + ex.Message);
                }
            }

            this.Close();
        }

        /// <summary>
        /// 重写WndProc方法，处理窗体消息
        /// </summary>
        protected override void WndProc(ref Message m)
        {
            const int WM_NCHITTEST = 0x0084;
            const int HTCAPTION = 2;

            // 允许通过鼠标拖动窗体
            if (m.Msg == WM_NCHITTEST)
            {
                base.WndProc(ref m);
                if (m.Result.ToInt32() == 1) // HTCLIENT
                {
                    m.Result = new IntPtr(HTCAPTION);
                    return;
                }
            }

            base.WndProc(ref m);
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void ProgressForm_Load(object sender, EventArgs e)
        {
            // 设置双缓冲，减少闪烁
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer |
                          ControlStyles.AllPaintingInWmPaint |
                          ControlStyles.UserPaint, true);

            // 自定义进度条样式
            SetProgressBarColor(progressBar, Color.FromArgb(0, 120, 215));
        }
        
        // Dispose方法已移至Designer.cs文件中

        /// <summary>
        /// 窗体绘制事件
        /// </summary>
        private void ProgressForm_Paint(object sender, PaintEventArgs e)
        {
            // 创建渐变背景
            using (LinearGradientBrush brush = new LinearGradientBrush(
                this.ClientRectangle,
                Color.FromArgb(240, 240, 250), // 浅蓝灰色
                Color.FromArgb(220, 220, 240), // 浅紫灰色
                LinearGradientMode.ForwardDiagonal))
            {
                e.Graphics.FillRectangle(brush, this.ClientRectangle);
            }

            // 绘制边框
            using (Pen pen = new Pen(Color.FromArgb(200, 200, 220), 2))
            {
                DrawRoundedRectangle(e.Graphics, pen, 1, 1, this.Width - 3, this.Height - 3, 20);
            }
        }

        /// <summary>
        /// 绘制圆角矩形
        /// </summary>
        private void DrawRoundedRectangle(Graphics graphics, Pen pen, int x, int y, int width, int height, int radius)
        {
            GraphicsPath path = new GraphicsPath();

            // 左上角弧线
            path.AddArc(x, y, radius, radius, 180, 90);
            // 顶边线
            path.AddLine(x + radius, y, x + width - radius, y);
            // 右上角弧线
            path.AddArc(x + width - radius, y, radius, radius, 270, 90);
            // 右边线
            path.AddLine(x + width, y + radius, x + width, y + height - radius);
            // 右下角弧线
            path.AddArc(x + width - radius, y + height - radius, radius, radius, 0, 90);
            // 底边线
            path.AddLine(x + width - radius, y + height, x + radius, y + height);
            // 左下角弧线
            path.AddArc(x, y + height - radius, radius, radius, 90, 90);
            // 左边线
            path.AddLine(x, y + height - radius, x, y + radius);

            path.CloseFigure();
            graphics.DrawPath(pen, path);
        }

        /// <summary>
        /// 设置进度条颜色
        /// </summary>
        private void SetProgressBarColor(ProgressBar progressBar, Color color)
        {
            // 使用P/Invoke调用Windows API来设置进度条颜色
            SendMessage(progressBar.Handle, 0x409, 2, color.ToArgb());
        }

        /// <summary>
        /// 创建圆角矩形区域的API函数
        /// </summary>
        [DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(int nLeftRect, int nTopRect,
            int nRightRect, int nBottomRect, int nWidthEllipse, int nHeightEllipse);

        /// <summary>
        /// 发送Windows消息的API函数
        /// </summary>
        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        private static extern IntPtr SendMessage(IntPtr hWnd, int msg, int wParam, int lParam);
    }
}