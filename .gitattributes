# 设置默认行为，如果core.autocrlf未设置
* text=auto

# 显式声明文本文件，并设置它们的规范化方式
*.cs text diff=csharp
*.resx text
*.csproj text
*.sln text
*.md text
*.gitignore text
*.gitattributes text

# 声明二进制文件，防止修改
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.mov binary
*.mp4 binary
*.mp3 binary
*.flv binary
*.fla binary
*.swf binary
*.gz binary
*.zip binary
*.7z binary
*.ttf binary
*.eot binary
*.woff binary
*.woff2 binary
*.pyc binary
*.pdf binary

# 确保shell脚本使用LF行尾，即使在Windows上也是如此
*.sh text eol=lf

# 确保批处理脚本使用CRLF行尾，即使在Unix上也是如此
*.bat text eol=crlf
*.cmd text eol=crlf

# 排除特定文件不进行差异比较
*.min.js -diff
*.min.css -diff