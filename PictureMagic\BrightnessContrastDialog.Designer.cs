namespace PictureMagic
{
    partial class BrightnessContrastDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            pictureBoxPreview = new PictureBox();
            labelBrightness = new Label();
            labelContrast = new Label();
            trackBarBrightness = new TrackBar();
            trackBarContrast = new TrackBar();
            buttonOK = new Button();
            buttonCancel = new Button();
            buttonReset = new Button();
            labelBrightnessValue = new Label();
            labelContrastValue = new Label();
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).BeginInit();
            ((System.ComponentModel.ISupportInitialize)trackBarBrightness).BeginInit();
            ((System.ComponentModel.ISupportInitialize)trackBarContrast).BeginInit();
            SuspendLayout();
            // 
            // pictureBoxPreview
            // 
            pictureBoxPreview.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            pictureBoxPreview.BackColor = Color.DimGray;
            pictureBoxPreview.Location = new Point(14, 12);
            pictureBoxPreview.Name = "pictureBoxPreview";
            pictureBoxPreview.Size = new Size(518, 300);
            pictureBoxPreview.TabIndex = 0;
            pictureBoxPreview.TabStop = false;
            // 
            // labelBrightness
            // 
            labelBrightness.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            labelBrightness.AutoSize = true;
            labelBrightness.Location = new Point(14, 325);
            labelBrightness.Name = "labelBrightness";
            labelBrightness.Size = new Size(43, 20);
            labelBrightness.TabIndex = 1;
            labelBrightness.Text = "亮度:";
            // 
            // labelContrast
            // 
            labelContrast.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            labelContrast.AutoSize = true;
            labelContrast.Location = new Point(14, 365);
            labelContrast.Name = "labelContrast";
            labelContrast.Size = new Size(58, 20);
            labelContrast.TabIndex = 2;
            labelContrast.Text = "对比度:";
            // 
            // trackBarBrightness
            // 
            trackBarBrightness.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            trackBarBrightness.Location = new Point(87, 325);
            trackBarBrightness.Maximum = 100;
            trackBarBrightness.Minimum = -100;
            trackBarBrightness.Name = "trackBarBrightness";
            trackBarBrightness.Size = new Size(394, 56);
            trackBarBrightness.TabIndex = 3;
            trackBarBrightness.TickFrequency = 10;
            trackBarBrightness.ValueChanged += trackBarBrightness_Scroll;
            // 
            // trackBarContrast
            // 
            trackBarContrast.Anchor = AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            trackBarContrast.Location = new Point(87, 365);
            trackBarContrast.Maximum = 100;
            trackBarContrast.Minimum = -100;
            trackBarContrast.Name = "trackBarContrast";
            trackBarContrast.Size = new Size(394, 56);
            trackBarContrast.TabIndex = 4;
            trackBarContrast.TickFrequency = 10;
            trackBarContrast.ValueChanged += trackBarContrast_Scroll;
            // 
            // buttonOK
            // 
            buttonOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonOK.Location = new Point(334, 415);
            buttonOK.Name = "buttonOK";
            buttonOK.Size = new Size(106, 29);
            buttonOK.TabIndex = 5;
            buttonOK.Text = "确定";
            buttonOK.UseVisualStyleBackColor = true;
            buttonOK.Click += buttonOK_Click;
            // 
            // buttonCancel
            // 
            buttonCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            buttonCancel.Location = new Point(447, 415);
            buttonCancel.Name = "buttonCancel";
            buttonCancel.Size = new Size(106, 29);
            buttonCancel.TabIndex = 6;
            buttonCancel.Text = "取消";
            buttonCancel.UseVisualStyleBackColor = true;
            buttonCancel.Click += buttonCancel_Click;
            // 
            // buttonReset
            // 
            buttonReset.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            buttonReset.Location = new Point(14, 415);
            buttonReset.Name = "buttonReset";
            buttonReset.Size = new Size(106, 29);
            buttonReset.TabIndex = 7;
            buttonReset.Text = "重置";
            buttonReset.UseVisualStyleBackColor = true;
            buttonReset.Click += buttonReset_Click;
            // 
            // labelBrightnessValue
            // 
            labelBrightnessValue.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            labelBrightnessValue.AutoSize = true;
            labelBrightnessValue.Location = new Point(487, 325);
            labelBrightnessValue.Name = "labelBrightnessValue";
            labelBrightnessValue.Size = new Size(18, 20);
            labelBrightnessValue.TabIndex = 8;
            labelBrightnessValue.Text = "0";
            // 
            // labelContrastValue
            // 
            labelContrastValue.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            labelContrastValue.AutoSize = true;
            labelContrastValue.Location = new Point(487, 365);
            labelContrastValue.Name = "labelContrastValue";
            labelContrastValue.Size = new Size(18, 20);
            labelContrastValue.TabIndex = 9;
            labelContrastValue.Text = "0";
            // 
            // BrightnessContrastDialog
            // 
            AcceptButton = buttonOK;
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            CancelButton = buttonCancel;
            ClientSize = new Size(566, 456);
            Controls.Add(labelContrastValue);
            Controls.Add(labelBrightnessValue);
            Controls.Add(buttonReset);
            Controls.Add(buttonCancel);
            Controls.Add(buttonOK);
            Controls.Add(trackBarContrast);
            Controls.Add(trackBarBrightness);
            Controls.Add(labelContrast);
            Controls.Add(labelBrightness);
            Controls.Add(pictureBoxPreview);
            MinimizeBox = false;
            MinimumSize = new Size(448, 400);
            Name = "BrightnessContrastDialog";
            ShowIcon = false;
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "亮度/对比度调整";
            ((System.ComponentModel.ISupportInitialize)pictureBoxPreview).EndInit();
            ((System.ComponentModel.ISupportInitialize)trackBarBrightness).EndInit();
            ((System.ComponentModel.ISupportInitialize)trackBarContrast).EndInit();
            ResumeLayout(false);
            PerformLayout();

        }

        #endregion

        private System.Windows.Forms.PictureBox pictureBoxPreview;
        private System.Windows.Forms.Label labelBrightness;
        private System.Windows.Forms.Label labelContrast;
        private System.Windows.Forms.TrackBar trackBarBrightness;
        private System.Windows.Forms.TrackBar trackBarContrast;
        private System.Windows.Forms.Button buttonOK;
        private System.Windows.Forms.Button buttonCancel;
        private System.Windows.Forms.Button buttonReset;
        private System.Windows.Forms.Label labelBrightnessValue;
        private System.Windows.Forms.Label labelContrastValue;
    }
}