namespace PictureMagic
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        private ToolStripMenuItem toolsToolStripMenuItem;
        private ToolStripMenuItem undoToolStripMenuItem;
        private ToolStripMenuItem redoToolStripMenuItem;
        private ToolStripButton toolStripButtonUndo;
        private ToolStripButton toolStripButtonRedo;
        private ToolStripSeparator toolStripSeparator8;
        private ToolStripSeparator toolStripSeparator9;
        private ToolStripMenuItem imageMosaicToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator10;
        private ToolStripButton toolStripButtonAbout;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            menuStripMain = new MenuStrip();
            fileToolStripMenuItem = new ToolStripMenuItem();
            openToolStripMenuItem = new ToolStripMenuItem();
            saveToolStripMenuItem = new ToolStripMenuItem();
            saveAsToolStripMenuItem = new ToolStripMenuItem();
            closeToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator1 = new ToolStripSeparator();
            exitToolStripMenuItem = new ToolStripMenuItem();
            editToolStripMenuItem = new ToolStripMenuItem();
            undoToolStripMenuItem = new ToolStripMenuItem();
            redoToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator9 = new ToolStripSeparator();
            rotateLeftToolStripMenuItem = new ToolStripMenuItem();
            rotateRightToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator2 = new ToolStripSeparator();
            flipHorizontalToolStripMenuItem = new ToolStripMenuItem();
            flipVerticalToolStripMenuItem = new ToolStripMenuItem();
            toolsToolStripMenuItem = new ToolStripMenuItem();
            cropToolStripMenuItem = new ToolStripMenuItem();
            resizeToolStripMenuItem = new ToolStripMenuItem();
            textToolStripMenuItem = new ToolStripMenuItem();
            drawToolStripMenuItem = new ToolStripMenuItem();
            imageMosaicToolStripMenuItem = new ToolStripMenuItem();
            effectsToolStripMenuItem = new ToolStripMenuItem();
            filtersToolStripMenuItem = new ToolStripMenuItem();
            toolStripSeparator3 = new ToolStripSeparator();
            grayscaleToolStripMenuItem = new ToolStripMenuItem();
            invertToolStripMenuItem = new ToolStripMenuItem();
            brightnessContrastToolStripMenuItem = new ToolStripMenuItem();
            helpToolStripMenuItem = new ToolStripMenuItem();
            aboutMenuItem = new ToolStripMenuItem();
            statusStripMain = new StatusStrip();
            toolStripStatusFileName = new ToolStripStatusLabel();
            toolStripStatusDimensions = new ToolStripStatusLabel();
            toolStripStatusFileSize = new ToolStripStatusLabel();
            lblCopyRight = new ToolStripStatusLabel();
            lblEmail = new ToolStripStatusLabel();
            toolStripMain = new ToolStrip();
            toolStripButtonOpen = new ToolStripButton();
            toolStripButtonSave = new ToolStripButton();
            toolStripButtonClose = new ToolStripButton();
            toolStripSeparator5 = new ToolStripSeparator();
            toolStripButtonUndo = new ToolStripButton();
            toolStripButtonRedo = new ToolStripButton();
            toolStripSeparator8 = new ToolStripSeparator();
            toolStripButtonRotateLeft = new ToolStripButton();
            toolStripButtonRotateRight = new ToolStripButton();
            toolStripButtonFlipH = new ToolStripButton();
            toolStripButtonFlipV = new ToolStripButton();
            toolStripSeparator6 = new ToolStripSeparator();
            toolStripButtonGrayscale = new ToolStripButton();
            toolStripButtonInvert = new ToolStripButton();
            toolStripSeparator7 = new ToolStripSeparator();
            toolStripButtonCrop = new ToolStripButton();
            toolStripButtonResize = new ToolStripButton();
            toolStripButtonImageMosaic = new ToolStripButton();
            toolStripSeparator10 = new ToolStripSeparator();
            toolStripButtonAbout = new ToolStripButton();
            splitContainerMain = new SplitContainer();
            tableLayoutPanelLeft = new TableLayoutPanel();
            panelTools = new Panel();
            listBoxTools = new ListBox();
            labelTools = new Label();
            panelFilters = new Panel();
            listBoxFilters = new ListBox();
            labelFilters = new Label();
            panelEffects = new Panel();
            listBoxEffects = new ListBox();
            labelEffects = new Label();
            pictureBoxMain = new PictureBox();
            menuStripMain.SuspendLayout();
            statusStripMain.SuspendLayout();
            toolStripMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)splitContainerMain).BeginInit();
            splitContainerMain.Panel1.SuspendLayout();
            splitContainerMain.Panel2.SuspendLayout();
            splitContainerMain.SuspendLayout();
            tableLayoutPanelLeft.SuspendLayout();
            panelTools.SuspendLayout();
            panelFilters.SuspendLayout();
            panelEffects.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)pictureBoxMain).BeginInit();
            SuspendLayout();
            // 
            // menuStripMain
            // 
            menuStripMain.ImageScalingSize = new Size(20, 20);
            menuStripMain.Items.AddRange(new ToolStripItem[] { fileToolStripMenuItem, editToolStripMenuItem, toolsToolStripMenuItem, effectsToolStripMenuItem, helpToolStripMenuItem });
            menuStripMain.Location = new Point(0, 0);
            menuStripMain.Name = "menuStripMain";
            menuStripMain.Padding = new Padding(7, 2, 0, 2);
            menuStripMain.Size = new Size(1330, 28);
            menuStripMain.TabIndex = 0;
            menuStripMain.Text = "menuStrip1";
            // 
            // fileToolStripMenuItem
            // 
            fileToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { openToolStripMenuItem, saveToolStripMenuItem, saveAsToolStripMenuItem, closeToolStripMenuItem, toolStripSeparator1, exitToolStripMenuItem });
            fileToolStripMenuItem.Name = "fileToolStripMenuItem";
            fileToolStripMenuItem.Size = new Size(53, 24);
            fileToolStripMenuItem.Text = "文件";
            // 
            // openToolStripMenuItem
            // 
            openToolStripMenuItem.Name = "openToolStripMenuItem";
            openToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.O;
            openToolStripMenuItem.Size = new Size(236, 26);
            openToolStripMenuItem.Text = "打开";
            openToolStripMenuItem.Click += openToolStripMenuItem_Click;
            // 
            // saveToolStripMenuItem
            // 
            saveToolStripMenuItem.Name = "saveToolStripMenuItem";
            saveToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.S;
            saveToolStripMenuItem.Size = new Size(236, 26);
            saveToolStripMenuItem.Text = "保存";
            saveToolStripMenuItem.Click += saveToolStripMenuItem_Click;
            // 
            // saveAsToolStripMenuItem
            // 
            saveAsToolStripMenuItem.Name = "saveAsToolStripMenuItem";
            saveAsToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.Shift | Keys.S;
            saveAsToolStripMenuItem.Size = new Size(236, 26);
            saveAsToolStripMenuItem.Text = "另存为";
            saveAsToolStripMenuItem.Click += saveAsToolStripMenuItem_Click;
            // 
            // closeToolStripMenuItem
            // 
            closeToolStripMenuItem.Name = "closeToolStripMenuItem";
            closeToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.W;
            closeToolStripMenuItem.Size = new Size(236, 26);
            closeToolStripMenuItem.Text = "关闭";
            closeToolStripMenuItem.Click += closeToolStripMenuItem_Click;
            // 
            // toolStripSeparator1
            // 
            toolStripSeparator1.Name = "toolStripSeparator1";
            toolStripSeparator1.Size = new Size(233, 6);
            // 
            // exitToolStripMenuItem
            // 
            exitToolStripMenuItem.Name = "exitToolStripMenuItem";
            exitToolStripMenuItem.ShortcutKeys = Keys.Alt | Keys.F4;
            exitToolStripMenuItem.Size = new Size(236, 26);
            exitToolStripMenuItem.Text = "退出";
            exitToolStripMenuItem.Click += exitToolStripMenuItem_Click;
            // 
            // editToolStripMenuItem
            // 
            editToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { undoToolStripMenuItem, redoToolStripMenuItem, toolStripSeparator9, rotateLeftToolStripMenuItem, rotateRightToolStripMenuItem, toolStripSeparator2, flipHorizontalToolStripMenuItem, flipVerticalToolStripMenuItem });
            editToolStripMenuItem.Name = "editToolStripMenuItem";
            editToolStripMenuItem.Size = new Size(53, 24);
            editToolStripMenuItem.Text = "编辑";
            // 
            // undoToolStripMenuItem
            // 
            undoToolStripMenuItem.Name = "undoToolStripMenuItem";
            undoToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.Z;
            undoToolStripMenuItem.Size = new Size(177, 26);
            undoToolStripMenuItem.Text = "撤销";
            undoToolStripMenuItem.Click += undoToolStripMenuItem_Click;
            // 
            // redoToolStripMenuItem
            // 
            redoToolStripMenuItem.Name = "redoToolStripMenuItem";
            redoToolStripMenuItem.ShortcutKeys = Keys.Control | Keys.Y;
            redoToolStripMenuItem.Size = new Size(177, 26);
            redoToolStripMenuItem.Text = "重做";
            redoToolStripMenuItem.Click += redoToolStripMenuItem_Click;
            // 
            // toolStripSeparator9
            // 
            toolStripSeparator9.Name = "toolStripSeparator9";
            toolStripSeparator9.Size = new Size(174, 6);
            // 
            // rotateLeftToolStripMenuItem
            // 
            rotateLeftToolStripMenuItem.Name = "rotateLeftToolStripMenuItem";
            rotateLeftToolStripMenuItem.Size = new Size(177, 26);
            rotateLeftToolStripMenuItem.Text = "向左旋转";
            rotateLeftToolStripMenuItem.Click += rotateLeftToolStripMenuItem_Click;
            // 
            // rotateRightToolStripMenuItem
            // 
            rotateRightToolStripMenuItem.Name = "rotateRightToolStripMenuItem";
            rotateRightToolStripMenuItem.Size = new Size(177, 26);
            rotateRightToolStripMenuItem.Text = "向右旋转";
            rotateRightToolStripMenuItem.Click += rotateRightToolStripMenuItem_Click;
            // 
            // toolStripSeparator2
            // 
            toolStripSeparator2.Name = "toolStripSeparator2";
            toolStripSeparator2.Size = new Size(174, 6);
            // 
            // flipHorizontalToolStripMenuItem
            // 
            flipHorizontalToolStripMenuItem.Name = "flipHorizontalToolStripMenuItem";
            flipHorizontalToolStripMenuItem.Size = new Size(177, 26);
            flipHorizontalToolStripMenuItem.Text = "水平翻转";
            flipHorizontalToolStripMenuItem.Click += flipHorizontalToolStripMenuItem_Click;
            // 
            // flipVerticalToolStripMenuItem
            // 
            flipVerticalToolStripMenuItem.Name = "flipVerticalToolStripMenuItem";
            flipVerticalToolStripMenuItem.Size = new Size(177, 26);
            flipVerticalToolStripMenuItem.Text = "垂直翻转";
            flipVerticalToolStripMenuItem.Click += flipVerticalToolStripMenuItem_Click;
            // 
            // toolsToolStripMenuItem
            // 
            toolsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { cropToolStripMenuItem, resizeToolStripMenuItem, textToolStripMenuItem, drawToolStripMenuItem, imageMosaicToolStripMenuItem });
            toolsToolStripMenuItem.Name = "toolsToolStripMenuItem";
            toolsToolStripMenuItem.Size = new Size(53, 24);
            toolsToolStripMenuItem.Text = "工具";
            // 
            // cropToolStripMenuItem
            // 
            cropToolStripMenuItem.Name = "cropToolStripMenuItem";
            cropToolStripMenuItem.Size = new Size(152, 26);
            cropToolStripMenuItem.Text = "裁剪";
            cropToolStripMenuItem.Click += cropToolStripMenuItem_Click;
            // 
            // resizeToolStripMenuItem
            // 
            resizeToolStripMenuItem.Name = "resizeToolStripMenuItem";
            resizeToolStripMenuItem.Size = new Size(152, 26);
            resizeToolStripMenuItem.Text = "调整大小";
            resizeToolStripMenuItem.Click += resizeToolStripMenuItem_Click;
            // 
            // textToolStripMenuItem
            // 
            textToolStripMenuItem.Name = "textToolStripMenuItem";
            textToolStripMenuItem.Size = new Size(152, 26);
            textToolStripMenuItem.Text = "文本工具";
            textToolStripMenuItem.Click += textToolStripMenuItem_Click;
            // 
            // drawToolStripMenuItem
            // 
            drawToolStripMenuItem.Name = "drawToolStripMenuItem";
            drawToolStripMenuItem.Size = new Size(152, 26);
            drawToolStripMenuItem.Text = "绘图工具";
            drawToolStripMenuItem.Click += drawToolStripMenuItem_Click;
            // 
            // imageMosaicToolStripMenuItem
            // 
            imageMosaicToolStripMenuItem.Name = "imageMosaicToolStripMenuItem";
            imageMosaicToolStripMenuItem.Size = new Size(152, 26);
            imageMosaicToolStripMenuItem.Text = "图片拼接";
            imageMosaicToolStripMenuItem.Click += imageMosaicToolStripMenuItem_Click;
            // 
            // effectsToolStripMenuItem
            // 
            effectsToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { filtersToolStripMenuItem, toolStripSeparator3, grayscaleToolStripMenuItem, invertToolStripMenuItem, brightnessContrastToolStripMenuItem });
            effectsToolStripMenuItem.Name = "effectsToolStripMenuItem";
            effectsToolStripMenuItem.Size = new Size(53, 24);
            effectsToolStripMenuItem.Text = "特效";
            // 
            // filtersToolStripMenuItem
            // 
            filtersToolStripMenuItem.Name = "filtersToolStripMenuItem";
            filtersToolStripMenuItem.Size = new Size(173, 26);
            filtersToolStripMenuItem.Text = "滤镜库";
            filtersToolStripMenuItem.Click += filtersToolStripMenuItem_Click;
            // 
            // toolStripSeparator3
            // 
            toolStripSeparator3.Name = "toolStripSeparator3";
            toolStripSeparator3.Size = new Size(170, 6);
            // 
            // grayscaleToolStripMenuItem
            // 
            grayscaleToolStripMenuItem.Name = "grayscaleToolStripMenuItem";
            grayscaleToolStripMenuItem.Size = new Size(173, 26);
            grayscaleToolStripMenuItem.Text = "灰度";
            grayscaleToolStripMenuItem.Click += grayscaleToolStripMenuItem_Click;
            // 
            // invertToolStripMenuItem
            // 
            invertToolStripMenuItem.Name = "invertToolStripMenuItem";
            invertToolStripMenuItem.Size = new Size(173, 26);
            invertToolStripMenuItem.Text = "反相";
            invertToolStripMenuItem.Click += invertToolStripMenuItem_Click;
            // 
            // brightnessContrastToolStripMenuItem
            // 
            brightnessContrastToolStripMenuItem.Name = "brightnessContrastToolStripMenuItem";
            brightnessContrastToolStripMenuItem.Size = new Size(173, 26);
            brightnessContrastToolStripMenuItem.Text = "亮度/对比度";
            brightnessContrastToolStripMenuItem.Click += brightnessContrastToolStripMenuItem_Click;
            // 
            // helpToolStripMenuItem
            // 
            helpToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] { aboutMenuItem });
            helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            helpToolStripMenuItem.Size = new Size(53, 24);
            helpToolStripMenuItem.Text = "帮助";
            // 
            // aboutMenuItem
            // 
            aboutMenuItem.Name = "aboutMenuItem";
            aboutMenuItem.Size = new Size(122, 26);
            aboutMenuItem.Text = "关于";
            aboutMenuItem.Click += aboutMenuItem_Click;
            // 
            // statusStripMain
            // 
            statusStripMain.ImageScalingSize = new Size(20, 20);
            statusStripMain.Items.AddRange(new ToolStripItem[] { toolStripStatusFileName, toolStripStatusDimensions, toolStripStatusFileSize, lblCopyRight, lblEmail });
            statusStripMain.Location = new Point(0, 729);
            statusStripMain.Name = "statusStripMain";
            statusStripMain.Padding = new Padding(1, 0, 16, 0);
            statusStripMain.Size = new Size(1330, 26);
            statusStripMain.TabIndex = 1;
            statusStripMain.Text = "statusStrip1";
            // 
            // toolStripStatusFileName
            // 
            toolStripStatusFileName.Name = "toolStripStatusFileName";
            toolStripStatusFileName.Size = new Size(62, 20);
            toolStripStatusFileName.Text = "文件: 无";
            // 
            // toolStripStatusDimensions
            // 
            toolStripStatusDimensions.Name = "toolStripStatusDimensions";
            toolStripStatusDimensions.Size = new Size(53, 20);
            toolStripStatusDimensions.Text = "尺寸: -";
            // 
            // toolStripStatusFileSize
            // 
            toolStripStatusFileSize.Name = "toolStripStatusFileSize";
            toolStripStatusFileSize.Size = new Size(53, 20);
            toolStripStatusFileSize.Text = "大小: -";
            // 
            // lblCopyRight
            // 
            lblCopyRight.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Bold);
            lblCopyRight.Name = "lblCopyRight";
            lblCopyRight.Size = new Size(950, 20);
            lblCopyRight.Spring = true;
            // 
            // lblEmail
            // 
            lblEmail.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Underline, GraphicsUnit.Point, 134);
            lblEmail.IsLink = true;
            lblEmail.Name = "lblEmail";
            lblEmail.Size = new Size(195, 20);
            lblEmail.Text = "<EMAIL>";
            lblEmail.Click += lblEmail_Click;
            // 
            // toolStripMain
            // 
            toolStripMain.ImageScalingSize = new Size(20, 20);
            toolStripMain.Items.AddRange(new ToolStripItem[] { toolStripButtonOpen, toolStripButtonSave, toolStripButtonClose, toolStripSeparator5, toolStripButtonUndo, toolStripButtonRedo, toolStripSeparator8, toolStripButtonRotateLeft, toolStripButtonRotateRight, toolStripButtonFlipH, toolStripButtonFlipV, toolStripSeparator6, toolStripButtonGrayscale, toolStripButtonInvert, toolStripSeparator7, toolStripButtonCrop, toolStripButtonResize, toolStripButtonImageMosaic, toolStripSeparator10, toolStripButtonAbout });
            toolStripMain.Location = new Point(0, 28);
            toolStripMain.Name = "toolStripMain";
            toolStripMain.Size = new Size(1330, 25);
            toolStripMain.TabIndex = 2;
            toolStripMain.Text = "toolStrip1";
            // 
            // toolStripButtonOpen
            // 
            toolStripButtonOpen.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonOpen.ImageTransparentColor = Color.Magenta;
            toolStripButtonOpen.Name = "toolStripButtonOpen";
            toolStripButtonOpen.Size = new Size(29, 22);
            toolStripButtonOpen.Text = "打开";
            toolStripButtonOpen.Click += openToolStripMenuItem_Click;
            // 
            // toolStripButtonSave
            // 
            toolStripButtonSave.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonSave.ImageTransparentColor = Color.Magenta;
            toolStripButtonSave.Name = "toolStripButtonSave";
            toolStripButtonSave.Size = new Size(29, 22);
            toolStripButtonSave.Text = "保存";
            toolStripButtonSave.Click += saveToolStripMenuItem_Click;
            // 
            // toolStripButtonClose
            // 
            toolStripButtonClose.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonClose.ImageTransparentColor = Color.Magenta;
            toolStripButtonClose.Name = "toolStripButtonClose";
            toolStripButtonClose.Size = new Size(29, 22);
            toolStripButtonClose.Text = "关闭";
            toolStripButtonClose.Click += closeToolStripMenuItem_Click;
            // 
            // toolStripSeparator5
            // 
            toolStripSeparator5.Name = "toolStripSeparator5";
            toolStripSeparator5.Size = new Size(6, 25);
            // 
            // toolStripButtonUndo
            // 
            toolStripButtonUndo.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonUndo.ImageTransparentColor = Color.Magenta;
            toolStripButtonUndo.Name = "toolStripButtonUndo";
            toolStripButtonUndo.Size = new Size(29, 22);
            toolStripButtonUndo.Text = "撤销";
            toolStripButtonUndo.Click += undoToolStripMenuItem_Click;
            // 
            // toolStripButtonRedo
            // 
            toolStripButtonRedo.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonRedo.ImageTransparentColor = Color.Magenta;
            toolStripButtonRedo.Name = "toolStripButtonRedo";
            toolStripButtonRedo.Size = new Size(29, 22);
            toolStripButtonRedo.Text = "重做";
            toolStripButtonRedo.Click += redoToolStripMenuItem_Click;
            // 
            // toolStripSeparator8
            // 
            toolStripSeparator8.Name = "toolStripSeparator8";
            toolStripSeparator8.Size = new Size(6, 25);
            // 
            // toolStripButtonRotateLeft
            // 
            toolStripButtonRotateLeft.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonRotateLeft.ImageTransparentColor = Color.Magenta;
            toolStripButtonRotateLeft.Name = "toolStripButtonRotateLeft";
            toolStripButtonRotateLeft.Size = new Size(29, 22);
            toolStripButtonRotateLeft.Text = "向左旋转";
            toolStripButtonRotateLeft.Click += rotateLeftToolStripMenuItem_Click;
            // 
            // toolStripButtonRotateRight
            // 
            toolStripButtonRotateRight.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonRotateRight.ImageTransparentColor = Color.Magenta;
            toolStripButtonRotateRight.Name = "toolStripButtonRotateRight";
            toolStripButtonRotateRight.Size = new Size(29, 22);
            toolStripButtonRotateRight.Text = "向右旋转";
            toolStripButtonRotateRight.Click += rotateRightToolStripMenuItem_Click;
            // 
            // toolStripButtonFlipH
            // 
            toolStripButtonFlipH.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonFlipH.ImageTransparentColor = Color.Magenta;
            toolStripButtonFlipH.Name = "toolStripButtonFlipH";
            toolStripButtonFlipH.Size = new Size(29, 22);
            toolStripButtonFlipH.Text = "水平翻转";
            toolStripButtonFlipH.Click += flipHorizontalToolStripMenuItem_Click;
            // 
            // toolStripButtonFlipV
            // 
            toolStripButtonFlipV.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonFlipV.ImageTransparentColor = Color.Magenta;
            toolStripButtonFlipV.Name = "toolStripButtonFlipV";
            toolStripButtonFlipV.Size = new Size(29, 22);
            toolStripButtonFlipV.Text = "垂直翻转";
            toolStripButtonFlipV.Click += flipVerticalToolStripMenuItem_Click;
            // 
            // toolStripSeparator6
            // 
            toolStripSeparator6.Name = "toolStripSeparator6";
            toolStripSeparator6.Size = new Size(6, 25);
            // 
            // toolStripButtonGrayscale
            // 
            toolStripButtonGrayscale.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonGrayscale.ImageTransparentColor = Color.Magenta;
            toolStripButtonGrayscale.Name = "toolStripButtonGrayscale";
            toolStripButtonGrayscale.Size = new Size(29, 22);
            toolStripButtonGrayscale.Text = "灰度";
            toolStripButtonGrayscale.Click += grayscaleToolStripMenuItem_Click;
            // 
            // toolStripButtonInvert
            // 
            toolStripButtonInvert.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonInvert.ImageTransparentColor = Color.Magenta;
            toolStripButtonInvert.Name = "toolStripButtonInvert";
            toolStripButtonInvert.Size = new Size(29, 22);
            toolStripButtonInvert.Text = "反相";
            toolStripButtonInvert.Click += invertToolStripMenuItem_Click;
            // 
            // toolStripSeparator7
            // 
            toolStripSeparator7.Name = "toolStripSeparator7";
            toolStripSeparator7.Size = new Size(6, 25);
            // 
            // toolStripButtonCrop
            // 
            toolStripButtonCrop.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonCrop.ImageTransparentColor = Color.Magenta;
            toolStripButtonCrop.Name = "toolStripButtonCrop";
            toolStripButtonCrop.Size = new Size(29, 22);
            toolStripButtonCrop.Text = "裁剪";
            toolStripButtonCrop.Click += cropToolStripMenuItem_Click;
            // 
            // toolStripButtonResize
            // 
            toolStripButtonResize.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonResize.ImageTransparentColor = Color.Magenta;
            toolStripButtonResize.Name = "toolStripButtonResize";
            toolStripButtonResize.Size = new Size(29, 22);
            toolStripButtonResize.Text = "调整大小";
            toolStripButtonResize.Click += resizeToolStripMenuItem_Click;
            // 
            // toolStripButtonImageMosaic
            // 
            toolStripButtonImageMosaic.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonImageMosaic.ImageTransparentColor = Color.Magenta;
            toolStripButtonImageMosaic.Name = "toolStripButtonImageMosaic";
            toolStripButtonImageMosaic.Size = new Size(29, 22);
            toolStripButtonImageMosaic.Text = "图片拼接";
            toolStripButtonImageMosaic.Click += imageMosaicToolStripMenuItem_Click;
            // 
            // toolStripSeparator10
            // 
            toolStripSeparator10.Name = "toolStripSeparator10";
            toolStripSeparator10.Size = new Size(6, 25);
            // 
            // toolStripButtonAbout
            // 
            toolStripButtonAbout.DisplayStyle = ToolStripItemDisplayStyle.Image;
            toolStripButtonAbout.ImageTransparentColor = Color.Magenta;
            toolStripButtonAbout.Name = "toolStripButtonAbout";
            toolStripButtonAbout.Size = new Size(29, 22);
            toolStripButtonAbout.Text = "关于";
            toolStripButtonAbout.ToolTipText = "关于PictureMagic";
            toolStripButtonAbout.Click += aboutMenuItem_Click;
            // 
            // splitContainerMain
            // 
            splitContainerMain.Dock = DockStyle.Fill;
            splitContainerMain.FixedPanel = FixedPanel.Panel1;
            splitContainerMain.Location = new Point(0, 53);
            splitContainerMain.Name = "splitContainerMain";
            // 
            // splitContainerMain.Panel1
            // 
            splitContainerMain.Panel1.Controls.Add(tableLayoutPanelLeft);
            splitContainerMain.Panel1MinSize = 200;
            // 
            // splitContainerMain.Panel2
            // 
            splitContainerMain.Panel2.Controls.Add(pictureBoxMain);
            splitContainerMain.Size = new Size(1330, 676);
            splitContainerMain.SplitterDistance = 225;
            splitContainerMain.TabIndex = 3;
            // 
            // tableLayoutPanelLeft
            // 
            tableLayoutPanelLeft.ColumnCount = 1;
            tableLayoutPanelLeft.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanelLeft.Controls.Add(panelTools, 0, 0);
            tableLayoutPanelLeft.Controls.Add(panelFilters, 0, 1);
            tableLayoutPanelLeft.Controls.Add(panelEffects, 0, 2);
            tableLayoutPanelLeft.Dock = DockStyle.Fill;
            tableLayoutPanelLeft.Location = new Point(0, 0);
            tableLayoutPanelLeft.Name = "tableLayoutPanelLeft";
            tableLayoutPanelLeft.RowCount = 3;
            tableLayoutPanelLeft.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            tableLayoutPanelLeft.RowStyles.Add(new RowStyle(SizeType.Percent, 33.33F));
            tableLayoutPanelLeft.RowStyles.Add(new RowStyle(SizeType.Percent, 33.34F));
            tableLayoutPanelLeft.Size = new Size(225, 676);
            tableLayoutPanelLeft.TabIndex = 0;
            // 
            // panelTools
            // 
            panelTools.BackColor = Color.WhiteSmoke;
            panelTools.Controls.Add(listBoxTools);
            panelTools.Controls.Add(labelTools);
            panelTools.Dock = DockStyle.Fill;
            panelTools.Location = new Point(3, 3);
            panelTools.Name = "panelTools";
            panelTools.Size = new Size(219, 219);
            panelTools.TabIndex = 0;
            // 
            // listBoxTools
            // 
            listBoxTools.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            listBoxTools.BackColor = Color.White;
            listBoxTools.BorderStyle = BorderStyle.FixedSingle;
            listBoxTools.Font = new Font("Segoe UI", 9F);
            listBoxTools.ForeColor = Color.Black;
            listBoxTools.IntegralHeight = false;
            listBoxTools.Items.AddRange(new object[] { "裁剪工具", "绘图工具", "文本工具", "调整大小", "图片拼接" });
            listBoxTools.Location = new Point(6, 35);
            listBoxTools.Name = "listBoxTools";
            listBoxTools.Size = new Size(208, 179);
            listBoxTools.TabIndex = 1;
            listBoxTools.SelectedIndexChanged += ToolsListBox_SelectedIndexChanged;
            // 
            // labelTools
            // 
            labelTools.BackColor = Color.LightGray;
            labelTools.Dock = DockStyle.Top;
            labelTools.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            labelTools.Location = new Point(0, 0);
            labelTools.Name = "labelTools";
            labelTools.Size = new Size(219, 30);
            labelTools.TabIndex = 0;
            labelTools.Text = "工具";
            labelTools.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panelFilters
            // 
            panelFilters.BackColor = Color.WhiteSmoke;
            panelFilters.Controls.Add(listBoxFilters);
            panelFilters.Controls.Add(labelFilters);
            panelFilters.Dock = DockStyle.Fill;
            panelFilters.Location = new Point(3, 228);
            panelFilters.Name = "panelFilters";
            panelFilters.Size = new Size(219, 219);
            panelFilters.TabIndex = 1;
            // 
            // listBoxFilters
            // 
            listBoxFilters.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            listBoxFilters.BackColor = Color.White;
            listBoxFilters.BorderStyle = BorderStyle.FixedSingle;
            listBoxFilters.Font = new Font("Segoe UI", 9F);
            listBoxFilters.ForeColor = Color.Black;
            listBoxFilters.IntegralHeight = false;
            listBoxFilters.Location = new Point(6, 35);
            listBoxFilters.Name = "listBoxFilters";
            listBoxFilters.Size = new Size(208, 179);
            listBoxFilters.TabIndex = 1;
            listBoxFilters.SelectedIndexChanged += FiltersListBox_SelectedIndexChanged;
            // 
            // labelFilters
            // 
            labelFilters.BackColor = Color.LightGray;
            labelFilters.Dock = DockStyle.Top;
            labelFilters.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            labelFilters.Location = new Point(0, 0);
            labelFilters.Name = "labelFilters";
            labelFilters.Size = new Size(219, 30);
            labelFilters.TabIndex = 0;
            labelFilters.Text = "滤镜";
            labelFilters.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panelEffects
            // 
            panelEffects.BackColor = Color.WhiteSmoke;
            panelEffects.Controls.Add(listBoxEffects);
            panelEffects.Controls.Add(labelEffects);
            panelEffects.Dock = DockStyle.Fill;
            panelEffects.Location = new Point(3, 453);
            panelEffects.Name = "panelEffects";
            panelEffects.Size = new Size(219, 220);
            panelEffects.TabIndex = 2;
            // 
            // listBoxEffects
            // 
            listBoxEffects.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            listBoxEffects.BackColor = Color.White;
            listBoxEffects.BorderStyle = BorderStyle.FixedSingle;
            listBoxEffects.Font = new Font("Segoe UI", 9F);
            listBoxEffects.ForeColor = Color.Black;
            listBoxEffects.IntegralHeight = false;
            listBoxEffects.Items.AddRange(new object[] { "亮度/对比度", "向左旋转", "向右旋转", "水平翻转", "垂直翻转" });
            listBoxEffects.Location = new Point(6, 35);
            listBoxEffects.Name = "listBoxEffects";
            listBoxEffects.Size = new Size(208, 180);
            listBoxEffects.TabIndex = 1;
            listBoxEffects.SelectedIndexChanged += EffectsListBox_SelectedIndexChanged;
            // 
            // labelEffects
            // 
            labelEffects.BackColor = Color.LightGray;
            labelEffects.Dock = DockStyle.Top;
            labelEffects.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            labelEffects.Location = new Point(0, 0);
            labelEffects.Name = "labelEffects";
            labelEffects.Size = new Size(219, 30);
            labelEffects.TabIndex = 0;
            labelEffects.Text = "特效";
            labelEffects.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // pictureBoxMain
            // 
            pictureBoxMain.BackColor = Color.DimGray;
            pictureBoxMain.Dock = DockStyle.Fill;
            pictureBoxMain.Location = new Point(0, 0);
            pictureBoxMain.Name = "pictureBoxMain";
            pictureBoxMain.Size = new Size(1101, 676);
            pictureBoxMain.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBoxMain.TabIndex = 0;
            pictureBoxMain.TabStop = false;
            // 
            // MainForm
            // 
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            ClientSize = new Size(1330, 755);
            Controls.Add(splitContainerMain);
            Controls.Add(toolStripMain);
            Controls.Add(statusStripMain);
            Controls.Add(menuStripMain);
            Icon = (Icon)resources.GetObject("$this.Icon");
            MainMenuStrip = menuStripMain;
            Name = "MainForm";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "PictureMagic - 图像编辑器";
            WindowState = FormWindowState.Maximized;
            Activated += MainForm_Activated;
            FormClosing += MainForm_FormClosing;
            Load += MainForm_Load;
            menuStripMain.ResumeLayout(false);
            menuStripMain.PerformLayout();
            statusStripMain.ResumeLayout(false);
            statusStripMain.PerformLayout();
            toolStripMain.ResumeLayout(false);
            toolStripMain.PerformLayout();
            splitContainerMain.Panel1.ResumeLayout(false);
            splitContainerMain.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainerMain).EndInit();
            splitContainerMain.ResumeLayout(false);
            tableLayoutPanelLeft.ResumeLayout(false);
            panelTools.ResumeLayout(false);
            panelFilters.ResumeLayout(false);
            panelEffects.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)pictureBoxMain).EndInit();
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private MenuStrip menuStripMain;
        private ToolStripMenuItem fileToolStripMenuItem;
        private ToolStripMenuItem openToolStripMenuItem;
        private ToolStripMenuItem saveToolStripMenuItem;
        private ToolStripMenuItem saveAsToolStripMenuItem;
        private ToolStripMenuItem closeToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator1;
        private ToolStripMenuItem exitToolStripMenuItem;
        private ToolStripMenuItem editToolStripMenuItem;
        private ToolStripMenuItem rotateLeftToolStripMenuItem;
        private ToolStripMenuItem rotateRightToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator2;
        private ToolStripMenuItem flipHorizontalToolStripMenuItem;
        private ToolStripMenuItem flipVerticalToolStripMenuItem;
        private ToolStripSeparator toolStripSeparator3;
        private ToolStripMenuItem grayscaleToolStripMenuItem;
        private ToolStripMenuItem invertToolStripMenuItem;
        private ToolStripMenuItem brightnessContrastToolStripMenuItem;
        // toolStripSeparator4已被移除
        private ToolStripMenuItem resizeToolStripMenuItem;
        private ToolStripMenuItem cropToolStripMenuItem;
        private ToolStripMenuItem effectsToolStripMenuItem;
        private ToolStripMenuItem filtersToolStripMenuItem;
        private ToolStripMenuItem textToolStripMenuItem;
        private ToolStripMenuItem drawToolStripMenuItem;
        private ToolStripMenuItem helpToolStripMenuItem;
        private StatusStrip statusStripMain;
        private ToolStripStatusLabel toolStripStatusFileName;
        private ToolStripStatusLabel toolStripStatusDimensions;
        private ToolStripStatusLabel toolStripStatusFileSize;
        private ToolStrip toolStripMain;
        private ToolStripButton toolStripButtonOpen;
        private ToolStripButton toolStripButtonSave;
        private ToolStripButton toolStripButtonClose;
        private ToolStripSeparator toolStripSeparator5;
        private ToolStripButton toolStripButtonRotateLeft;
        private ToolStripButton toolStripButtonRotateRight;
        private ToolStripButton toolStripButtonFlipH;
        private ToolStripButton toolStripButtonFlipV;
        private ToolStripSeparator toolStripSeparator6;
        private ToolStripButton toolStripButtonGrayscale;
        private ToolStripButton toolStripButtonInvert;
        private ToolStripSeparator toolStripSeparator7;
        private ToolStripButton toolStripButtonCrop;
        private ToolStripButton toolStripButtonResize;
        private ToolStripButton toolStripButtonImageMosaic;
        private SplitContainer splitContainerMain;
        private TableLayoutPanel tableLayoutPanelLeft;
        private Panel panelTools;
        private Label labelTools;
        private ListBox listBoxTools;
        private Panel panelFilters;
        private Label labelFilters;
        private ListBox listBoxFilters;
        private Panel panelEffects;
        private Label labelEffects;
        private ListBox listBoxEffects;
        private PictureBox pictureBoxMain;
        private ToolStripMenuItem aboutMenuItem;
        private ToolStripStatusLabel lblCopyRight;
        private ToolStripStatusLabel lblEmail;
    }
}