namespace CommonLib
{
    public  class LicenseGenerator
    {
        // 简单的注册码生成算法（示例）
        public static string GenerateLicenseKey(string machineCode, string secretKey)
        {
            // 将机器码和密钥组合
            string combined = machineCode + secretKey;

            // 使用HMACSHA256生成注册码
            using (var hmac = new System.Security.Cryptography.HMACSHA256(System.Text.Encoding.UTF8.GetBytes(secretKey)))
            {
                var hashBytes = hmac.ComputeHash(System.Text.Encoding.UTF8.GetBytes(machineCode));

                // 将哈希值转换为Base64字符串
                string base64Hash = Convert.ToBase64String(hashBytes);

                return base64Hash;
            }
        }

        // 验证注册码
        public static bool ValidateLicenseKey(string machineCode, string licenseKey, string secretKey)
        {
            // 生成正确的注册码
            string correctLicenseKey = GenerateLicenseKey(machineCode, secretKey);

            // 比较用户输入的注册码和正确的注册码
            return string.Equals(licenseKey, correctLicenseKey, StringComparison.OrdinalIgnoreCase);
        }
    }
}
