﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>WinExe</OutputType>
		<TargetFramework>net9.0-windows</TargetFramework>
		<Nullable>enable</Nullable>
		<UseWindowsForms>true</UseWindowsForms>
		<ApplicationHighDpiMode>SystemAware</ApplicationHighDpiMode>
		<ApplicationIcon>Resources\Icons\app_icon.ico</ApplicationIcon>
		<ImplicitUsings>enable</ImplicitUsings>
		<AllowUnsafeBlocks>true</AllowUnsafeBlocks>
	</PropertyGroup>

	<ItemGroup>
		<None Include="CustomInputDialog.cs" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="System.Management" Version="9.0.6" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\CommonLib\CommonLib.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="AboutDialog.cs">
			<SubType>Form</SubType>
		</Compile>
		<Compile Update="AboutDialog.Designer.cs">
			<DependentUpon>AboutDialog.cs</DependentUpon>
		</Compile>
		<Compile Update="Properties\Resources.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Resources.resx</DependentUpon>
		</Compile>
		<EmbeddedResource Update="AboutDialog.resx">
			<DependentUpon>AboutDialog.cs</DependentUpon>
		</EmbeddedResource>
		<EmbeddedResource Update="Properties\Resources.resx">
			<Generator>ResXFileCodeGenerator</Generator>
			<LastGenOutput>Resources.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

</Project>