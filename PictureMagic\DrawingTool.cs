using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;

namespace PictureMagic
{
    public class DrawingTool : IDisposable
    {
        // 绘图工具类型
        public enum ToolType
        {
            Pencil,     // 铅笔
            Brush,      // 画笔
            Line,       // 直线
            Rectangle,  // 矩形
            Ellipse,    // 椭圆
            Eraser      // 橡皮擦
        }

        // 当前工具类型
        private ToolType currentTool = ToolType.Pencil;

        // 绘图属性
        private Color drawColor = Color.Black;
        private int drawSize = 3;
        private bool isDrawing = false;
        private Point lastPoint;
        private Point startPoint;

        // 绘图图层
        private Bitmap drawLayer;
        private Bitmap tempLayer;
        private Bitmap originalImage;
        private PictureBox pictureBox;

        // 绘图历史
        private Stack<Bitmap> undoStack = new Stack<Bitmap>();
        private Stack<Bitmap> redoStack = new Stack<Bitmap>();
        private const int MAX_HISTORY = 20;

        // 事件
        public event EventHandler<DrawingCompletedEventArgs> DrawingCompleted = null!;
        // 添加历史状态变更事件
        public event EventHandler HistoryStateChanged = null!;

        // 构造函数
        public DrawingTool(PictureBox pictureBox, Image image)
        {
            this.pictureBox = pictureBox;
            this.originalImage = new Bitmap(image);

            // 创建绘图图层
            drawLayer = new Bitmap(originalImage.Width, originalImage.Height);
            tempLayer = new Bitmap(originalImage.Width, originalImage.Height);

            // 清空绘图图层
            using (Graphics g = Graphics.FromImage(drawLayer))
            {
                g.Clear(Color.Transparent);
            }

            // 保存初始状态到撤销栈
            SaveToUndoStack();

            // 设置PictureBox事件
            pictureBox.MouseDown += PictureBox_MouseDown;
            pictureBox.MouseMove += PictureBox_MouseMove;
            pictureBox.MouseUp += PictureBox_MouseUp;
            pictureBox.Paint += PictureBox_Paint;
        }

        // 设置工具类型
        public void SetToolType(ToolType toolType)
        {
            currentTool = toolType;
        }

        // 设置绘图颜色
        public void SetColor(Color color)
        {
            drawColor = color;
        }

        // 设置绘图大小
        public void SetSize(int size)
        {
            drawSize = size;
        }

        // 撤销操作
        public bool Undo()
        {
            if (undoStack.Count > 1) // 保留初始状态
            {
                // 保存当前状态到重做栈
                redoStack.Push(new Bitmap(drawLayer));
                if (redoStack.Count > MAX_HISTORY)
                {
                    var oldState = redoStack.ToArray()[redoStack.Count - 1];
                    redoStack.Clear();
                    redoStack.Push(oldState);
                }

                // 恢复上一个状态
                drawLayer.Dispose();
                drawLayer = undoStack.Pop();
                pictureBox.Invalidate();
                
                // 触发历史状态变更事件
                HistoryStateChanged?.Invoke(this, EventArgs.Empty);
                return true;
            }
            return false;
        }

        // 重做操作
        public bool Redo()
        {
            if (redoStack.Count > 0)
            {
                // 保存当前状态到撤销栈
                undoStack.Push(new Bitmap(drawLayer));

                // 恢复下一个状态
                drawLayer.Dispose();
                drawLayer = redoStack.Pop();
                pictureBox.Invalidate();
                
                // 触发历史状态变更事件
                HistoryStateChanged?.Invoke(this, EventArgs.Empty);
                return true;
            }
            return false;
        }
        
        // 检查是否可以撤销
        public bool CanUndo()
        {
            return undoStack.Count > 1; // 保留初始状态，所以需要>1
        }
        
        // 检查是否可以重做
        public bool CanRedo()
        {
            return redoStack.Count > 0;
        }

        // 清除所有绘图
        public void Clear()
        {
            // 保存当前状态到撤销栈
            SaveToUndoStack();

            // 清空绘图图层
            using (Graphics g = Graphics.FromImage(drawLayer))
            {
                g.Clear(Color.Transparent);
            }

            // 清空重做栈
            ClearRedoStack();

            pictureBox.Invalidate();
            
            // 触发历史状态变更事件
            HistoryStateChanged?.Invoke(this, EventArgs.Empty);
        }

        // 完成绘图并返回结果
        public void FinishDrawing()
        {
            // 合并原始图像和绘图图层
            Bitmap result = new Bitmap(originalImage.Width, originalImage.Height);
            using (Graphics g = Graphics.FromImage(result))
            {
                g.DrawImage(originalImage, 0, 0, originalImage.Width, originalImage.Height);
                g.DrawImage(drawLayer, 0, 0, drawLayer.Width, drawLayer.Height);
            }

            // 触发绘图完成事件
            DrawingCompleted?.Invoke(this, new DrawingCompletedEventArgs(result));
        }

        // 保存当前状态到撤销栈
        private void SaveToUndoStack()
        {
            undoStack.Push(new Bitmap(drawLayer));
            if (undoStack.Count > MAX_HISTORY)
            {
                var oldState = undoStack.ToArray()[undoStack.Count - 1];
                undoStack.Clear();
                undoStack.Push(oldState);
            }
            ClearRedoStack();
            
            // 触发历史状态变更事件
            HistoryStateChanged?.Invoke(this, EventArgs.Empty);
        }

        // 清空重做栈
        private void ClearRedoStack()
        {
            while (redoStack.Count > 0)
            {
                var bitmap = redoStack.Pop();
                bitmap.Dispose();
            }
        }

        // 鼠标按下事件
        private void PictureBox_MouseDown(object? sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                isDrawing = true;
                lastPoint = ConvertCoordinates(e.Location);
                startPoint = lastPoint;

                // 对于需要临时图层的工具，复制当前绘图图层到临时图层
                if (currentTool == ToolType.Line || currentTool == ToolType.Rectangle || currentTool == ToolType.Ellipse)
                {
                    using (Graphics g = Graphics.FromImage(tempLayer))
                    {
                        g.Clear(Color.Transparent);
                        g.DrawImage(drawLayer, 0, 0);
                    }
                }
                else if (currentTool == ToolType.Pencil || currentTool == ToolType.Brush || currentTool == ToolType.Eraser)
                {
                    // 对于即时绘制的工具，直接在绘图图层上绘制点
                    using (Graphics g = Graphics.FromImage(drawLayer))
                    {
                        if (currentTool == ToolType.Eraser)
                        {
                            // 橡皮擦使用透明色
                            using (SolidBrush brush = new SolidBrush(Color.Transparent))
                            using (GraphicsPath path = new GraphicsPath())
                            {
                                path.AddEllipse(lastPoint.X - drawSize / 2, lastPoint.Y - drawSize / 2, drawSize, drawSize);
                                g.SetClip(path);
                                g.Clear(Color.Transparent);
                                g.ResetClip();
                            }
                        }
                        else
                        {
                            // 铅笔和画笔使用选定颜色
                            using (SolidBrush brush = new SolidBrush(drawColor))
                            {
                                g.FillEllipse(brush, lastPoint.X - drawSize / 2, lastPoint.Y - drawSize / 2, drawSize, drawSize);
                            }
                        }
                    }
                }

                pictureBox.Invalidate();
            }
        }

        // 鼠标移动事件
        private void PictureBox_MouseMove(object? sender, MouseEventArgs e)
        {
            if (isDrawing && e.Button == MouseButtons.Left)
            {
                Point currentPoint = ConvertCoordinates(e.Location);

                if (currentTool == ToolType.Line || currentTool == ToolType.Rectangle || currentTool == ToolType.Ellipse)
                {
                    // 对于需要临时图层的工具，在临时图层上绘制预览
                    using (Graphics g = Graphics.FromImage(tempLayer))
                    {
                        g.Clear(Color.Transparent);
                        g.DrawImage(drawLayer, 0, 0);

                        g.SmoothingMode = SmoothingMode.AntiAlias;
                        using (Pen pen = new Pen(drawColor, drawSize))
                        {
                            pen.StartCap = LineCap.Round;
                            pen.EndCap = LineCap.Round;

                            if (currentTool == ToolType.Line)
                            {
                                g.DrawLine(pen, startPoint, currentPoint);
                            }
                            else if (currentTool == ToolType.Rectangle)
                            {
                                Rectangle rect = GetRectangle(startPoint, currentPoint);
                                g.DrawRectangle(pen, rect);
                            }
                            else if (currentTool == ToolType.Ellipse)
                            {
                                Rectangle rect = GetRectangle(startPoint, currentPoint);
                                g.DrawEllipse(pen, rect);
                            }
                        }
                    }
                }
                else if (currentTool == ToolType.Pencil || currentTool == ToolType.Brush || currentTool == ToolType.Eraser)
                {
                    // 对于即时绘制的工具，直接在绘图图层上绘制线条
                    using (Graphics g = Graphics.FromImage(drawLayer))
                    {
                        g.SmoothingMode = SmoothingMode.AntiAlias;

                        if (currentTool == ToolType.Eraser)
                        {
                            // 橡皮擦使用透明色
                            using (GraphicsPath path = new GraphicsPath())
                            {
                                path.AddLine(lastPoint, currentPoint);
                                using (Pen pen = new Pen(Color.Black, drawSize))
                                {
                                    pen.StartCap = LineCap.Round;
                                    pen.EndCap = LineCap.Round;
                                    g.SetClip(path.GetBounds());
                                    g.SetClip(path.GetBounds());
                                    using (SolidBrush brush = new SolidBrush(Color.Transparent))
                                    {
                                        g.CompositingMode = CompositingMode.SourceCopy;
                                        g.DrawPath(pen, path);
                                        g.FillPath(brush, path);
                                        g.CompositingMode = CompositingMode.SourceOver;
                                    }
                                    g.ResetClip();
                                }
                            }
                        }
                        else
                        {
                            // 铅笔和画笔使用选定颜色
                            using (Pen pen = new Pen(drawColor, drawSize))
                            {
                                pen.StartCap = LineCap.Round;
                                pen.EndCap = LineCap.Round;

                                if (currentTool == ToolType.Pencil)
                                {
                                    g.DrawLine(pen, lastPoint, currentPoint);
                                }
                                else if (currentTool == ToolType.Brush)
                                {
                                    // 画笔使用更柔和的效果
                                    pen.LineJoin = LineJoin.Round;
                                    g.DrawLine(pen, lastPoint, currentPoint);
                                }
                            }
                        }
                    }
                }

                lastPoint = currentPoint;
                pictureBox.Invalidate();
            }
        }

        // 鼠标释放事件
        private void PictureBox_MouseUp(object? sender, MouseEventArgs e)
        {
            if (isDrawing && e.Button == MouseButtons.Left)
            {
                Point currentPoint = ConvertCoordinates(e.Location);

                if (currentTool == ToolType.Line || currentTool == ToolType.Rectangle || currentTool == ToolType.Ellipse)
                {
                    // 对于需要临时图层的工具，在绘图图层上绘制最终结果
                    using (Graphics g = Graphics.FromImage(drawLayer))
                    {
                        g.SmoothingMode = SmoothingMode.AntiAlias;
                        using (Pen pen = new Pen(drawColor, drawSize))
                        {
                            pen.StartCap = LineCap.Round;
                            pen.EndCap = LineCap.Round;

                            if (currentTool == ToolType.Line)
                            {
                                g.DrawLine(pen, startPoint, currentPoint);
                            }
                            else if (currentTool == ToolType.Rectangle)
                            {
                                Rectangle rect = GetRectangle(startPoint, currentPoint);
                                g.DrawRectangle(pen, rect);
                            }
                            else if (currentTool == ToolType.Ellipse)
                            {
                                Rectangle rect = GetRectangle(startPoint, currentPoint);
                                g.DrawEllipse(pen, rect);
                            }
                        }
                    }
                }

                // 保存当前状态到撤销栈
                SaveToUndoStack();

                isDrawing = false;
                pictureBox.Invalidate();
            }
        }

        // 绘制事件
        private void PictureBox_Paint(object? sender, PaintEventArgs e)
        {
            // 计算图像在PictureBox中的位置和大小
            Rectangle destRect = GetImageRectangle();

            // 绘制原始图像
            e.Graphics.DrawImage(originalImage, destRect);

            // 绘制绘图图层或临时图层
            if (currentTool == ToolType.Line || currentTool == ToolType.Rectangle || currentTool == ToolType.Ellipse)
            {
                if (isDrawing)
                {
                    e.Graphics.DrawImage(tempLayer, destRect);
                }
                else
                {
                    e.Graphics.DrawImage(drawLayer, destRect);
                }
            }
            else
            {
                e.Graphics.DrawImage(drawLayer, destRect);
            }
        }

        // 获取矩形（确保宽度和高度为正值）
        private Rectangle GetRectangle(Point p1, Point p2)
        {
            int x = Math.Min(p1.X, p2.X);
            int y = Math.Min(p1.Y, p2.Y);
            int width = Math.Abs(p1.X - p2.X);
            int height = Math.Abs(p1.Y - p2.Y);
            return new Rectangle(x, y, width, height);
        }

        // 转换鼠标坐标到图像坐标
        private Point ConvertCoordinates(Point mousePoint)
        {
            Rectangle imageRect = GetImageRectangle();
            float scaleX = (float)originalImage.Width / imageRect.Width;
            float scaleY = (float)originalImage.Height / imageRect.Height;

            int imageX = (int)((mousePoint.X - imageRect.X) * scaleX);
            int imageY = (int)((mousePoint.Y - imageRect.Y) * scaleY);

            // 确保坐标在图像范围内
            imageX = Math.Max(0, Math.Min(imageX, originalImage.Width - 1));
            imageY = Math.Max(0, Math.Min(imageY, originalImage.Height - 1));

            return new Point(imageX, imageY);
        }

        // 获取图像在PictureBox中的位置和大小
        private Rectangle GetImageRectangle()
        {
            Rectangle result = new Rectangle(0, 0, pictureBox.Width, pictureBox.Height);
            if (originalImage != null)
            {
                float imageRatio = (float)originalImage.Width / originalImage.Height;
                float containerRatio = (float)pictureBox.Width / pictureBox.Height;

                if (imageRatio > containerRatio)
                {
                    // 图像比容器宽，上下有空白
                    int height = (int)(pictureBox.Width / imageRatio);
                    int y = (pictureBox.Height - height) / 2;
                    result = new Rectangle(0, y, pictureBox.Width, height);
                }
                else
                {
                    // 图像比容器高，左右有空白
                    int width = (int)(pictureBox.Height * imageRatio);
                    int x = (pictureBox.Width - width) / 2;
                    result = new Rectangle(x, 0, width, pictureBox.Height);
                }
            }
            return result;
        }

        // 释放资源
        public void Dispose()
        {
            // 移除事件处理程序
            pictureBox.MouseDown -= PictureBox_MouseDown;
            pictureBox.MouseMove -= PictureBox_MouseMove;
            pictureBox.MouseUp -= PictureBox_MouseUp;
            pictureBox.Paint -= PictureBox_Paint;

            // 释放图像资源
            drawLayer?.Dispose();
            tempLayer?.Dispose();
            originalImage?.Dispose();

            // 清空历史栈
            while (undoStack.Count > 0)
            {
                undoStack.Pop().Dispose();
            }

            while (redoStack.Count > 0)
            {
                redoStack.Pop().Dispose();
            }
        }
    }

    // 绘图完成事件参数
    public class DrawingCompletedEventArgs : EventArgs
    {
        public Image ResultImage { get; private set; }

        public DrawingCompletedEventArgs(Image resultImage)
        {
            ResultImage = resultImage;
        }
    }
}