namespace LicenseGenerator;

partial class LicenseGeneratorForm
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LicenseGeneratorForm));
        lblMachineCode = new Label();
        txtMachineCode = new TextBox();
        btnGenerate = new Button();
        lblLicenseKey = new Label();
        txtLicenseKey = new TextBox();
        btnCopy = new Button();
        lblTitle = new Label();
        lblDescription = new Label();
        btnGetCurrentMachineCode = new Button();
        SuspendLayout();
        // 
        // lblMachineCode
        // 
        lblMachineCode.AutoSize = true;
        lblMachineCode.Location = new Point(30, 100);
        lblMachineCode.Name = "lblMachineCode";
        lblMachineCode.Size = new Size(54, 20);
        lblMachineCode.TabIndex = 0;
        lblMachineCode.Text = "序列号";
        // 
        // txtMachineCode
        // 
        txtMachineCode.BorderStyle = BorderStyle.FixedSingle;
        txtMachineCode.Location = new Point(30, 123);
        txtMachineCode.Multiline = true;
        txtMachineCode.Name = "txtMachineCode";
        txtMachineCode.Size = new Size(540, 68);
        txtMachineCode.TabIndex = 1;
        // 
        // btnGenerate
        // 
        btnGenerate.Location = new Point(220, 206);
        btnGenerate.Name = "btnGenerate";
        btnGenerate.Size = new Size(150, 40);
        btnGenerate.TabIndex = 2;
        btnGenerate.Text = "生成注册码";
        btnGenerate.UseVisualStyleBackColor = true;
        btnGenerate.Click += btnGenerate_Click;
        // 
        // lblLicenseKey
        // 
        lblLicenseKey.AutoSize = true;
        lblLicenseKey.Location = new Point(30, 269);
        lblLicenseKey.Name = "lblLicenseKey";
        lblLicenseKey.Size = new Size(54, 20);
        lblLicenseKey.TabIndex = 3;
        lblLicenseKey.Text = "注册码";
        // 
        // txtLicenseKey
        // 
        txtLicenseKey.BorderStyle = BorderStyle.FixedSingle;
        txtLicenseKey.Location = new Point(30, 292);
        txtLicenseKey.Multiline = true;
        txtLicenseKey.Name = "txtLicenseKey";
        txtLicenseKey.ReadOnly = true;
        txtLicenseKey.Size = new Size(540, 80);
        txtLicenseKey.TabIndex = 4;
        // 
        // btnCopy
        // 
        btnCopy.Location = new Point(220, 378);
        btnCopy.Name = "btnCopy";
        btnCopy.Size = new Size(150, 40);
        btnCopy.TabIndex = 5;
        btnCopy.Text = "复制注册码";
        btnCopy.UseVisualStyleBackColor = true;
        btnCopy.Click += btnCopy_Click;
        // 
        // lblTitle
        // 
        lblTitle.AutoSize = true;
        lblTitle.Font = new Font("Microsoft YaHei UI", 16F, FontStyle.Bold);
        lblTitle.Location = new Point(30, 20);
        lblTitle.Name = "lblTitle";
        lblTitle.Size = new Size(311, 36);
        lblTitle.TabIndex = 6;
        lblTitle.Text = "PictureMagic 注册工具";
        // 
        // lblDescription
        // 
        lblDescription.AutoSize = true;
        lblDescription.Location = new Point(30, 65);
        lblDescription.Name = "lblDescription";
        lblDescription.Size = new Size(383, 20);
        lblDescription.TabIndex = 7;
        lblDescription.Text = "请输入用户的序列号，点击\"生成注册码\"按钮生成注册码";
        // 
        // btnGetCurrentMachineCode
        // 
        btnGetCurrentMachineCode.Location = new Point(408, 224);
        btnGetCurrentMachineCode.Name = "btnGetCurrentMachineCode";
        btnGetCurrentMachineCode.Size = new Size(180, 40);
        btnGetCurrentMachineCode.TabIndex = 8;
        btnGetCurrentMachineCode.Text = "获取当前机器码";
        btnGetCurrentMachineCode.UseVisualStyleBackColor = true;
        btnGetCurrentMachineCode.Visible = false;
        btnGetCurrentMachineCode.Click += btnGetCurrentMachineCode_Click;
        // 
        // LicenseGeneratorForm
        // 
        AutoScaleDimensions = new SizeF(9F, 20F);
        AutoScaleMode = AutoScaleMode.Font;
        ClientSize = new Size(600, 450);
        Controls.Add(lblDescription);
        Controls.Add(lblTitle);
        Controls.Add(btnCopy);
        Controls.Add(txtLicenseKey);
        Controls.Add(lblLicenseKey);
        Controls.Add(btnGetCurrentMachineCode);
        Controls.Add(btnGenerate);
        Controls.Add(txtMachineCode);
        Controls.Add(lblMachineCode);
        FormBorderStyle = FormBorderStyle.FixedDialog;
        Icon = (Icon)resources.GetObject("$this.Icon");
        MaximizeBox = false;
        Name = "LicenseGeneratorForm";
        StartPosition = FormStartPosition.CenterScreen;
        Text = "PictureMagic 注册工具";
        ResumeLayout(false);
        PerformLayout();
    }

    #endregion

    private Label lblMachineCode;
    private TextBox txtMachineCode;
    private Button btnGenerate;
    private Label lblLicenseKey;
    private TextBox txtLicenseKey;
    private Button btnCopy;
    private Label lblTitle;
    private Label lblDescription;
    private Button btnGetCurrentMachineCode;
}
