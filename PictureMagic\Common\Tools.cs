﻿using System.Diagnostics;

namespace PictureMagic.Common
{
    public class Tools
    {
        public static bool IsRegistered = false;

        public static string GetVersion()
        {
            string fileLastWriteTimeString = File.GetLastWriteTime(Process.GetCurrentProcess().MainModule.FileName).ToString("yyyyMMdd.HHmmss");
            System.Version version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version;

            string versionNumber = $"{version.Major}.{version.Minor}.{version.Build}.{version.Revision} ({fileLastWriteTimeString})";
            return versionNumber;
        }

        public static bool IsItemExist(ListView listView, string text)
        {
            foreach (ListViewItem item in listView.Items)
            {
                if (item.Text == text)
                {
                    return true; // 找到相同的元素
                }
            }
            return false; // 没有找到
        }

        public static Image LoadImageSafe(string filePath)
        {
            try
            {
                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    Image temp = Image.FromStream(fs);
                    return new Bitmap(temp);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载图片失败: {ex.Message}");
                return null;
            }
        }
    }
}
