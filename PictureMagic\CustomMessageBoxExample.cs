using System;
using System.Windows.Forms;

namespace PictureMagic
{
    /// <summary>
    /// 自定义消息框使用示例
    /// </summary>
    public static class CustomMessageBoxExample
    {
        /// <summary>
        /// 演示如何使用CustomMessageBox
        /// </summary>
        public static void ShowExamples(Form parent)
        {
            // 示例1：基本用法，类似于MessageBox.Show
            DialogResult result1 = CustomMessageBox.Show("这是一个简单的消息框", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            
            // 示例2：使用自定义按钮和事件处理（不带额外参数）
            DialogResult result2 = CustomMessageBox.Show(
                "是否要保存更改？\n点击'另存为'可以保存到新文件。", 
                "保存确认", 
                MessageBoxButtons.YesNo, 
                MessageBoxIcon.Question, 
                "另存为", 
                new CustomMessageBox.CustomButtonClickEventHandler(SaveAsHandler));
            
            // 根据结果执行不同的操作
            switch (result2)
            {
                case DialogResult.Yes:
                    MessageBox.Show("文件已保存", "提示");
                    break;
                case DialogResult.No:
                    MessageBox.Show("取消保存", "提示");
                    break;
                // 自定义按钮的处理在SaveAsHandler方法中
            }
            
            // 示例3：使用自定义按钮和事件处理（带额外参数）
            string defaultFileName = "新文档.txt";
            DialogResult result3 = CustomMessageBox.Show(
                "是否要保存更改？\n点击'另存为'可以保存到新文件。\n\n默认文件名已设置为：" + defaultFileName, 
                "保存确认（带默认文件名）", 
                MessageBoxButtons.YesNo, 
                MessageBoxIcon.Question, 
                "另存为", 
                new CustomMessageBox.CustomButtonClickEventHandler(SaveAsHandler),
                defaultFileName); // 传递默认文件名作为自定义数据
            
            // 根据结果执行不同的操作
            switch (result3)
            {
                case DialogResult.Yes:
                    MessageBox.Show("文件已保存", "提示");
                    break;
                case DialogResult.No:
                    MessageBox.Show("取消保存", "提示");
                    break;
                // 自定义按钮的处理在SaveAsHandler方法中
            }
        }
        
        /// <summary>
        /// 自定义按钮的事件处理方法
        /// </summary>
        private static void SaveAsHandler(object sender, CustomButtonClickEventArgs e)
        {
            // 获取用户自定义数据（如果有）
            string defaultFileName = e.UserData as string;
            
            // 这里可以实现另存为的逻辑
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "所有文件|*.*";
                saveFileDialog.Title = "另存为";
                
                // 如果有默认文件名，则设置它
                if (!string.IsNullOrEmpty(defaultFileName))
                {
                    saveFileDialog.FileName = defaultFileName;
                }
                
                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 执行保存操作
                    MessageBox.Show($"文件已另存为: {saveFileDialog.FileName}", "提示");
                }
            }
            
            // 关闭自定义消息框
            if (sender is Form form)
            {
                form.DialogResult = DialogResult.OK; // 设置一个结果
                form.Close();
            }
        }
    }
}