using CommonLib;
using PictureMagic.Common;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows.Forms;
namespace PictureMagic
{
    public partial class ImageMosaicDialog : Form
    {
        private List<ImageWithPath> sourceImages = new List<ImageWithPath>();
        private Image previewImage = null;
        private int rows = 1;
        private int columns = 2;
        private int horizontalSpacing = 10;
        private int verticalSpacing = 10;
        private Color backgroundColor = Color.White;
        private Image backgroundImage = null;
        private bool maintainAspectRatio = true;
        private float outputRatio = 0.5625f; // 16:9 宽屏比例 (默认值，与Designer.cs中cmbOutputRatio.SelectedIndex = 3对应)
                                             // 移除alignCenter变量，因为现在始终居中对齐

        // 用于缩略图预览功能
        // 不再需要mosaicThumbnails字段，只使用mosaicImageSets
        // 定义一个类来存储图片和路径信息
        private class ImageWithPath
        {
            public Image Image { get; set; }
            public string FilePath { get; set; }
            public Size ImageSize { get; set; }
            public bool IsLoaded { get; set; }
            public object Tag { get; set; }

            public ImageWithPath(Image image, string filePath)
            {
                Image = image;
                FilePath = filePath;
                Tag = filePath; // 初始化Tag为文件路径
                if (image != null)
                {
                    ImageSize = image.Size;
                    IsLoaded = true;
                }
                else
                {
                    IsLoaded = false;
                }
            }

            public ImageWithPath(string filePath, Size imageSize)
            {
                Image = null;
                FilePath = filePath;
                ImageSize = imageSize;
                IsLoaded = false;
                Tag = filePath; // 初始化Tag为文件路径
            }

            public void LoadImage()
            {
                if (!IsLoaded && Image == null && !string.IsNullOrEmpty(FilePath))
                {
                    Image = Tools.LoadImageSafe(FilePath);
                    IsLoaded = Image != null;
                    if (IsLoaded)
                    {
                        ImageSize = Image.Size;
                    }
                }
            }

            public void UnloadImage()
            {
                if (IsLoaded && Image != null)
                {
                    try
                    {
                        Image.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"释放图像资源时出错: {ex.Message}");
                    }
                    finally
                    {
                        Image = null;
                        IsLoaded = false;
                    }
                }
            }
        }

        private List<List<ImageWithPath>> mosaicImageSets = new List<List<ImageWithPath>>();
        private int currentPreviewIndex = -1;

        /// <summary>
        /// 处理拼图预览滑块滚动事件
        /// </summary>
        private void trackBarMosaicPreview_Scroll(object sender, EventArgs e)
        {
            int selectedIndex = trackBarMosaicPreview.Value;
            Debug.WriteLine($"trackBarMosaicPreview_Scroll: 选中索引={selectedIndex}");

            // 处理有效的选中索引
            if (selectedIndex >= 0 && selectedIndex < mosaicImageSets.Count)
            {
                // 如果当前预览索引与选中索引相同，不进行重复操作
                if (currentPreviewIndex == selectedIndex)
                    return;

                // 更新当前预览索引
                currentPreviewIndex = selectedIndex;
                try
                {
                    // 更新预览信息标签
                    lblMosaicPreviewInfo.Text = $"拼图 {selectedIndex + 1}/{mosaicImageSets.Count}";

                    // 在 lstImages 中高亮显示当前子集中的文件
                    HighlightCurrentSubsetFiles(selectedIndex);

                    // 释放当前预览图像资源
                    if (picPreview.Image != null)
                    {
                        picPreview.Image.Dispose();
                        picPreview.Image = null;
                    }

                    // 调用 UpdatePreviewDisplay 方法更新预览显示
                    // 该方法会使用当前选中的图片子集创建拼图并显示
                    UpdatePreviewDisplay();
                }
                catch (Exception ex)
                {
                    // 处理错误但不中断用户体验
                    Debug.WriteLine($"无法显示拼图预览 {selectedIndex}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 生成拼图缩略图并添加到列表中
        /// </summary>
        private void PrepareMosaicPreview()
        {
            // 如果不需要重新准备拼图预览，直接返回
            // 注意：当positionsNeedRecalculation为true时，需要重新准备拼图预览
            // 这确保在切换背景展示方式时能正确准备拼图预览
            if (!positionsNeedRecalculation && mosaicImageSets.Count > 0 &&
                lastImageCount == sourceImages.Count && lastRows == rows && lastColumns == columns)
            {
                return;
            }

            // 如果源图片列表为空，清空拼图预览并返回
            if (sourceImages.Count == 0)
            {
                ClearResources();
                return;
            }

            // 保存当前光标状态
            Cursor originalCursor = Cursor.Current;
            Cursor.Current = Cursors.WaitCursor;

            // 显示状态消息
            lblStatus.Text = "正在准备拼图预览...";

            // 清空现有图片子集
            ClearResources();

            // 计算需要准备的拼图数量
            int totalImages = sourceImages.Count;
            int imagesPerMosaic = rows * columns;
            int mosaicCount = (int)Math.Ceiling((double)totalImages / imagesPerMosaic);

            // 准备每个拼图的图片子集
            for (int i = 0; i < mosaicCount; i++)
            {
                // 获取当前拼图的图片子集
                List<ImageWithPath> imageSubset = new List<ImageWithPath>();
                int startIndex = i * imagesPerMosaic;
                int endIndex = Math.Min(startIndex + imagesPerMosaic, totalImages);

                for (int j = startIndex; j < endIndex; j++)
                {
                    // 获取图片信息（不加载图片，只复制引用）
                    ImageWithPath originalImageInfo = sourceImages[j];
                    // 创建新的 ImageWithPath 对象，但不加载图片
                    ImageWithPath newImageInfo = new ImageWithPath(originalImageInfo.FilePath, originalImageInfo.ImageSize);
                    imageSubset.Add(newImageInfo);
                }

                // 保存图片子集
                mosaicImageSets.Add(imageSubset);
            }

            // 设置TrackBar的属性
            trackBarMosaicPreview.Minimum = 0;
            trackBarMosaicPreview.Maximum = mosaicCount > 0 ? mosaicCount - 1 : 0;

            // 根据拼图数量显式设置TrackBar的启用状态
            if (mosaicCount > 0)
            {
                // 确保TrackBar启用
                trackBarMosaicPreview.Enabled = true;
                // 高亮显示第一个子集中的文件
                HighlightCurrentSubsetFiles(0);
                currentPreviewIndex = 0;
                lblMosaicPreviewInfo.Text = $"拼图 1/{mosaicCount}";
            }
            else
            {
                lblMosaicPreviewInfo.Text = "无拼图预览";
                // 确保TrackBar禁用
                trackBarMosaicPreview.Enabled = false;
            }

            trackBarMosaicPreview.Value = 0;
            trackBarMosaicPreview.TickFrequency = Math.Max(1, mosaicCount / 10);

            // 如果有拼图，选中第一个并更新预览
            if (mosaicCount > 0)
            {
                currentPreviewIndex = 0;
                lblMosaicPreviewInfo.Text = $"拼图 1/{mosaicCount}";
                UpdatePreviewDisplay();
            }
            else
            {
                lblMosaicPreviewInfo.Text = "无拼图预览";
            }

            // 更新状态变量
            lastImageCount = sourceImages.Count;
            lastRows = rows;
            lastColumns = columns;

            // 更新状态标签
            lblStatus.Text = $"已准备 {mosaicCount} 个拼图预览";

            // 恢复原始光标
            Cursor.Current = originalCursor;
        }

        /// <summary>
        /// 清空
        /// </summary>
        private void ClearResources()
        {
            // 清空图片子集集合，确保释放所有加载的图片资源
            foreach (var imageSubset in mosaicImageSets)
            {
                foreach (var imgInfo in imageSubset)
                {
                    imgInfo.UnloadImage();
                }
                imageSubset.Clear();
            }
            mosaicImageSets.Clear();
            currentPreviewIndex = -1;

            // 释放预览图像资源
            if (picPreview != null && picPreview.Image != null)
            {
                try
                {
                    Image oldImage = picPreview.Image;
                    picPreview.Image = null;
                    oldImage.Dispose();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"释放预览图像时出错: {ex.Message}");
                }
            }

            // 强制进行垃圾回收
            GC.Collect();
            GC.WaitForPendingFinalizers();

            // 重置TrackBar
            trackBarMosaicPreview.Value = 0;
            trackBarMosaicPreview.Maximum = 0;
            // 始终禁用TrackBar，直到有新的拼图集合
            trackBarMosaicPreview.Enabled = false;
            lblMosaicPreviewInfo.Text = "无拼图预览";

            // 清除所有高亮显示和选中状态
            lstImages.SuspendLayout();
            lstImages.BeginUpdate();
            try
            {
                // 清除所有项目的高亮显示和选中状态
                foreach (ListViewItem item in lstImages.Items)
                {
                    item.Selected = false;
                    item.BackColor = SystemColors.Window;
                }
                // 确保清除ListView的选中项
                lstImages.SelectedItems.Clear();
                // 确保没有选中项
                lstImages.SelectedIndices.Clear();
            }
            finally
            {
                lstImages.EndUpdate();
                lstImages.ResumeLayout();
            }

            // 重置状态变量，确保下次添加图片时会重新生成预览
            lastImageCount = 0;
            lastRows = 0;
            lastColumns = 0;
            positionsNeedRecalculation = true;
        }


        // 自定义比较器类，用于ListView的多列排序
        public class ListViewItemComparer : System.Collections.IComparer, IComparer<ListViewItem>
        {
            private int columnIndex;
            private bool ascending;

            public ListViewItemComparer(int columnIndex, bool ascending = true)
            {
                this.columnIndex = columnIndex;
                this.ascending = ascending;
            }

            // 实现IComparer<ListViewItem>接口的Compare方法
            public int Compare(ListViewItem x, ListViewItem y)
            {
                string textX = x.SubItems[columnIndex].Text;
                string textY = y.SubItems[columnIndex].Text;

                // 支持数字排序
                int result;
                if (double.TryParse(textX, out double numX) && double.TryParse(textY, out double numY))
                {
                    result = numX.CompareTo(numY);
                }
                else
                {
                    result = string.Compare(textX, textY);
                }

                return ascending ? result : -result;
            }

            // 实现System.Collections.IComparer接口的Compare方法
            int System.Collections.IComparer.Compare(object x, object y)
            {
                return Compare((ListViewItem)x, (ListViewItem)y);
            }
        }

        // 用于预览窗口的缩放和平移
        private float zoomFactor = 1.0f;
        private bool isDragging = false;
        private Point lastMousePosition;
        private Point imageOffset = new Point(0, 0);

        // 添加缺失的变量
        private List<Rectangle> imagePositions = new List<Rectangle>();
        private int canvasWidth = 800; // 默认画布宽度
        private bool allowAutoCalculation = true; // 控制是否允许自动计算间距
        private bool isUpdatingFromCode = false; // 标志是否正在从代码更新控件值

        // 拼接后的4个边距
        private int marginLeft = 0;
        private int marginRight = 0;
        private int marginTop = 0;
        private int marginBottom = 0;

        // 性能优化相关变量
        private System.Windows.Forms.Timer previewUpdateTimer;
        private List<Rectangle> cachedPositions;
        private bool positionsNeedRecalculation = true;
        private const int MAX_PREVIEW_WIDTH = 2048;
        private const int MAX_PREVIEW_HEIGHT = 1536;

        private Image resultImage;
        [System.ComponentModel.Browsable(false)]
        [System.ComponentModel.DesignerSerializationVisibility(System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public Image ResultImage
        {
            get { return resultImage; }
            private set { resultImage = value; }
        }

        public string OutputFilePath = string.Empty;
        public ImageMosaicDialog()
        {
            InitializeComponent();

            // 添加单选按钮的事件处理
            rdbStretch.CheckedChanged += RadioButton_CheckedChanged;
            rdbAutoSize.CheckedChanged += RadioButton_CheckedChanged;
            Width = (int)(Screen.PrimaryScreen.WorkingArea.Width * 0.85);
            Height = (int)(Screen.PrimaryScreen.WorkingArea.Height * 0.85);

            // 初始化状态标签
            lblStatus.Text = "就绪";

            // 不再需要设置ListView的属性，因为已经替换为TrackBar控件
        }

        // 已移除对ListView控件的使用，改为使用TrackBar控制拼图预览

        /// <summary>
        /// 设置ListView控件的图标间距
        /// </summary>
        /// <param name="listView">要设置的ListView控件</param>
        /// <param name="width">图标宽度间距</param>
        /// <param name="height">图标高度间距</param>
        // SetListViewSpacing方法和NativeMethods类已移除，因为不再使用ListView控件


        private void ButtonAddImages_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.RestoreDirectory = true;
                // 从设置中加载上次的路径
                var settings = AppSettings.LoadSettings();
                if (!string.IsNullOrEmpty(settings.LastAddImagesPath))
                {
                    openFileDialog.InitialDirectory = settings.LastAddImagesPath;
                }
                openFileDialog.Filter = "图像文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif";
                openFileDialog.Multiselect = true;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    // 启用画布尺寸控件，以便重新计算
                    numCanvasWidth.Enabled = true;
                    numCanvasHeight.Enabled = true;

                    int inconsistentResolutionCount = 0;
                    ProgressForm progressForm = null;

                    try
                    {
                        // 始终显示进度窗口，不管图片数量多少
                        //if (openFileDialog.FileNames.Length > 10)
                        //{
                        // 使用ShowProgressDialog方法显示进度窗体，确保遮罩效果生效
                        progressForm = ProgressForm.ShowProgressDialog(this, "正在加载图片...", true); // 使用遮罩效果
                        progressForm.SetContinuousStyle(0, openFileDialog.FileNames.Length);
                        progressForm.Text = "添加图片";
                        //}

                        // 暂停界面更新，减少闪烁
                        lstImages.BeginUpdate();

                        int currentIndex = 0;
                        foreach (string fileName in openFileDialog.FileNames)
                        {
                            try
                            {
                                // 更新进度
                                if (progressForm != null)
                                {
                                    progressForm.UpdateProgress(++currentIndex);
                                    progressForm.UpdateMessage($"正在加载图片 {currentIndex}/{openFileDialog.FileNames.Length}...");

                                    // 检查是否取消操作
                                    if (progressForm.IsCancelled())
                                    {
                                        break;
                                    }

                                    if (Tools.IsItemExist(lstImages, fileName))
                                    {
                                        progressForm.UpdateMessage($"图片 {fileName} 已存在列表中，跳过...");
                                        continue;
                                    }
                                }
                                else
                                {
                                    if (Tools.IsItemExist(lstImages, fileName))
                                    {
                                        continue;
                                    }
                                }
                                // 只获取图片的基本信息，不加载完整图片
                                string fullPath = Path.GetFullPath(fileName);
                                Size imageSize;

                                // 临时加载图片以获取尺寸，然后立即释放
                                using (Image tempImg = Image.FromFile(fileName))
                                {
                                    imageSize = tempImg.Size;
                                }

                                // 创建轻量级的 ImageWithPath 对象，不保留图片对象
                                ImageWithPath imageInfo = new ImageWithPath(fullPath, imageSize);
                                sourceImages.Add(imageInfo);
                                lstImages.Items.Add(fullPath);

                                // 检查分辨率是否与第一张图片一致
                                if (sourceImages.Count > 1)
                                {
                                    Size firstImageSize = sourceImages[0].ImageSize;
                                    if (imageSize.Width != firstImageSize.Width || imageSize.Height != firstImageSize.Height)
                                    {
                                        inconsistentResolutionCount++;
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"无法加载图片 {fileName}: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                    finally
                    {
                        // 恢复界面更新
                        lstImages.EndUpdate();

                        // 关闭进度窗口
                        if (progressForm != null)
                        {
                            progressForm.CloseProgress();
                            progressForm = null;
                        }
                    }

                    // 如果添加了图片，选择第一张图片
                    if (lstImages.Items.Count > 0 && lstImages.SelectedIndices.Count == 0)
                    {
                        lstImages.Items[0].Selected = true;
                    }

                    // 自动调整列宽度以适应内容
                    lstImages.AutoResizeColumns(ColumnHeaderAutoResizeStyle.ColumnContent);
                    UpdateImageCountLabel();

                    // 添加新图片时重新启用自动计算
                    allowAutoCalculation = true;

                    // 提示分辨率不一致的图片数量
                    if (inconsistentResolutionCount > 0)
                    {
                        MessageBox.Show($"本次导入的{openFileDialog.FileNames.Length}个图片文件中，有{inconsistentResolutionCount}个文件与当前列表中的第一个文件分辨率不一致，可能会导致变形。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                    //按文件名排序
                    sourceImages.Sort((a, b) => string.Compare(a.Tag.ToString(), b.Tag.ToString()));
                    lstImages.ListViewItemSorter = new ListViewItemComparer(0, true);
                    lstImages.Sort();

                    // 更新预览
                    DelayedUpdatePreview();

                    // 更新上次选择的路径
                    // 更新上次选择的路径并保存
                    if (openFileDialog.FileNames.Length > 0)
                    {
                        settings.LastAddImagesPath = Path.GetDirectoryName(openFileDialog.FileNames[0]);
                        AppSettings.SaveSettings(settings);
                    }
                }
            }
        }

        private void UpdateImageCountLabel()
        {
            lblImageCount.Text = $"图片总数: {sourceImages.Count}";
        }

        private void ButtonClearAllImages_Click(object sender, EventArgs e)
        {
            if (sourceImages.Count == 0)
            {
                return; // 如果没有图片，直接返回
            }

            // 询问用户是否确定要清空所有图片
            DialogResult result = MessageBox.Show("确定要清空所有图片吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                // 暂停界面更新，减少闪烁
                lstImages.BeginUpdate();

                try
                {
                    // 启用画布尺寸控件，以便重新计算
                    numCanvasWidth.Enabled = true;
                    numCanvasHeight.Enabled = true;

                    // 释放所有图片资源
                    foreach (ImageWithPath imgInfo in sourceImages)
                    {
                        try
                        {
                            if (imgInfo != null)
                            {
                                imgInfo.UnloadImage(); // 使用 UnloadImage 方法释放资源
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"释放图片资源时出错: {ex.Message}");
                        }
                    }

                    // 清空图片列表
                    sourceImages.Clear();
                    lstImages.Items.Clear();

                    // 清空缩略图和重置状态变量
                    ClearResources();

                    // 强制进行垃圾回收
                    GC.Collect();
                    GC.WaitForPendingFinalizers();

                    // 更新图片计数
                    UpdateImageCountLabel();

                    // 标记位置需要重新计算
                    positionsNeedRecalculation = true;
                }
                finally
                {
                    // 恢复界面更新
                    lstImages.EndUpdate();
                }

                // 更新预览（清空预览）
                if (previewImage != null)
                {
                    previewImage.Dispose();
                    previewImage = null;
                    picPreview.Image = null;
                }

                // 更新状态标签
                lblStatus.Text = "已清空所有图片";
            }
        }

        private void ButtonRemoveImages_Click(object sender, EventArgs e)
        {
            if (lstImages.SelectedIndices.Count > 0)
            {
                // 暂停界面更新，减少闪烁
                lstImages.BeginUpdate();

                try
                {
                    // 启用画布尺寸控件，以便重新计算
                    numCanvasWidth.Enabled = true;
                    numCanvasHeight.Enabled = true;

                    // 创建一个临时列表存储要删除的索引
                    List<int> indicesToRemove = new List<int>();
                    for (int i = 0; i < lstImages.SelectedIndices.Count; i++)
                    {
                        indicesToRemove.Add(lstImages.SelectedIndices[i]);
                    }

                    // 对索引进行排序（从大到小），避免删除时索引变化问题
                    indicesToRemove.Sort();
                    indicesToRemove.Reverse();

                    // 根据索引删除图片
                    foreach (int index in indicesToRemove)
                    {
                        if (index >= 0 && index < sourceImages.Count)
                        {
                            try
                            {
                                // 释放图片资源
                                if (sourceImages[index] != null)
                                {
                                    sourceImages[index].UnloadImage(); // 使用 UnloadImage 方法释放资源
                                    sourceImages[index] = null; // 设置为null以便GC回收
                                }
                                sourceImages.RemoveAt(index);
                                lstImages.Items.RemoveAt(index);
                            }
                            catch (Exception ex)
                            {
                                // 记录错误但继续处理其他图片
                                Debug.WriteLine($"删除图片时出错: {ex.Message}");
                            }
                        }
                    }

                    // 强制进行垃圾回收
                    GC.Collect();
                    GC.WaitForPendingFinalizers();

                    // 如果所有图片都被删除，清空缩略图
                    if (sourceImages.Count == 0)
                    {
                        ClearResources();
                        // 确保lstImages也被清空
                        lstImages.Items.Clear();
                        UpdateImageCountLabel();
                    }
                    else
                    {
                        //按文件名排序
                        sourceImages.Sort((a, b) => string.Compare(a.Tag.ToString(), b.Tag.ToString()));
                        lstImages.ListViewItemSorter = new ListViewItemComparer(0, true);
                        lstImages.Sort();
                    }

                    // 自动调整列宽度以适应内容
                    lstImages.AutoResizeColumns(ColumnHeaderAutoResizeStyle.ColumnContent);
                    // 移除图片后更新计数
                    UpdateImageCountLabel();
                    // 标记位置需要重新计算
                    positionsNeedRecalculation = true;
                }
                finally
                {
                    // 恢复界面更新
                    lstImages.EndUpdate();
                }

                // 更新预览
                DelayedUpdatePreview();
            }

            // 清除选中状态，避免连续删除时出错
            lstImages.SelectedIndices.Clear();
        }

        private void NumericRows_ValueChanged(object sender, EventArgs e)
        {
            NumericUpDown numericUpDown = sender as NumericUpDown;
            if (numericUpDown != null)
            {
                rows = (int)numericUpDown.Value;

                // 行数变化时重新启用自动计算
                allowAutoCalculation = true;

                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;

                // 根据新规则计算垂直间距：（画布的高度-行数*照片里最高的高度）/（行数-1）
                if (rows > 1 && numCanvasHeight != null && sourceImages.Count > 0)
                {
                    int height = (int)numCanvasHeight.Value;

                    // 计算照片的最大高度
                    int maxHeight = 0;
                    foreach (ImageWithPath imgWithPath in sourceImages)
                    {
                        if (imgWithPath.Image != null)
                        {
                            maxHeight = Math.Max(maxHeight, imgWithPath.Image.Height);
                        }
                    }

                    // 计算垂直间距
                    verticalSpacing = (height - rows * maxHeight) / (rows - 1);
                    verticalSpacing = Math.Max(0, verticalSpacing); // 确保间距不为负数

                    // 安全地设置垂直间距值
                    try
                    {
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = Math.Max(0, verticalSpacing);
                        isUpdatingFromCode = false;
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        // 如果出现范围错误，使用最大允许值
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = numVerticalSpacing.Maximum;
                        isUpdatingFromCode = false;
                        verticalSpacing = (int)numVerticalSpacing.Maximum;
                    }
                }

                // 标记位置需要重新计算
                positionsNeedRecalculation = true;
                // 更新预览
                DelayedUpdatePreview();
            }
        }

        private void NumericColumns_ValueChanged(object sender, EventArgs e)
        {
            NumericUpDown numericUpDown = sender as NumericUpDown;
            if (numericUpDown != null)
            {
                columns = (int)numericUpDown.Value;

                // 列数变化时重新启用自动计算
                allowAutoCalculation = true;

                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;

                // 根据新规则计算画布宽度：照片里最宽的宽度*列数+（列数-1）*水平间距
                if (sourceImages.Count > 0 && numCanvasWidth != null)
                {
                    // 计算照片的最大宽度和高度
                    int maxWidth = 0;
                    int maxHeight = 0;
                    foreach (ImageWithPath imgWithPath in sourceImages)
                    {
                        if (imgWithPath.Image != null)
                        {
                            maxWidth = Math.Max(maxWidth, imgWithPath.Image.Width);
                            maxHeight = Math.Max(maxHeight, imgWithPath.Image.Height);
                        }
                    }

                    // 标记位置需要重新计算
                    positionsNeedRecalculation = true;

                    // 根据新规则计算画布宽度
                    int originalCalculatedWidth = maxWidth * columns + (columns - 1) * horizontalSpacing;
                    int calculatedWidth = originalCalculatedWidth;

                    // 确保计算出的宽度不小于最小值
                    calculatedWidth = Math.Max(calculatedWidth, (int)numCanvasWidth.Minimum);
                    // 确保计算出的宽度不大于最大值
                    calculatedWidth = Math.Min(calculatedWidth, (int)numCanvasWidth.Maximum);

                    // 检查宽度是否被限制
                    bool widthWasLimited = calculatedWidth != originalCalculatedWidth;

                    // 安全地设置画布宽度值
                    try
                    {
                        numCanvasWidth.Value = calculatedWidth;

                        // 如果宽度被限制，反向计算水平间距
                        if (widthWasLimited && columns > 1)
                        {
                            // 反向计算水平间距：(画布宽度 - 列数*照片最大宽度) / (列数-1)
                            int newHorizontalSpacing = (calculatedWidth - maxWidth * columns) / (columns - 1);
                            newHorizontalSpacing = Math.Max(0, newHorizontalSpacing); // 确保间距不为负数

                            // 更新水平间距控件值
                            if (newHorizontalSpacing != horizontalSpacing)
                            {
                                isUpdatingFromCode = true;
                                numHorizontalSpacing.Value = newHorizontalSpacing;
                                isUpdatingFromCode = false;
                                horizontalSpacing = newHorizontalSpacing;
                            }
                        }
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        // 如果仍然出现范围错误，使用最小允许值
                        numCanvasWidth.Value = numCanvasWidth.Minimum;
                        calculatedWidth = (int)numCanvasWidth.Minimum;

                        // 反向计算水平间距
                        if (columns > 1)
                        {
                            // 反向计算水平间距：(画布宽度 - 列数*照片最大宽度) / (列数-1)
                            int newHorizontalSpacing = (calculatedWidth - maxWidth * columns) / (columns - 1);
                            newHorizontalSpacing = Math.Max(0, newHorizontalSpacing); // 确保间距不为负数

                            // 更新水平间距控件值
                            if (newHorizontalSpacing != horizontalSpacing)
                            {
                                isUpdatingFromCode = true;
                                numHorizontalSpacing.Value = newHorizontalSpacing;
                                isUpdatingFromCode = false;
                                horizontalSpacing = newHorizontalSpacing;
                            }
                        }
                    }

                    // 根据新规则计算画布高度：画布宽度*输出比例
                    int calculatedHeight = (int)(calculatedWidth * outputRatio);

                    // 确保高度在允许的范围内
                    calculatedHeight = Math.Max(calculatedHeight, (int)numCanvasHeight.Minimum);
                    calculatedHeight = Math.Min(calculatedHeight, (int)numCanvasHeight.Maximum);

                    // 安全地设置画布高度值
                    try
                    {
                        numCanvasHeight.Value = calculatedHeight;
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        // 如果仍然出现范围错误，使用最小允许值
                        numCanvasHeight.Value = numCanvasHeight.Minimum;
                        calculatedHeight = (int)numCanvasHeight.Minimum;
                    }

                    // 根据新规则计算垂直间距：（画布的高度-行数*照片里最高的高度）/（行数-1）
                    if (rows > 1)
                    {
                        verticalSpacing = (calculatedHeight - rows * maxHeight) / (rows - 1);
                        verticalSpacing = Math.Max(0, verticalSpacing); // 确保间距不为负数

                        // 安全地设置垂直间距值
                        try
                        {
                            isUpdatingFromCode = true;
                            numVerticalSpacing.Value = Math.Max(0, verticalSpacing);
                            isUpdatingFromCode = false;
                        }
                        catch (ArgumentOutOfRangeException)
                        {
                            // 如果出现范围错误，使用最大允许值
                            isUpdatingFromCode = true;
                            numVerticalSpacing.Value = numVerticalSpacing.Maximum;
                            isUpdatingFromCode = false;
                            verticalSpacing = (int)numVerticalSpacing.Maximum;
                        }
                    }
                }

                // 标记位置需要重新计算
                positionsNeedRecalculation = true;
                DelayedUpdatePreview();
            }
        }

        private void NumericHorizontalSpacing_ValueChanged(object sender, EventArgs e)
        {
            // 如果是代码更新，直接返回
            if (isUpdatingFromCode) return;

            NumericUpDown numericUpDown = sender as NumericUpDown;
            if (numericUpDown != null)
            {
                horizontalSpacing = (int)numericUpDown.Value;

                // 用户手动修改间距时，禁用自动计算
                allowAutoCalculation = false;

                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;

                // 根据新规则计算画布宽度：照片里最宽的宽度*列数+（列数-1）*水平间距
                if (sourceImages.Count > 0 && numCanvasWidth != null)
                {
                    // 计算照片的最大宽度和高度
                    int maxWidth = 0;
                    int maxHeight = 0;
                    foreach (ImageWithPath imgWithPath in sourceImages)
                    {
                        if (imgWithPath.Image != null)
                        {
                            maxWidth = Math.Max(maxWidth, imgWithPath.Image.Width);
                            maxHeight = Math.Max(maxHeight, imgWithPath.Image.Height);
                        }
                    }

                    // 根据新规则计算画布宽度
                    int originalCalculatedWidth = maxWidth * columns + (columns - 1) * horizontalSpacing;
                    int calculatedWidth = originalCalculatedWidth;

                    // 确保计算出的宽度不小于最小值
                    calculatedWidth = Math.Max(calculatedWidth, (int)numCanvasWidth.Minimum);
                    // 确保计算出的宽度不大于最大值
                    calculatedWidth = Math.Min(calculatedWidth, (int)numCanvasWidth.Maximum);

                    // 检查宽度是否被限制
                    bool widthWasLimited = calculatedWidth != originalCalculatedWidth;

                    // 安全地设置画布宽度值
                    try
                    {
                        numCanvasWidth.Value = calculatedWidth;

                        // 如果宽度被限制，反向计算水平间距
                        if (widthWasLimited && columns > 1)
                        {
                            // 反向计算水平间距：(画布宽度 - 列数*照片最大宽度) / (列数-1)
                            int newHorizontalSpacing = (calculatedWidth - maxWidth * columns) / (columns - 1);
                            newHorizontalSpacing = Math.Max(0, newHorizontalSpacing); // 确保间距不为负数

                            // 更新水平间距控件值
                            if (newHorizontalSpacing != horizontalSpacing)
                            {
                                isUpdatingFromCode = true;
                                numHorizontalSpacing.Value = newHorizontalSpacing;
                                isUpdatingFromCode = false;
                                horizontalSpacing = newHorizontalSpacing;
                            }
                        }
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        // 如果仍然出现范围错误，使用最小允许值
                        numCanvasWidth.Value = numCanvasWidth.Minimum;
                        calculatedWidth = (int)numCanvasWidth.Minimum;

                        // 反向计算水平间距
                        if (columns > 1)
                        {
                            // 反向计算水平间距：(画布宽度 - 列数*照片最大宽度) / (列数-1)
                            int newHorizontalSpacing = (calculatedWidth - maxWidth * columns) / (columns - 1);
                            newHorizontalSpacing = Math.Max(0, newHorizontalSpacing); // 确保间距不为负数

                            // 更新水平间距控件值
                            if (newHorizontalSpacing != horizontalSpacing)
                            {
                                isUpdatingFromCode = true;
                                numHorizontalSpacing.Value = newHorizontalSpacing;
                                isUpdatingFromCode = false;
                                horizontalSpacing = newHorizontalSpacing;
                            }
                        }
                    }

                    // 根据新规则计算画布高度：画布宽度*输出比例
                    int calculatedHeight = (int)(calculatedWidth * outputRatio);

                    // 确保高度在允许的范围内
                    calculatedHeight = Math.Max(calculatedHeight, (int)numCanvasHeight.Minimum);
                    calculatedHeight = Math.Min(calculatedHeight, (int)numCanvasHeight.Maximum);

                    // 安全地设置画布高度值
                    try
                    {
                        numCanvasHeight.Value = calculatedHeight;
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        // 如果仍然出现范围错误，使用最小允许值
                        numCanvasHeight.Value = numCanvasHeight.Minimum;
                        calculatedHeight = (int)numCanvasHeight.Minimum;
                    }

                    // 根据新规则计算垂直间距：（画布的高度-行数*照片里最高的高度）/（行数-1）
                    if (rows > 1)
                    {
                        verticalSpacing = (calculatedHeight - rows * maxHeight) / (rows - 1);
                        verticalSpacing = Math.Max(0, verticalSpacing); // 确保间距不为负数

                        // 安全地设置垂直间距值
                        try
                        {
                            isUpdatingFromCode = true;
                            numVerticalSpacing.Value = Math.Max(0, verticalSpacing);
                            isUpdatingFromCode = false;
                        }
                        catch (ArgumentOutOfRangeException)
                        {
                            // 如果出现范围错误，使用最大允许值
                            isUpdatingFromCode = true;
                            numVerticalSpacing.Value = numVerticalSpacing.Maximum;
                            isUpdatingFromCode = false;
                            verticalSpacing = (int)numVerticalSpacing.Maximum;
                        }
                    }
                }

                // 标记位置需要重新计算
                positionsNeedRecalculation = true;
                DelayedUpdatePreview();
            }
        }

        private void NumericVerticalSpacing_ValueChanged(object sender, EventArgs e)
        {
            // 如果是代码更新，直接返回
            if (isUpdatingFromCode) return;

            NumericUpDown numericUpDown = sender as NumericUpDown;
            if (numericUpDown != null)
            {
                // 当用户手动更改垂直间距时，只更新垂直间距值和预览，不重新计算画布高度
                verticalSpacing = (int)numericUpDown.Value;

                // 用户手动修改间距时，禁用自动计算
                allowAutoCalculation = false;

                DelayedUpdatePreview();
            }
        }

        private void ButtonBackgroundColor_Click(object sender, EventArgs e)
        {
            using (ColorDialog colorDialog = new ColorDialog())
            {
                colorDialog.Color = backgroundColor;
                if (colorDialog.ShowDialog() == DialogResult.OK)
                {
                    backgroundColor = colorDialog.Color;
                    Button button = sender as Button;
                    if (button != null)
                    {
                        button.BackColor = backgroundColor;
                    }
                    // 标记位置需要重新计算
                    positionsNeedRecalculation = true;
                    DelayedUpdatePreview();
                }
            }
        }

        private void CheckBoxMaintainAspectRatio_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox checkBox = sender as CheckBox;
            if (checkBox != null)
            {
                maintainAspectRatio = checkBox.Checked;
                // 标记位置需要重新计算
                positionsNeedRecalculation = true;
                DelayedUpdatePreview();
            }
        }

        // 移除动态设置对齐方式的事件处理程序，改为在初始化时设置默认值
        // private void CheckBoxAlignCenter_CheckedChanged(object sender, EventArgs e)
        // {
        //     CheckBox checkBox = sender as CheckBox;
        //     if (checkBox != null)
        //     {
        //         alignCenter = checkBox.Checked;
        //         UpdatePreview();
        //     }
        // }

        // 移除对齐方式相关的事件处理程序



        private void CmbOutputRatio_SelectedIndexChanged(object sender, EventArgs e)
        {
            ComboBox comboBox = sender as ComboBox;
            if (comboBox != null && comboBox.SelectedIndex >= 0)
            {
                // 输出比例变化时重新启用自动计算
                allowAutoCalculation = true;

                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;

                // 根据选择的索引设置输出比例值
                switch (comboBox.SelectedIndex)
                {
                    case 0: // 1:1 (正方形)
                        outputRatio = 1.0f;
                        break;
                    case 1: // 4:3 (标准)
                        outputRatio = 0.75f;
                        break;
                    case 2: // 3:2 (照片)
                        outputRatio = 0.6667f;
                        break;
                    case 3: // 16:9 (宽屏)
                        outputRatio = 0.5625f;
                        break;
                    case 4: // 2:1 (全景)
                        outputRatio = 0.5f;
                        break;
                    case 5: // 3:4 (竖屏标准)
                        outputRatio = 1.3333f;
                        break;
                    case 6: // 2:3 (竖屏照片)
                        outputRatio = 1.5f;
                        break;
                    case 7: // 9:16 (竖屏宽屏)
                        outputRatio = 1.78f;
                        break;
                    case 8: // 1:2 (竖屏全景)
                        outputRatio = 2.0f;
                        break;
                    default:
                        outputRatio = 1.0f;
                        break;
                }

                // 根据选择的输出比例调整画布高度
                if (numCanvasWidth != null && numCanvasHeight != null)
                {
                    // 获取当前画布宽度
                    int width = (int)numCanvasWidth.Value;

                    // 根据输出比例计算新的高度
                    int newHeight = (int)(width * outputRatio);

                    // 确保高度在允许的范围内
                    newHeight = Math.Max(newHeight, (int)numCanvasHeight.Minimum);
                    newHeight = Math.Min(newHeight, (int)numCanvasHeight.Maximum);

                    // 安全地更新画布高度
                    try
                    {
                        numCanvasHeight.Value = newHeight;
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        // 如果仍然出现范围错误，使用最小允许值
                        numCanvasHeight.Value = numCanvasHeight.Minimum;
                        newHeight = (int)numCanvasHeight.Minimum;
                    }

                    // 根据新规则计算垂直间距：（画布的高度-行数*照片里最高的高度）/（行数-1）
                    if (rows > 1 && sourceImages.Count > 0)
                    {
                        // 计算照片的最大高度
                        int maxHeight = 0;
                        foreach (ImageWithPath imgWithPath in sourceImages)
                        {
                            if (imgWithPath.Image != null)
                            {
                                maxHeight = Math.Max(maxHeight, imgWithPath.Image.Height);
                            }
                        }

                        // 计算垂直间距
                        verticalSpacing = (newHeight - rows * maxHeight) / (rows - 1);
                        verticalSpacing = Math.Max(0, verticalSpacing); // 确保间距不为负数
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = Math.Max(0, verticalSpacing);
                        isUpdatingFromCode = false;
                    }
                }

                UpdatePreview();
            }
        }

        private void NumCanvasWidth_ValueChanged(object sender, EventArgs e)
        {
            // 根据宽度和输出比例计算高度
            if (numCanvasHeight != null)
            {
                int width = (int)numCanvasWidth.Value;
                int height = (int)(width * outputRatio);

                // 确保高度在允许的范围内
                height = Math.Max(height, (int)numCanvasHeight.Minimum);
                height = Math.Min(height, (int)numCanvasHeight.Maximum);

                // 安全地设置高度
                try
                {
                    numCanvasHeight.Value = height;
                }
                catch (ArgumentOutOfRangeException)
                {
                    // 如果仍然出现范围错误，使用最小允许值
                    numCanvasHeight.Value = numCanvasHeight.Minimum;
                    height = (int)numCanvasHeight.Minimum;
                }

                // 根据新规则计算垂直间距：（内容区域的高度-行数*照片里最高的高度）/（行数-1）
                if (rows > 1 && sourceImages.Count > 0)
                {
                    // 减去上下边距，得到实际的内容区域高度
                    int contentAreaHeight = height - marginTop - marginBottom;

                    // 计算照片的最大高度
                    int maxHeight = 0;
                    foreach (ImageWithPath imgWithPath in sourceImages)
                    {
                        if (imgWithPath.Image != null)
                        {
                            maxHeight = Math.Max(maxHeight, imgWithPath.Image.Height);
                        }
                    }

                    // 计算垂直间距（基于内容区域，不包括边距）
                    verticalSpacing = (contentAreaHeight - rows * maxHeight) / (rows - 1);
                    verticalSpacing = Math.Max(0, verticalSpacing); // 确保间距不为负数

                    // 安全地设置垂直间距值
                    try
                    {
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = Math.Max(0, verticalSpacing);
                        isUpdatingFromCode = false;
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        // 如果出现范围错误，使用最大允许值
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = numVerticalSpacing.Maximum;
                        isUpdatingFromCode = false;
                        verticalSpacing = (int)numVerticalSpacing.Maximum;
                    }
                }

                // 标记位置需要重新计算
                positionsNeedRecalculation = true;
                // 更新预览
                DelayedUpdatePreview();
            }
        }

        private void NumCanvasHeight_ValueChanged(object sender, EventArgs e)
        {
            // 根据新规则计算垂直间距：（内容区域的高度-行数*照片里最高的高度）/（行数-1）
            if (rows > 1 && sourceImages.Count > 0)
            {
                int totalHeight = (int)numCanvasHeight.Value;
                // 减去上下边距，得到实际的内容区域高度
                int contentAreaHeight = totalHeight - marginTop - marginBottom;

                // 计算照片的最大高度
                int maxHeight = 0;
                foreach (ImageWithPath imgWithPath in sourceImages)
                {
                    if (imgWithPath.Image != null)
                    {
                        maxHeight = Math.Max(maxHeight, imgWithPath.Image.Height);
                    }
                }

                // 计算垂直间距（基于内容区域，不包括边距）
                verticalSpacing = (contentAreaHeight - rows * maxHeight) / (rows - 1);
                verticalSpacing = Math.Max(0, verticalSpacing); // 确保间距不为负数

                // 安全地设置垂直间距值
                try
                {
                    isUpdatingFromCode = true;
                    numVerticalSpacing.Value = Math.Max(0, verticalSpacing);
                    isUpdatingFromCode = false;
                }
                catch (ArgumentOutOfRangeException)
                {
                    // 如果出现范围错误，使用最大允许值
                    isUpdatingFromCode = true;
                    numVerticalSpacing.Value = numVerticalSpacing.Maximum;
                    isUpdatingFromCode = false;
                    verticalSpacing = (int)numVerticalSpacing.Maximum;
                }
            }

            // 标记位置需要重新计算
            positionsNeedRecalculation = true;
            // 更新预览
            DelayedUpdatePreview();
        }

        private void ButtonOK_Click(object sender, EventArgs e)
        {
            if (sourceImages.Count == 0)
            {
                MessageBox.Show("请添加至少一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                DialogResult = DialogResult.None;
                return;
            }
            OutputFilePath = GetFileNameWithoutExtension();

            // 根据当前预览索引决定返回哪个拼图
            if (currentPreviewIndex >= 0 && currentPreviewIndex < mosaicImageSets.Count)
            {
                // 使用当前选中的图片子集创建高质量拼图
                List<ImageWithPath> imageWithPathSubset = mosaicImageSets[currentPreviewIndex];

                // 使用图片子集创建高质量拼图（非预览模式）
                ResultImage = CreateMosaicImageForSubset(imageWithPathSubset, false, false);
            }
            else
            {
                // 如果没有选中缩略图，则创建完整的拼图
                ResultImage = CreateMosaicImage(false, false);
            }
            DialogResult = DialogResult.OK;
        }

        //获取不带扩展名的文件全路径
        private string GetFileNameWithoutExtension()
        {
            // 获取实际参与拼图的最后一张图片的索引
            int lastImageIndex;

            // 根据当前预览索引决定使用哪组图片的最后一个文件名
            if (currentPreviewIndex >= 0 && currentPreviewIndex < mosaicImageSets.Count)
            {
                // 当前选中了拼图预览，使用该图片子集的最后一个图片索引
                List<ImageWithPath> imageWithPathSubset = mosaicImageSets[currentPreviewIndex];

                // 计算该子集在sourceImages中的最后一个索引
                int imagesPerMosaic = rows * columns;
                int startIndex = currentPreviewIndex * imagesPerMosaic;
                int endIndex = Math.Min(startIndex + imagesPerMosaic, sourceImages.Count) - 1;

                // 使用该组的最后一个图片索引
                lastImageIndex = endIndex;
            }
            else
            {
                // 没有选中缩略图，使用原来的逻辑
                lastImageIndex = Math.Min(sourceImages.Count, (int)numRows.Value * (int)numColumns.Value) - 1;
            }

            // 确保索引有效
            string lastPicFile = string.Empty;
            if (lastImageIndex >= 0 && lastImageIndex < lstImages.Items.Count)
            {
                lastPicFile = lstImages.Items[lastImageIndex].Text;
            }
            else
            {
                // 如果没有图片参与拼图，或者索引无效，可以设置一个默认值或空字符串
                lastPicFile = ""; // 或者其他默认值
            }
            string fullPath = Path.GetFullPath(lastPicFile);
            string fileExtension = Path.GetExtension(lastPicFile);
            string pathWithoutExtension = fullPath.Remove(fullPath.Length - fileExtension.Length);
            return pathWithoutExtension + "--Merge";
        }

        private string GetSourceFileDir()
        {
            // 获取实际参与拼图的最后一张图片的索引
            int lastImageIndex = Math.Min(sourceImages.Count, (int)numRows.Value * (int)numColumns.Value) - 1;

            // 确保索引有效
            string lastPicFile = string.Empty;
            if (lastImageIndex >= 0 && lastImageIndex < lstImages.Items.Count)
            {
                lastPicFile = lstImages.Items[lastImageIndex].Text;
            }
            else
            {
                // 如果没有图片参与拼图，或者索引无效，可以设置一个默认值或空字符串
                lastPicFile = ""; // 或者其他默认值
            }
            return Path.GetDirectoryName(lastPicFile);
        }

        // 延迟更新预览，避免频繁调用
        private void DelayedUpdatePreview()
        {
            // 停止之前的定时器
            previewUpdateTimer?.Stop();
            previewUpdateTimer?.Dispose();

            // 创建新的定时器，减少延迟到200ms，提高响应速度
            previewUpdateTimer = new System.Windows.Forms.Timer();
            previewUpdateTimer.Interval = 10;
            previewUpdateTimer.Tick += (s, e) =>
            {
                previewUpdateTimer.Stop();
                previewUpdateTimer.Dispose();
                previewUpdateTimer = null;
                UpdatePreview();
            };
            previewUpdateTimer.Start();
        }

        // 移除thumbnailsNeedRegeneration变量，不再需要
        private int lastImageCount = 0;
        private int lastRows = 0;
        private int lastColumns = 0;

        private void UpdatePreview()
        {
            // 释放之前的预览图像
            if (previewImage != null)
            {
                previewImage.Dispose();
                previewImage = null;
            }

            // 如果没有图片或预览控件为空，则返回
            if (sourceImages.Count == 0 || picPreview == null)
            {
                if (picPreview != null)
                {
                    picPreview.Image = null;
                }
                return;
            }

            // 如果位置需要重新计算，需要重新生成拼图预览
            // 注：已移除thumbnailsNeedRegeneration变量

            // 释放旧的预览图像资源
            if (previewImage != null)
            {
                Image oldImage = previewImage;
                previewImage = null;
                try
                {
                    oldImage.Dispose();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"释放旧预览图像时出错: {ex.Message}");
                }
            }

            // 创建拼接图像（预览模式）
            try
            {
                previewImage = CreateMosaicImage(true, false);
                if (previewImage == null) return;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建预览图像时出错: {ex.Message}");
                lblStatus.Text = "创建预览图像时出错";
                return;
            }

            // 预览比例显示已移除

            // 检查是否需要重新生成拼图预览
            bool needRegeneratePreview = sourceImages.Count != lastImageCount ||
                                      rows != lastRows ||
                                      columns != lastColumns ||
                                      positionsNeedRecalculation ||
                                      mosaicImageSets.Count == 0; // 确保在没有拼图集合时重新生成

            // 如果源图片列表为空，确保清空拼图预览
            if (sourceImages.Count == 0)
            {
                ClearResources();
            }
            // 只有在需要时才重新生成拼图预览，减少不必要的刷新
            else if (needRegeneratePreview)
            {
                // 准备拼图预览
                PrepareMosaicPreview();

                // 更新计数
                lastImageCount = sourceImages.Count;
                lastRows = rows;
                lastColumns = columns;

                // 确保在重新生成预览后，如果有拼图，高亮显示第一个子集中的文件并更新预览显示
                if (mosaicImageSets.Count > 0)
                {
                    currentPreviewIndex = 0;
                    HighlightCurrentSubsetFiles(0);
                    // 确保TrackBar启用
                    trackBarMosaicPreview.Enabled = true;
                    // 更新预览信息文本
                    lblMosaicPreviewInfo.Text = $"拼图 1/{mosaicImageSets.Count}";
                }
                else
                {
                    // 确保TrackBar禁用
                    trackBarMosaicPreview.Enabled = false;
                    // 更新预览信息文本
                    lblMosaicPreviewInfo.Text = "无拼图预览";
                }
            }

            // 更新预览显示
            try
            {
                // 确保拼图预览已经完全准备好
                if (needRegeneratePreview)
                {
                    // 如果拼图预览正在准备中，先显示完整预览
                    picPreview.Image = previewImage;
                    Debug.WriteLine("拼图预览正在准备中，暂时显示完整预览");

                    // 如果有拼图，更新预览显示
                    if (mosaicImageSets.Count > 0)
                    {
                        UpdatePreviewDisplay();
                    }
                }
                // 如果有选中的拼图预览，显示选中的拼图
                else if (currentPreviewIndex >= 0 && currentPreviewIndex < mosaicImageSets.Count)
                {
                    // 调用UpdatePreviewDisplay方法更新预览显示
                    UpdatePreviewDisplay();
                    Debug.WriteLine($"显示选中的拼图预览 {currentPreviewIndex}");
                }
                // 否则显示完整预览
                else
                {
                    // 显示完整预览，等待用户选择
                    picPreview.Image = previewImage;
                    Debug.WriteLine("没有选中的拼图预览，显示完整预览");
                }

            }
            catch (Exception ex)
            {
                // 发生异常，使用完整预览
                picPreview.Image = previewImage;
                Debug.WriteLine($"显示缩略图时出错: {ex.Message}");
            }

            // 检查预览图像是否有效，避免访问无效图像属性导致异常
            if (previewImage == null)
            {
                Debug.WriteLine("预览图像为空，跳过缩放计算");
                return;
            }

            float scaleX, scaleY, minScale;
            try
            {
                // 计算适合预览窗口的初始缩放因子
                scaleX = (float)picPreview.ClientSize.Width / previewImage.Width;
                scaleY = (float)picPreview.ClientSize.Height / previewImage.Height;

                // 选择较小的缩放因子，确保整个图像都能显示在预览窗口中
                // 如果图像能完全适合预览窗口，使用1.0的缩放因子，不进行缩小
                minScale = Math.Min(scaleX, scaleY);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"计算缩放因子时出错: {ex.Message}");
                return;
            }
            if (minScale >= 1.0f)
            {
                zoomFactor = 1.0f; // 图像适合预览窗口，不缩小
            }
            else
            {
                zoomFactor = minScale * 0.98f; // 图像过大，需要缩小并留出少量边距
            }

            // 确保缩放因子在合理范围内
            if (zoomFactor < 0.1f) zoomFactor = 0.1f;
            if (zoomFactor > 5.0f) zoomFactor = 5.0f;

            // 只在首次加载或图像尺寸发生重大变化时重置平移状态
            // 注释掉自动重置，保持用户的拖拽状态
            // imageOffset = new Point(0, 0);

            // 使用新的预览显示方法更新预览
            UpdatePreviewDisplay();
        }

        // 鼠标滚轮事件处理 - 已禁用缩放功能
        private void PicPreview_MouseWheel(object sender, MouseEventArgs e)
        {
            // 已禁用缩放功能，此方法不执行任何操作
            // 保留方法签名以防其他地方有引用
            return;
        }

        // 鼠标按下事件处理 - 已禁用拖动功能
        private void PicPreview_MouseDown(object sender, MouseEventArgs e)
        {
            // 已禁用拖动功能，此方法不执行任何操作
            // 保留方法签名以防其他地方有引用
            return;
        }

        // 鼠标移动事件处理 - 已禁用拖动功能
        private void PicPreview_MouseMove(object sender, MouseEventArgs e)
        {
            // 已禁用拖动功能，此方法不执行任何操作
            // 保留方法签名以防其他地方有引用
            return;
        }

        // 鼠标释放事件处理 - 已禁用拖动功能
        private void PicPreview_MouseUp(object sender, MouseEventArgs e)
        {
            // 已禁用拖动功能，此方法不执行任何操作
            // 保留方法签名以防其他地方有引用
            return;
        }

        // 更新预览显示 - 仅应用平移，不再支持缩放
        private void UpdatePreviewDisplay()
        {
            if (picPreview == null) return;

            // 获取预览图像
            Image sourceImage = null;
            bool needToDisposeSourceImage = false;

            // 强制进行垃圾回收，帮助释放未使用的资源
            GC.Collect();
            GC.WaitForPendingFinalizers();

            try
            {
                // 确定使用哪个图像作为源
                if (currentPreviewIndex >= 0 && currentPreviewIndex < mosaicImageSets.Count)
                {
                    // 使用当前选中的图片子集创建拼图
                    List<ImageWithPath> imageWithPathSubset = mosaicImageSets[currentPreviewIndex];
                    sourceImage = CreateMosaicImageForSubset(imageWithPathSubset, true, true);
                    needToDisposeSourceImage = true; // 标记需要释放这个新创建的图像
                }
                else
                {
                    // 使用完整预览图像
                    sourceImage = previewImage;
                    needToDisposeSourceImage = false; // 不释放previewImage，它会在其他地方管理
                }

                // 如果没有有效的源图像，直接返回
                if (sourceImage == null) return;

                // 验证图像是否有效
                try
                {
                    // 尝试访问图像属性以验证图像是否有效
                    var width = sourceImage.Width;
                    var height = sourceImage.Height;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"图像无效: {ex.Message}");
                    return; // 图像无效，直接返回
                }

                // 固定缩放因子为1.0，禁用缩放功能
                zoomFactor = 1.0f;

                // 重置图像偏移，禁用平移功能
                imageOffset = new Point(0, 0);

                // 创建一个新的位图，用于绘制居中显示的预览图像（无缩放和平移）
                Bitmap displayBitmap = new Bitmap(picPreview.Width, picPreview.Height);

                using (Graphics g = Graphics.FromImage(displayBitmap))
                {
                    // 设置高质量绘图
                    g.SmoothingMode = SmoothingMode.HighQuality;
                    g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                    g.PixelOffsetMode = PixelOffsetMode.HighQuality;

                    // 清除背景
                    g.Clear(Color.LightGray);

                    // 使用原始图像尺寸（不缩放）
                    int imageWidth = sourceImage.Width;
                    int imageHeight = sourceImage.Height;

                    // 预览比例显示已移除

                    // 计算适合预览窗口的缩放因子
                    float scaleX = (float)picPreview.Width / imageWidth;
                    float scaleY = (float)picPreview.Height / imageHeight;
                    float scale = Math.Min(scaleX, scaleY) * 0.95f; // 使用95%的空间，留出边距

                    // 计算缩放后的尺寸
                    int scaledWidth = (int)(imageWidth * scale);
                    int scaledHeight = (int)(imageHeight * scale);

                    // 计算图像在控件中的位置（居中显示）
                    int x = (picPreview.Width - scaledWidth) / 2;
                    int y = (picPreview.Height - scaledHeight) / 2;

                    // 绘制缩放后居中显示的图像
                    g.DrawImage(sourceImage, new Rectangle(x, y, scaledWidth, scaledHeight));

                    // 绘制画布边界指示框
                    using (Pen borderPen = new Pen(Color.Red, 2))
                    {
                        borderPen.DashStyle = DashStyle.Dash;
                        g.DrawRectangle(borderPen, x, y, scaledWidth, scaledHeight);
                    }
                }

                // 更新预览控件的图像
                if (picPreview.Image != null)
                {
                    try
                    {
                        Image oldImage = picPreview.Image;
                        picPreview.Image = null; // 先清空引用
                        oldImage.Dispose(); // 再释放资源
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"释放picPreview.Image时出错: {ex.Message}");
                    }
                }
                picPreview.Image = displayBitmap;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"UpdatePreviewDisplay出错: {ex.Message}");
                // 移除对displayBitmap的引用，因为如果异常发生在displayBitmap创建之前，这个变量将不可用
            }
            finally
            {
                // 如果是我们创建的临时图像，需要释放
                if (needToDisposeSourceImage && sourceImage != null && sourceImage != previewImage)
                {
                    try
                    {
                        sourceImage.Dispose();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"释放sourceImage时出错: {ex.Message}");
                    }
                }

                // 强制进行垃圾回收，帮助释放未使用的资源
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
        }

        // LstImages_SelectedIndexChanged方法和AdjustCanvasSizeForSelectedImages方法已移除
        // 不再根据选中的图片自动调整画布尺寸

        private Bitmap CreateMosaicImage(bool isPreview = false, bool addWatermark = false)
        {
            // 计算图片位置
            List<Rectangle> imageRectangles = GetImagePositions();
            if (imageRectangles.Count == 0) return null;

            // 获取画布尺寸（已包含边距）
            int currentCanvasWidth = (int)numCanvasWidth.Value;
            int currentCanvasHeight = (int)numCanvasHeight.Value;

            // 总尺寸就是画布尺寸（已包含边距）
            int totalWidth = currentCanvasWidth;
            int totalHeight = currentCanvasHeight;

            // 如果是预览模式，限制图像尺寸以提升性能
            float previewScale = 1.0f;
            List<Rectangle> scaledImageRectangles = new List<Rectangle>();

            if (isPreview)
            {
                // 将预览缩放比例固定为0.9
                previewScale = 0.9f;

                // 始终应用缩放，无论是放大还是缩小
                totalWidth = (int)(totalWidth * previewScale);
                totalHeight = (int)(totalHeight * previewScale);

                // 预先计算缩放后的图片位置，避免在绘制时重复计算
                foreach (Rectangle rect in imageRectangles)
                {
                    Rectangle scaledRect = new Rectangle(
                        (int)Math.Round(rect.X * previewScale),
                        (int)Math.Round(rect.Y * previewScale),
                        (int)Math.Round(rect.Width * previewScale),
                        (int)Math.Round(rect.Height * previewScale));
                    scaledImageRectangles.Add(scaledRect);
                }
            }
            else
            {
                // 非预览模式，直接使用原始位置（已包含边距）
                scaledImageRectangles.AddRange(imageRectangles);
            }

            Bitmap mosaicImage = new Bitmap(totalWidth, totalHeight);
            using (Graphics g = Graphics.FromImage(mosaicImage))
            {
                // 根据是否为预览模式和缩略图生成设置不同的质量
                if (isPreview)
                {
                    // 预览模式使用中等质量，平衡性能和显示效果
                    g.SmoothingMode = SmoothingMode.HighSpeed;
                    g.InterpolationMode = InterpolationMode.Bilinear;
                    g.PixelOffsetMode = PixelOffsetMode.Half;
                    g.CompositingQuality = CompositingQuality.HighSpeed;
                }
                else
                {
                    // 非预览模式使用高质量设置
                    g.SmoothingMode = SmoothingMode.HighQuality;
                    g.InterpolationMode = InterpolationMode.HighQualityBicubic;
                    g.PixelOffsetMode = PixelOffsetMode.HighQuality;
                    g.CompositingQuality = CompositingQuality.HighQuality;
                }

                if (backgroundImage != null)
                {
                    // 根据选择的背景图片处理方式进行绘制
                    if (rdbStretch.Checked)
                    {
                        // 铺满画布模式 - 拉伸图片填满整个画布
                        g.DrawImage(backgroundImage, 0, 0, totalWidth, totalHeight);
                    }
                    else if (rdbAutoSize.Checked)
                    {
                        // 自动大小居中模式 - 保持原比例并居中显示
                        g.Clear(backgroundColor); // 先清除背景

                        // 计算保持原比例的缩放尺寸
                        Rectangle fitRect = CalculateAspectRatioFit(
                            backgroundImage.Width, backgroundImage.Height,
                            totalWidth, totalHeight,
                            0, 0);

                        // 居中显示背景图片
                        int x = (totalWidth - fitRect.Width) / 2;
                        int y = (totalHeight - fitRect.Height) / 2;
                        g.DrawImage(backgroundImage, x, y, fitRect.Width, fitRect.Height);
                    }
                }
                else
                {
                    // 没有背景图片，填充背景色
                    g.Clear(backgroundColor);
                }


                // 绘制每张图片，保持原比例
                // 处理所有行列位置，如果图片不足则用空白填充
                int totalPositions = rows * columns;
                // 确保不会访问超出scaledImageRectangles范围的索引
                int positionsToProcess = Math.Min(totalPositions, scaledImageRectangles.Count);
                for (int i = 0; i < positionsToProcess; i++)
                {
                    Rectangle destRect = scaledImageRectangles[i];

                    // 检查是否有对应的源图片
                    if (i < sourceImages.Count && sourceImages[i] != null)
                    {
                        // 有源图片，确保图片已加载
                        ImageWithPath imgInfo = sourceImages[i];
                        imgInfo.LoadImage();

                        // 获取图片对象
                        Image sourceImage = imgInfo.Image;

                        try
                        {
                            // 创建图像的副本以避免多线程访问冲突
                            using (Bitmap tempImage = new Bitmap(sourceImage))
                            {
                                // 计算保持原比例的缩放尺寸
                                Rectangle fitRect = CalculateAspectRatioFit(
                                    tempImage.Width, tempImage.Height,
                                    destRect.Width, destRect.Height,
                                    destRect.X, destRect.Y);

                                // 绘制图片，保持原比例
                                g.DrawImage(tempImage, fitRect);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"绘制图片时出错: {ex.Message}");
                            // 绘制错误占位符
                            using (Brush errorBrush = new SolidBrush(Color.LightPink))
                            using (Pen errorPen = new Pen(Color.Red, 1))
                            {
                                g.FillRectangle(errorBrush, destRect);
                                g.DrawRectangle(errorPen, destRect);
                            }
                        }
                    }
                    else
                    {
                        // 没有源图片，绘制空白区域（可选：添加边框或标记）
                        // 使用浅灰色填充空白区域
                        using (Brush emptyBrush = new SolidBrush(Color.FromArgb(240, 240, 240)))
                        {
                            g.FillRectangle(emptyBrush, destRect);
                        }

                        // 可选：添加虚线边框标识空白区域
                        using (Pen emptyPen = new Pen(Color.LightGray, 1))
                        {
                            emptyPen.DashStyle = DashStyle.Dash;
                            g.DrawRectangle(emptyPen, destRect);
                        }
                    }
                }

                // 如果需要添加水印且未注册，则添加水印
                if (addWatermark && !PictureMagic.Common.Tools.IsRegistered)
                {
                    // 水印文字
                    string[] watermarkText = new string[] {
                        "PictureMagic",
                        "未注册版本生成低画质图像",
                        "<EMAIL>"
                    };

                    // 创建不透明的画笔，使水印在低质量图片中也能清晰显示
                    using (Font watermarkFont = new Font("Arial", 24, FontStyle.Bold))
                    using (SolidBrush brush = new SolidBrush(Color.FromArgb(255, 255, 0, 0)))
                    {
                        // 计算水印位置（居中）
                        StringFormat format = new StringFormat();
                        format.Alignment = StringAlignment.Center;
                        format.LineAlignment = StringAlignment.Center;

                        // 计算三行文字的总高度
                        float lineHeight = watermarkFont.GetHeight(g) * 1.2f;
                        float watermarkTotalHeight = lineHeight * watermarkText.Length;

                        // 计算起始Y坐标（居中）
                        float startY = (watermarkTotalHeight / 2) * -1 + (watermarkTotalHeight / 2);

                        // 绘制三行水印文字（带白色边框以增强可见性）
                        for (int i = 0; i < watermarkText.Length; i++)
                        {
                            float y = (watermarkTotalHeight / 2) * -1 + (i * lineHeight) + (watermarkTotalHeight / 2);
                            PointF textPosition = new PointF(totalWidth / 2, totalHeight / 2 + y - startY);

                            // 先绘制白色边框（通过在不同位置绘制白色文字实现）
                            using (SolidBrush whiteBrush = new SolidBrush(Color.White))
                            {
                                // 绘制四周的白色文字作为边框
                                for (int offsetX = -2; offsetX <= 2; offsetX += 2)
                                {
                                    for (int offsetY = -2; offsetY <= 2; offsetY += 2)
                                    {
                                        if (offsetX != 0 || offsetY != 0) // 跳过中心点
                                        {
                                            g.DrawString(watermarkText[i], watermarkFont, whiteBrush,
                                                new PointF(textPosition.X + offsetX, textPosition.Y + offsetY), format);
                                        }
                                    }
                                }
                            }

                            // 然后绘制红色文字
                            g.DrawString(watermarkText[i], watermarkFont, brush, textPosition, format);
                        }
                    }
                }
            }

            return mosaicImage;
        }

        // 计算保持原比例的缩放尺寸
        private Rectangle CalculateAspectRatioFit(int srcWidth, int srcHeight, int maxWidth, int maxHeight, int offsetX, int offsetY)
        {
            // 计算源图片的宽高比
            double ratio = Math.Min((double)maxWidth / srcWidth, (double)maxHeight / srcHeight);

            // 计算缩放后的宽度和高度
            int destWidth = (int)(srcWidth * ratio);
            int destHeight = (int)(srcHeight * ratio);

            // 计算在单元格内居中的位置
            int x = offsetX + (maxWidth - destWidth) / 2;
            int y = offsetY + (maxHeight - destHeight) / 2;

            return new Rectangle(x, y, destWidth, destHeight);
        }

        // 获取图片位置（带缓存）
        private List<Rectangle> GetImagePositions()
        {
            if (positionsNeedRecalculation || cachedPositions == null)
            {
                cachedPositions = CalculateImagePositions();
                positionsNeedRecalculation = false;
            }
            return cachedPositions;
        }

        private List<Rectangle> CalculateImagePositions()
        {
            List<Rectangle> positions = new List<Rectangle>();
            if (sourceImages.Count == 0) return positions;

            // 计算行数和列数 - 使用完整的行列数，不根据图片数量限制
            // 这样即使图片数量不足，也能创建完整的拼图布局
            int effectiveRows = rows;
            int effectiveColumns = columns;
            // 注意：我们不再根据图片数量限制行列数，确保创建完整的拼图布局

            // 计算照片的最大宽度和高度
            int maxWidth = 0;
            int maxHeight = 0;
            foreach (ImageWithPath imgWithPath in sourceImages)
            {
                if (imgWithPath.Image != null)
                {
                    maxWidth = Math.Max(maxWidth, imgWithPath.Image.Width);
                    maxHeight = Math.Max(maxHeight, imgWithPath.Image.Height);
                }
            }

            // 获取原始照片尺寸 A和B
            int A = maxWidth;
            int B = maxHeight;
            // 获取行列数 X和Y
            int X = effectiveColumns;
            int Y = effectiveRows;
            // 获取拼图比例 C和D
            float ratio = outputRatio;
            float C, D;
            if (ratio >= 1.0f) // 竖屏比例
            {
                C = 1.0f;
                D = ratio;
            }
            else // 横屏比例
            {
                C = 1.0f / ratio;
                D = 1.0f;
            }

            int contentWidth, contentHeight; // 内容区域尺寸（不包含边距）
            int canvasWidth, canvasHeight;    // 总画布尺寸（包含边距）
            int cellWidth, cellHeight;

            // 根据图中算法判断使用哪种计算方式
            float leftValue = (A * X) / (float)(B * Y);
            float rightValue = C / D;

            if (allowAutoCalculation)
            {
                if (leftValue < rightValue)
                {
                    // 情况1：设置垂直间距V=0
                    verticalSpacing = 0;
                    isUpdatingFromCode = true;
                    numVerticalSpacing.Value = 0;
                    isUpdatingFromCode = false;

                    // 固定垂直尺寸 N = B * Y (N是内容区域高度)
                    int N = B * Y;
                    contentHeight = N;

                    // 计算水平尺寸 M = N * C / D (M是内容区域宽度)
                    int M = (int)(N * C / D);
                    contentWidth = M;

                    // 计算水平间距 H = (M - A*X) / (X-1)
                    if (X > 1) // 避免除以零
                    {
                        horizontalSpacing = (M - A * X) / (X - 1);
                        horizontalSpacing = Math.Max(0, horizontalSpacing); // 确保间距不为负数
                        isUpdatingFromCode = true;
                        numHorizontalSpacing.Value = Math.Max(0, Math.Min(horizontalSpacing, (int)numHorizontalSpacing.Maximum));
                        isUpdatingFromCode = false;
                    }
                    else
                    {
                        horizontalSpacing = 0;
                        isUpdatingFromCode = true;
                        numHorizontalSpacing.Value = 0;
                        isUpdatingFromCode = false;
                    }
                }
                else
                {
                    // 情况2：设置水平间距H=0
                    horizontalSpacing = 0;
                    isUpdatingFromCode = true;
                    numHorizontalSpacing.Value = 0;
                    isUpdatingFromCode = false;

                    // 固定水平尺寸 M = A * X (M是内容区域宽度)
                    int M = A * X;
                    contentWidth = M;

                    // 计算垂直尺寸 N = M * D / C (N是内容区域高度)
                    int N = (int)(M * D / C);
                    contentHeight = N;

                    // 计算垂直间距 V = (N - B*Y) / (Y-1)
                    if (Y > 1) // 避免除以零
                    {
                        verticalSpacing = (N - B * Y) / (Y - 1);
                        verticalSpacing = Math.Max(0, verticalSpacing); // 确保间距不为负数
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = Math.Max(0, Math.Min(verticalSpacing, (int)numVerticalSpacing.Maximum));
                        isUpdatingFromCode = false;
                    }
                    else
                    {
                        verticalSpacing = 0;
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = 0;
                        isUpdatingFromCode = false;
                    }
                }
            }
            else
            {
                // 手动模式：使用当前的间距值计算内容区域尺寸
                int calculatedWidth = A * X + (X - 1) * horizontalSpacing;
                int calculatedHeight = B * Y + (Y - 1) * verticalSpacing;

                if (maintainAspectRatio)
                {
                    // 保持输出比例：根据输出比例调整尺寸
                    float currentRatio = (float)calculatedHeight / calculatedWidth;

                    if (Math.Abs(currentRatio - outputRatio) > 0.01f) // 如果比例差异较大
                    {
                        // 保持宽度，调整高度以匹配比例
                        contentWidth = calculatedWidth;
                        contentHeight = (int)(calculatedWidth * outputRatio);

                        // 重新计算垂直间距以适应新的高度
                        if (Y > 1)
                        {
                            verticalSpacing = (contentHeight - B * Y) / (Y - 1);
                            verticalSpacing = Math.Max(0, verticalSpacing);
                            isUpdatingFromCode = true;
                            numVerticalSpacing.Value = Math.Max(0, Math.Min(verticalSpacing, (int)numVerticalSpacing.Maximum));
                            isUpdatingFromCode = false;
                        }
                    }
                    else
                    {
                        contentWidth = calculatedWidth;
                        contentHeight = calculatedHeight;
                    }
                }
                else
                {
                    // 不保持比例：直接使用计算出的尺寸
                    contentWidth = calculatedWidth;
                    contentHeight = calculatedHeight;
                }
            }

            // 计算总画布尺寸（内容区域 + 边距）
            canvasWidth = contentWidth + marginLeft + marginRight;
            canvasHeight = contentHeight + marginTop + marginBottom;

            // 如果维持宽高比，需要调整画布尺寸以确保最终比例正确
            if (maintainAspectRatio)
            {
                // 根据画布宽度和输出比例计算正确的画布高度
                int targetCanvasHeight = (int)(canvasWidth * outputRatio);

                // 如果计算出的高度与当前高度不同，调整画布高度
                if (Math.Abs(targetCanvasHeight - canvasHeight) > 1)
                {
                    canvasHeight = targetCanvasHeight;
                    // 重新计算内容区域高度
                    contentHeight = canvasHeight - marginTop - marginBottom;

                    // 确保内容区域高度足够容纳所有图像
                    int minContentHeight = B * Y + (Y - 1) * Math.Max(0, verticalSpacing);
                    if (contentHeight < minContentHeight)
                    {
                        contentHeight = minContentHeight;
                        canvasHeight = contentHeight + marginTop + marginBottom;
                        // 重新计算画布宽度以保持比例
                        canvasWidth = (int)(canvasHeight / outputRatio);
                        // 重新计算内容区域宽度
                        contentWidth = canvasWidth - marginLeft - marginRight;
                    }

                    // 重新计算垂直间距以适应新的内容高度
                    if (Y > 1)
                    {
                        verticalSpacing = (contentHeight - B * Y) / (Y - 1);
                        verticalSpacing = Math.Max(0, verticalSpacing);
                        isUpdatingFromCode = true;
                        numVerticalSpacing.Value = Math.Max(0, Math.Min(verticalSpacing, (int)numVerticalSpacing.Maximum));
                        isUpdatingFromCode = false;
                    }

                    // 重新计算水平间距以适应新的内容宽度
                    if (X > 1)
                    {
                        horizontalSpacing = (contentWidth - A * X) / (X - 1);
                        horizontalSpacing = Math.Max(0, horizontalSpacing);
                        isUpdatingFromCode = true;
                        numHorizontalSpacing.Value = Math.Max(0, Math.Min(horizontalSpacing, (int)numHorizontalSpacing.Maximum));
                        isUpdatingFromCode = false;
                    }
                }
            }

            // 确保画布尺寸在允许的范围内
            canvasWidth = Math.Max(canvasWidth, (int)numCanvasWidth.Minimum);
            canvasWidth = Math.Min(canvasWidth, (int)numCanvasWidth.Maximum);
            canvasHeight = Math.Max(canvasHeight, (int)numCanvasHeight.Minimum);
            canvasHeight = Math.Min(canvasHeight, (int)numCanvasHeight.Maximum);

            // 更新画布尺寸控件值（显示总尺寸）
            isUpdatingFromCode = true;
            numCanvasWidth.Value = canvasWidth;
            numCanvasHeight.Value = canvasHeight;
            isUpdatingFromCode = false;

            // 计算单元格尺寸
            cellWidth = A;
            cellHeight = B;

            // 禁用画布尺寸控件，使其不可编辑
            numCanvasWidth.Enabled = false;
            numCanvasHeight.Enabled = false;

            // 计算起始位置（考虑边距和居中对齐）
            int totalWidthNeeded = X * cellWidth + (X - 1) * horizontalSpacing;
            int totalHeightNeeded = Y * cellHeight + (Y - 1) * verticalSpacing;

            // 确保内容不超过内容区域
            totalWidthNeeded = Math.Min(totalWidthNeeded, contentWidth);
            totalHeightNeeded = Math.Min(totalHeightNeeded, contentHeight);

            // 在内容区域内居中对齐
            int contentStartX = (contentWidth - totalWidthNeeded) / 2;
            int contentStartY = (contentHeight - totalHeightNeeded) / 2;

            // 加上边距得到在总画布上的起始位置
            int startX = marginLeft + Math.Max(0, contentStartX);
            int startY = marginTop + Math.Max(0, contentStartY);

            // 计算所有行列位置，不仅仅是有图片的位置
            // 这样即使图片数量不足，也能保持画布大小一致
            for (int i = 0; i < X * Y; i++)
            {
                int row = i / X;
                int col = i % X;

                int x = startX + col * (cellWidth + horizontalSpacing);
                int y = startY + row * (cellHeight + verticalSpacing);

                positions.Add(new Rectangle(x, y, cellWidth, cellHeight));
            }

            // 同时更新imagePositions成员变量，以便其他方法使用
            imagePositions.Clear();
            imagePositions.AddRange(positions);

            return positions;
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // 停止并释放定时器
            previewUpdateTimer?.Stop();
            previewUpdateTimer?.Dispose();
            previewUpdateTimer = null;

            base.OnFormClosing(e);

            // 释放预览图像
            if (previewImage != null)
            {
                previewImage.Dispose();
                previewImage = null;
            }

            // 清理缓存
            cachedPositions?.Clear();
            cachedPositions = null;

            // 无论对话框结果如何，都释放所有源图像
            // 如果是确定按钮，ResultImage已经被创建并返回给调用者
            // 源图像不再需要保留
            foreach (ImageWithPath imgInfo in sourceImages)
            {
                imgInfo.UnloadImage();
            }
            sourceImages.Clear();

            // 清理图片子集集合
            mosaicImageSets.Clear();
        }

        private void ImageMosaicDialog_Load(object sender, EventArgs e)
        {
            cmbOutputRatio.SelectedIndex = 3;


        }

        private void btnSelectBackgroundPicture_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.RestoreDirectory = true;
                // 从设置中加载上次的路径
                var settings = AppSettings.LoadSettings();
                if (!string.IsNullOrEmpty(settings.LastBackgroundPicturePath))
                {
                    openFileDialog.InitialDirectory = settings.LastBackgroundPicturePath;
                }
                openFileDialog.Filter = "图像文件|*.jpg;*.jpeg;*.png;*.bmp;*.gif";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    backgroundImage = Tools.LoadImageSafe(openFileDialog.FileName);//Image.FromFile(openFileDialog.FileName);
                    // 标记位置需要重新计算
                    positionsNeedRecalculation = true;
                    // 更新预览
                    UpdatePreview();

                    // 更新上次选择的路径并保存
                    settings.LastBackgroundPicturePath = Path.GetDirectoryName(openFileDialog.FileName);
                    AppSettings.SaveSettings(settings);
                }
            }
        }

        private void btnClearBackgroundPicture_Click(object sender, EventArgs e)
        {
            backgroundImage = null;
            // 标记位置需要重新计算
            positionsNeedRecalculation = true;
            // 更新预览
            UpdatePreview();
        }

        private void RadioButton_CheckedChanged(object sender, EventArgs e)
        {
            // 当背景图片处理方式改变时更新预览
            if (backgroundImage != null)
            {
                // 标记位置需要重新计算，确保在切换背景模式时重新计算所有位置
                positionsNeedRecalculation = true;
                UpdatePreview();
            }
        }

        // 边距控件事件处理方法
        private void NumMarginLeft_ValueChanged(object sender, EventArgs e)
        {
            marginLeft = (int)numMarginLeft.Value;

            // 边距变化时，标记位置需要重新计算
            positionsNeedRecalculation = true;

            // 如果启用自动计算，重新计算画布尺寸和间距
            if (allowAutoCalculation && sourceImages.Count > 0)
            {
                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;
            }

            DelayedUpdatePreview();
        }

        private void NumMarginRight_ValueChanged(object sender, EventArgs e)
        {
            marginRight = (int)numMarginRight.Value;

            // 边距变化时，标记位置需要重新计算
            positionsNeedRecalculation = true;

            // 如果启用自动计算，重新计算画布尺寸和间距
            if (allowAutoCalculation && sourceImages.Count > 0)
            {
                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;
            }

            DelayedUpdatePreview();
        }

        private void NumMarginTop_ValueChanged(object sender, EventArgs e)
        {
            marginTop = (int)numMarginTop.Value;

            // 边距变化时，标记位置需要重新计算
            positionsNeedRecalculation = true;

            // 如果启用自动计算，重新计算画布尺寸和间距
            if (allowAutoCalculation && sourceImages.Count > 0)
            {
                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;
            }

            DelayedUpdatePreview();
        }

        private void ImageMosaicDialog_FormClosing(object sender, FormClosingEventArgs e)
        {

            ClearResources();
        }

        private void NumMarginBottom_ValueChanged(object sender, EventArgs e)
        {
            marginBottom = (int)numMarginBottom.Value;

            // 边距变化时，标记位置需要重新计算
            positionsNeedRecalculation = true;

            // 如果启用自动计算，重新计算画布尺寸和间距
            if (allowAutoCalculation && sourceImages.Count > 0)
            {
                // 启用画布尺寸控件，以便重新计算
                numCanvasWidth.Enabled = true;
                numCanvasHeight.Enabled = true;
            }

            DelayedUpdatePreview();
        }
        private void ButtonSaveCurrent_Click(object sender, EventArgs e)
        {
            if (sourceImages.Count == 0)
            {
                MessageBox.Show("请添加至少一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 获取输出文件路径（不带扩展名）
            string outputPath = GetFileNameWithoutExtension();

            // 获取当前预览窗口中显示的拼图
            Image mosaicImage = null;

            // 根据当前预览索引决定保存哪个拼图
            if (currentPreviewIndex >= 0 && currentPreviewIndex < mosaicImageSets.Count)
            {
                // 使用当前选中的图片子集创建高质量拼图
                List<ImageWithPath> imageWithPathSubset = mosaicImageSets[currentPreviewIndex];

                // 使用图片子集创建高质量拼图（非预览模式，添加水印）
                mosaicImage = CreateMosaicImageForSubset(imageWithPathSubset, false, true);
            }
            else
            {
                // 如果没有选中缩略图，则创建完整的拼图（添加水印）
                mosaicImage = CreateMosaicImage(false, true);
            }

            if (mosaicImage != null)
            {
                try
                {
                    // 使用保存选项对话框
                    using (SaveOptionsDialog saveOptionsDialog = new SaveOptionsDialog(mosaicImage, outputPath, false))
                    {
                        if (saveOptionsDialog.ShowDialog() == DialogResult.OK)
                        {
                            try
                            {
                                // 获取保存选项
                                string filePath = saveOptionsDialog.SelectedFilePath;
                                ImageFormat format = saveOptionsDialog.SelectedFormat;
                                EncoderParameters encoderParams = saveOptionsDialog.EncoderParams;

                                // 如果是JPEG格式且有编码参数，使用编码参数保存
                                if (format.Equals(ImageFormat.Jpeg) && encoderParams != null)
                                {
                                    ImageCodecInfo jpegCodec = GetEncoderInfo("image/jpeg");
                                    if (jpegCodec != null)
                                    {
                                        mosaicImage.Save(filePath, jpegCodec, encoderParams);
                                    }
                                    else
                                    {
                                        // 如果找不到JPEG编码器，使用默认方式保存
                                        mosaicImage.Save(filePath, format);
                                    }
                                }
                                else
                                {
                                    // 其他格式或没有编码参数，使用默认方式保存
                                    mosaicImage.Save(filePath, format);
                                }

                                //MessageBox.Show($"图像已保存至【{filePath}】", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                StandardCustomMessageBox.Show($"图像已保存至【{filePath}】", "操作成功", MessageBoxButtons.OK, MessageBoxIcon.Information, "定位到文件", new StandardCustomMessageBox.CustomButtonClickEventHandler(LocateFile), filePath);
                            }
                            catch (Exception ex)
                            {
                                MessageBox.Show($"保存图像出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                            }
                        }
                    }
                }
                finally
                {
                    // 释放临时创建的图像资源
                    if (mosaicImage != null && mosaicImage != ResultImage)
                    {
                        mosaicImage.Dispose();
                    }
                }
            }
        }

        private static void LocateFile(object sender, CustomButtonClickEventArgs e)
        {
            string filePath = e.UserData as string;
            if (File.Exists(filePath))
            {
                Process.Start("explorer.exe", $"/select,{filePath}");
            }
            if (Directory.Exists(filePath))
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = filePath,
                    UseShellExecute = true // 必须加，才能打开资源管理器
                });
            }
        }


        private void ButtonSaveBatch_Click(object sender, EventArgs e)
        {
            if (sourceImages.Count == 0)
            {
                MessageBox.Show("请添加至少一张图片", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 计算每张拼图需要的图片数量
            int imagesPerMosaic = rows * columns;

            // 计算需要生成的拼图数量
            int totalMosaics = (sourceImages.Count / imagesPerMosaic) + (sourceImages.Count % imagesPerMosaic > 0 ? 1 : 0);

            if (totalMosaics <= 0)
            {
                MessageBox.Show("无法生成拼图，请检查行列设置", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // 获取基础输出文件路径（不带扩展名）
            string baseOutputPath = GetSourceFileDir();
            //string outputDir = Path.GetDirectoryName(baseOutputPath);
            //string fileNameWithoutExt = Path.GetFileNameWithoutExtension(baseOutputPath);
            //string extension = Path.GetExtension(baseOutputPath);

            // 使用保存选项对话框获取保存格式
            // 先创建一个临时图像用于显示保存选项对话框
            Image tempImage = null;
            try
            {
                // 创建进度窗口，提前显示进度
                ProgressForm progressForm = null;
                SaveBatchOptionsDialog saveOptionsDialog = null;
                DialogResult dialogResult = DialogResult.Cancel;

                try
                {
                    // 初始化并显示进度窗口
                    progressForm = new ProgressForm(true); // 使用遮罩效果
                    progressForm.SetMarqueeStyle();
                    progressForm.Text = "准备批量保存";
                    progressForm.UpdateMessage("正在创建预览图像...");
                    progressForm.Show(this); // 指定父窗体

                    // 创建临时图像
                    tempImage = CreateMosaicImage(false, false);
                    if (tempImage == null)
                    {
                        MessageBox.Show("无法创建预览图像，请检查图片设置", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 更新进度信息
                    progressForm.UpdateMessage("正在准备保存选项...");

                    // 创建保存选项对话框但不立即显示，在进度窗口仍然显示的情况下初始化
                    saveOptionsDialog = new SaveBatchOptionsDialog(tempImage, baseOutputPath, false);

                    // 在保存选项对话框完全准备好后关闭进度窗口
                    progressForm.CloseProgress();
                    progressForm = null;

                    // 显示保存选项对话框并获取结果
                    dialogResult = saveOptionsDialog.ShowDialog();
                }
                finally
                {
                    // 确保进度窗口被关闭
                    if (progressForm != null)
                    {
                        progressForm.CloseProgress();
                        progressForm = null;
                    }
                }

                // 处理对话框结果
                if (saveOptionsDialog != null)
                {
                    try
                    {
                        if (dialogResult == DialogResult.OK)
                        {
                            // 获取保存选项
                            string selectedFilePath = saveOptionsDialog.SelectedDirPath;
                            ImageFormat format = saveOptionsDialog.SelectedFormat;
                            EncoderParameters encoderParams = saveOptionsDialog.EncoderParams;

                            // 更新输出目录和扩展名
                            string outputDir = selectedFilePath;
                            string extension = saveOptionsDialog.FileExtension;
                            // 生成一个批次时间戳，确保同一批次的文件使用相同的时间戳
                            // string batchTimestamp = DateTime.Now.ToString("yyyyMMdd--HHmmssfff");

                            // 使用进度条显示保存进度
                            using (ProgressForm saveProgressForm = new ProgressForm(true)) // 使用遮罩效果
                            {
                                // 设置进度条最大值和标题
                                saveProgressForm.SetContinuousStyle(0, totalMosaics);
                                saveProgressForm.Text = "保存多张拼图";
                                saveProgressForm.UpdateMessage("准备保存拼图...");
                                saveProgressForm.Show(this); // 指定父窗体

                                int successCount = 0;
                                List<string> savedFiles = new List<string>();
                                ImageCodecInfo jpegCodec = null;

                                // 如果是JPEG格式，提前获取编码器信息
                                if (format.Equals(ImageFormat.Jpeg) && encoderParams != null)
                                {
                                    jpegCodec = GetEncoderInfo("image/jpeg");
                                }

                                // 生成并保存每张拼图
                                for (int mosaicIndex = 0; mosaicIndex < totalMosaics; mosaicIndex++)
                                {
                                    // 更新进度条
                                    saveProgressForm.UpdateProgress(mosaicIndex + 1);
                                    saveProgressForm.UpdateMessage($"正在保存第 {mosaicIndex + 1}/{totalMosaics} 张拼图...");

                                    // 计算当前拼图使用的图片范围
                                    int startIndex = mosaicIndex * imagesPerMosaic;
                                    int endIndex = Math.Min(startIndex + imagesPerMosaic - 1, sourceImages.Count - 1);

                                    // 创建当前拼图使用的图片列表
                                    List<Image> currentImages = new List<Image>();
                                    string lastImagePath = "";
                                    string lastImageName = "";

                                    try
                                    {
                                        // 获取最后一个图片的路径信息（用于文件命名）
                                        if (endIndex >= 0 && endIndex < sourceImages.Count)
                                        {
                                            lastImagePath = sourceImages[endIndex].Tag?.ToString() ??
                                                         (lstImages.Items.Count > endIndex ? lstImages.Items[endIndex].Text : $"image{endIndex}");
                                            lastImageName = Path.GetFileNameWithoutExtension(lastImagePath);
                                        }
                                        else
                                        {
                                            // 如果索引无效，使用默认名称
                                            lastImageName = $"image{mosaicIndex}";
                                        }

                                        // 添加当前范围内的图片
                                        for (int i = startIndex; i <= endIndex; i++)
                                        {
                                            if (i >= 0 && i < sourceImages.Count)
                                            {
                                                // 确保图片已加载
                                                sourceImages[i].LoadImage();
                                                if (sourceImages[i].Image != null)
                                                {
                                                    currentImages.Add(sourceImages[i].Image);
                                                }
                                            }
                                        }

                                        // 如果是最后一张拼图且图片数量不足，确保创建完整的拼图布局
                                        if (mosaicIndex == totalMosaics - 1 && currentImages.Count < imagesPerMosaic)
                                        {
                                            // 计算总单元格数
                                            int totalCells = rows * columns;

                                            // 记录实际图片数量，用于调试
                                            Console.WriteLine($"最后一张拼图：实际图片数量 {currentImages.Count}，总单元格数 {totalCells}");

                                            // 注意：CreateMosaicImageForSubset方法会自动处理空白填充
                                            // 但我们需要确保它知道应该创建多少个单元格
                                        }

                                        // 创建当前拼图
                                        Image currentMosaic = null;
                                        try
                                        {
                                            // 如果是最后一张拼图且图片数量不足，确保传递完整的行列信息
                                            if (mosaicIndex == totalMosaics - 1 && currentImages.Count < imagesPerMosaic)
                                            {
                                                Console.WriteLine($"创建最后一张拼图：实际图片数量 {currentImages.Count}，总单元格数 {imagesPerMosaic}");
                                            }

                                            // 在保存到文件时添加水印
                                            // 创建临时的 ImageWithPath 列表
                                            List<ImageWithPath> imageWithPathList = new List<ImageWithPath>();
                                            foreach (Image img in currentImages)
                                            {
                                                string filePath = img.Tag?.ToString() ?? "";
                                                imageWithPathList.Add(new ImageWithPath(img, filePath));
                                            }
                                            currentMosaic = CreateMosaicImageForSubset(imageWithPathList, false, true);

                                            if (currentMosaic != null)
                                            {
                                                // 生成当前拼图的文件名，保持原有命名方式不变
                                                string currentFileName = $"{lastImageName}--Merge--{DateTime.Now.ToString("yyyyMMdd--HHmmssfff")}--{mosaicIndex + 1}{extension}";
                                                string currentFilePath = Path.Combine(outputDir, currentFileName);

                                                // 保存当前拼图
                                                if (format.Equals(ImageFormat.Jpeg) && encoderParams != null && jpegCodec != null)
                                                {
                                                    currentMosaic.Save(currentFilePath, jpegCodec, encoderParams);
                                                }
                                                else
                                                {
                                                    currentMosaic.Save(currentFilePath, format);
                                                }

                                                savedFiles.Add(currentFilePath);
                                                successCount++;
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            MessageBox.Show($"保存第 {mosaicIndex + 1} 张拼图出错: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                                        }
                                        finally
                                        {
                                            // 释放当前拼图资源
                                            if (currentMosaic != null)
                                            {
                                                currentMosaic.Dispose();
                                                currentMosaic = null;
                                            }
                                        }
                                    }
                                    finally
                                    {
                                        // 清空临时图片列表（不释放图片，因为它们是引用）
                                        currentImages.Clear();
                                    }

                                    // 每处理几张图片后强制进行垃圾回收
                                    if (mosaicIndex % 5 == 0)
                                    {
                                        GC.Collect();
                                        GC.WaitForPendingFinalizers();
                                    }

                                    // 检查是否取消操作
                                    if (saveProgressForm.IsCancelled())
                                    {
                                        break;
                                    }
                                }

                                // 显示保存结果
                                if (successCount > 0)
                                {
                                    //MessageBox.Show($"成功保存 {successCount}/{totalMosaics} 张拼图至【{outputDir}】", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    StandardCustomMessageBox.Show($"成功保存 {successCount}/{totalMosaics} 张拼图至【{outputDir}】", "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information, "定位文件夹", new StandardCustomMessageBox.CustomButtonClickEventHandler(LocateFile), outputDir);
                                }
                                else
                                {
                                    MessageBox.Show("未能成功保存任何拼图", "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                                }

                                // 最后一次强制垃圾回收
                                GC.Collect();
                                GC.WaitForPendingFinalizers();
                            }
                        }
                    }
                    finally
                    {
                        // 释放保存选项对话框资源
                        if (saveOptionsDialog != null)
                        {
                            saveOptionsDialog.Dispose();
                            saveOptionsDialog = null;
                        }
                    }
                }
            }
            finally
            {
                // 释放临时图像资源
                if (tempImage != null)
                {
                    tempImage.Dispose();
                    tempImage = null;
                }
            }
        }


        // 为指定的图片子集创建拼图
        // 重载方法，接收 ImageWithPath 对象列表
        private Image CreateMosaicImageForSubset(List<ImageWithPath> imageWithPathSubset, bool isPreview = false, bool addWatermark = false)
        {
            // 保存原始图片列表的引用
            List<ImageWithPath> originalImages = sourceImages;
            // 保存原始位置计算标记状态
            bool originalPositionsNeedRecalculation = positionsNeedRecalculation;
            // 保存原始行列设置
            int originalRows = rows;
            int originalColumns = columns;
            Image result = null;

            // 创建一个新的图片子集列表，避免修改原始列表
            List<ImageWithPath> workingSubset = new List<ImageWithPath>(imageWithPathSubset.Count);
            foreach (ImageWithPath img in imageWithPathSubset)
            {
                workingSubset.Add(img);
            }

            try
            {
                // 临时替换sourceImages引用
                sourceImages = workingSubset;

                // 按需加载图片
                foreach (ImageWithPath imgInfo in workingSubset)
                {
                    try
                    {
                        // 只在需要时加载图片
                        imgInfo.LoadImage();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"加载图片时出错: {ex.Message}");
                    }
                }

                // 标记需要重新计算位置，因为图片列表已经改变
                positionsNeedRecalculation = true;

                // 确保即使图片数量不足，也会创建完整的拼图布局
                // 关键是确保CalculateImagePositions方法计算所有行列位置
                int totalCells = rows * columns;
                Console.WriteLine($"CreateMosaicImageForSubset: 图片数量={imageWithPathSubset.Count}, 行数={rows}, 列数={columns}, 总单元格数={totalCells}");

                // 使用现有方法创建拼图，传递水印参数
                result = CreateMosaicImage(isPreview, addWatermark);
                return result;
            }
            catch (Exception ex)
            {
                // 记录错误
                Console.WriteLine($"创建拼图子集时出错: {ex.Message}");
                // 如果出现异常，确保释放已创建的资源
                if (result != null)
                {
                    result.Dispose();
                    result = null;
                }
                throw; // 重新抛出异常以便上层处理
            }
            finally
            {
                // 恢复原始图片列表引用
                sourceImages = originalImages;

                // 恢复原始位置计算标记状态
                positionsNeedRecalculation = originalPositionsNeedRecalculation;

                // 恢复原始行列设置
                rows = originalRows;
                columns = originalColumns;

                // 使用完后立即释放图片资源
                foreach (ImageWithPath imgInfo in workingSubset)
                {
                    try
                    {
                        // 释放图片资源，但保留元数据
                        imgInfo.UnloadImage();
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"释放图片资源时出错: {ex.Message}");
                    }
                }

                // 强制进行垃圾回收，帮助释放未使用的资源
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
        }

        // 获取指定MIME类型的编码器信息
        private ImageCodecInfo GetEncoderInfo(string mimeType)
        {
            ImageCodecInfo[] codecs = ImageCodecInfo.GetImageEncoders();
            foreach (ImageCodecInfo codec in codecs)
            {
                if (codec.MimeType == mimeType)
                {
                    return codec;
                }
            }
            return null;
        }

        private void lstImages_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Control && e.KeyCode == Keys.A)
            {
                foreach (ListViewItem item in lstImages.Items)
                {
                    item.Selected = true;
                }
            }
        }

        /// <summary>
        /// 在 lstImages 中高亮显示当前子集中的文件
        /// </summary>
        /// <param name="subsetIndex">当前子集的索引</param>
        private void HighlightCurrentSubsetFiles(int subsetIndex)
        {
            if (subsetIndex < 0 || subsetIndex >= mosaicImageSets.Count)
                return;

            // 获取当前子集中的文件路径
            List<ImageWithPath> currentSubset = mosaicImageSets[subsetIndex];
            HashSet<string> currentFilePaths = new HashSet<string>();

            foreach (ImageWithPath imgWithPath in currentSubset)
            {
                if (!string.IsNullOrEmpty(imgWithPath.FilePath))
                {
                    currentFilePaths.Add(Path.GetFullPath(imgWithPath.FilePath));
                }
            }

            // 使用 SuspendLayout 和 BeginUpdate 防止闪烁
            lstImages.SuspendLayout();
            lstImages.BeginUpdate();

            try
            {
                // 记录需要高亮的项目和需要恢复的项目
                Dictionary<ListViewItem, bool> itemsToUpdate = new Dictionary<ListViewItem, bool>();
                ListViewItem firstHighlightedItem = null;

                // 先检查哪些项目需要更改，避免不必要的重绘
                foreach (ListViewItem item in lstImages.Items)
                {
                    string filePath = item.Text;
                    bool shouldHighlight = currentFilePaths.Contains(filePath);

                    // 只有当当前状态与目标状态不同时才更新
                    bool isCurrentlyHighlighted = item.BackColor == Color.LightYellow;
                    if (shouldHighlight != isCurrentlyHighlighted)
                    {
                        itemsToUpdate[item] = shouldHighlight;
                    }

                    // 记录第一个需要高亮的项目
                    if (shouldHighlight && firstHighlightedItem == null)
                    {
                        firstHighlightedItem = item;
                    }

                    // 清除选择状态
                    if (item.Selected)
                    {
                        item.Selected = false;
                    }
                }

                // 批量应用更改
                foreach (var kvp in itemsToUpdate)
                {
                    kvp.Key.BackColor = kvp.Value ? Color.LightYellow : SystemColors.Window;
                }

                // 确保第一个高亮的项目在可视范围内
                if (firstHighlightedItem != null)
                {
                    firstHighlightedItem.EnsureVisible();
                }
            }
            finally
            {
                // 完成更新后恢复布局和绘制
                lstImages.EndUpdate();
                lstImages.ResumeLayout();
            }
        }

        /// <summary>
        /// 处理拼图预览控制面板的键盘事件
        /// </summary>
        private void trackBarMosaicPreview_KeyDown(object sender, KeyEventArgs e)
        {
            if (mosaicImageSets.Count == 0)
                return;

            int currentValue = trackBarMosaicPreview.Value;
            int newValue = currentValue;

            // 根据按键调整值
            if (e.KeyCode == Keys.Left || e.KeyCode == Keys.Up)
            {
                // 向左或向上移动选择上一个拼图
                newValue = Math.Max(trackBarMosaicPreview.Minimum, currentValue - 1);
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.Right || e.KeyCode == Keys.Down)
            {
                // 向右或向下移动选择下一个拼图
                newValue = Math.Min(trackBarMosaicPreview.Maximum, currentValue + 1);
                e.Handled = true;
            }

            // 如果值发生变化，更新TrackBar和预览
            if (newValue != currentValue)
            {
                trackBarMosaicPreview.Value = newValue;

                // 更新当前预览索引并刷新预览显示
                currentPreviewIndex = newValue;
                if (newValue < mosaicImageSets.Count)
                {
                    // 更新预览信息标签
                    lblMosaicPreviewInfo.Text = $"拼图 {newValue + 1}/{mosaicImageSets.Count}";

                    // 在 lstImages 中高亮显示当前子集中的文件
                    HighlightCurrentSubsetFiles(newValue);

                    UpdatePreviewDisplay();
                }
            }
        }


    }
}
