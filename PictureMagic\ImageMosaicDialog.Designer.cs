namespace PictureMagic
{
    partial class ImageMosaicDialog
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;


        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(ImageMosaicDialog));
            splitContainer1 = new SplitContainer();
            groupBox3 = new GroupBox();
            tableLayoutPanel1 = new TableLayoutPanel();
            numMarginBottom = new NumericUpDown();
            numMarginTop = new NumericUpDown();
            numMarginRight = new NumericUpDown();
            numMarginLeft = new NumericUpDown();
            label1 = new Label();
            label2 = new Label();
            label3 = new Label();
            label9 = new Label();
            label10 = new Label();
            label5 = new Label();
            label6 = new Label();
            label8 = new Label();
            numColumns = new NumericUpDown();
            numRows = new NumericUpDown();
            numHorizontalSpacing = new NumericUpDown();
            numVerticalSpacing = new NumericUpDown();
            cmbOutputRatio = new ComboBox();
            numCanvasWidth = new NumericUpDown();
            numCanvasHeight = new NumericUpDown();
            btnBackgroundColor = new Button();
            chkKeepAspectRatio = new CheckBox();
            label4 = new Label();
            label7 = new Label();
            label11 = new Label();
            label12 = new Label();
            label13 = new Label();
            panel3 = new Panel();
            btnClearBackgroundPicture = new Button();
            btnSelectBackgroundPicture = new Button();
            label14 = new Label();
            panel4 = new Panel();
            rdbAutoSize = new RadioButton();
            rdbStretch = new RadioButton();
            groupBox1 = new GroupBox();
            lstImages = new ListView();
            pictureFilePath = new ColumnHeader();
            panel1 = new Panel();
            btnRemoveImage = new Button();
            btnAddImage = new Button();
            btnClearAllImages = new Button();
            lblImageCount = new Label();
            panelPreview = new Panel();
            groupBox2 = new GroupBox();
            picPreview = new PictureBox();
            panelPreviewControl = new Panel();
            groupBoxPreviewControl = new GroupBox();
            trackBarMosaicPreview = new TrackBar();
            lblMosaicPreviewInfo = new Label();
            panel2 = new Panel();
            btnCancel = new Button();
            btnOK = new Button();
            btnSaveCurrent = new Button();
            btnSaveBatch = new Button();
            lblStatus = new Label();
            ((System.ComponentModel.ISupportInitialize)splitContainer1).BeginInit();
            splitContainer1.Panel1.SuspendLayout();
            splitContainer1.Panel2.SuspendLayout();
            splitContainer1.SuspendLayout();
            groupBox3.SuspendLayout();
            tableLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)numMarginBottom).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numMarginTop).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numMarginRight).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numMarginLeft).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numColumns).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numRows).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numHorizontalSpacing).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numVerticalSpacing).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numCanvasWidth).BeginInit();
            ((System.ComponentModel.ISupportInitialize)numCanvasHeight).BeginInit();
            panel3.SuspendLayout();
            panel4.SuspendLayout();
            groupBox1.SuspendLayout();
            panel1.SuspendLayout();
            panelPreview.SuspendLayout();
            groupBox2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)picPreview).BeginInit();
            panelPreviewControl.SuspendLayout();
            groupBoxPreviewControl.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarMosaicPreview).BeginInit();
            panel2.SuspendLayout();
            SuspendLayout();
            // 
            // splitContainer1
            // 
            splitContainer1.Dock = DockStyle.Fill;
            splitContainer1.Location = new Point(0, 0);
            splitContainer1.Margin = new Padding(4);
            splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            splitContainer1.Panel1.Controls.Add(groupBox3);
            splitContainer1.Panel1.Controls.Add(groupBox1);
            splitContainer1.Panel1MinSize = 300;
            // 
            // splitContainer1.Panel2
            // 
            splitContainer1.Panel2.Controls.Add(panelPreview);
            splitContainer1.Panel2.Controls.Add(panelPreviewControl);
            splitContainer1.Panel2.Controls.Add(panel2);
            splitContainer1.Panel2MinSize = 400;
            splitContainer1.Size = new Size(1317, 953);
            splitContainer1.SplitterDistance = 346;
            splitContainer1.SplitterWidth = 5;
            splitContainer1.TabIndex = 0;
            // 
            // groupBox3
            // 
            groupBox3.Controls.Add(tableLayoutPanel1);
            groupBox3.Dock = DockStyle.Fill;
            groupBox3.Location = new Point(0, 333);
            groupBox3.Margin = new Padding(4);
            groupBox3.Name = "groupBox3";
            groupBox3.Padding = new Padding(4);
            groupBox3.Size = new Size(346, 620);
            groupBox3.TabIndex = 1;
            groupBox3.TabStop = false;
            groupBox3.Text = "拼接设置";
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 2;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 43.7869835F));
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 56.2130165F));
            tableLayoutPanel1.Controls.Add(numMarginBottom, 1, 11);
            tableLayoutPanel1.Controls.Add(numMarginTop, 1, 10);
            tableLayoutPanel1.Controls.Add(numMarginRight, 1, 9);
            tableLayoutPanel1.Controls.Add(numMarginLeft, 1, 8);
            tableLayoutPanel1.Controls.Add(label1, 0, 0);
            tableLayoutPanel1.Controls.Add(label2, 0, 1);
            tableLayoutPanel1.Controls.Add(label3, 0, 2);
            tableLayoutPanel1.Controls.Add(label9, 0, 3);
            tableLayoutPanel1.Controls.Add(label10, 0, 4);
            tableLayoutPanel1.Controls.Add(label5, 0, 5);
            tableLayoutPanel1.Controls.Add(label6, 0, 6);
            tableLayoutPanel1.Controls.Add(label8, 0, 7);
            tableLayoutPanel1.Controls.Add(numColumns, 1, 0);
            tableLayoutPanel1.Controls.Add(numRows, 1, 1);
            tableLayoutPanel1.Controls.Add(numHorizontalSpacing, 1, 2);
            tableLayoutPanel1.Controls.Add(numVerticalSpacing, 1, 3);
            tableLayoutPanel1.Controls.Add(cmbOutputRatio, 1, 4);
            tableLayoutPanel1.Controls.Add(numCanvasWidth, 1, 5);
            tableLayoutPanel1.Controls.Add(numCanvasHeight, 1, 6);
            tableLayoutPanel1.Controls.Add(btnBackgroundColor, 1, 7);
            tableLayoutPanel1.Controls.Add(chkKeepAspectRatio, 1, 14);
            tableLayoutPanel1.Controls.Add(label4, 0, 8);
            tableLayoutPanel1.Controls.Add(label7, 0, 9);
            tableLayoutPanel1.Controls.Add(label11, 0, 10);
            tableLayoutPanel1.Controls.Add(label12, 0, 11);
            tableLayoutPanel1.Controls.Add(label13, 0, 12);
            tableLayoutPanel1.Controls.Add(panel3, 1, 12);
            tableLayoutPanel1.Controls.Add(label14, 0, 13);
            tableLayoutPanel1.Controls.Add(panel4, 1, 13);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(4, 24);
            tableLayoutPanel1.Margin = new Padding(4);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 16;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 36F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 33F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 34F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 36F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 33F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 37F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 33F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 35F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 39F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 37F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 36F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 30F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 36F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 21F));
            tableLayoutPanel1.Size = new Size(338, 592);
            tableLayoutPanel1.TabIndex = 0;
            // 
            // numMarginBottom
            // 
            numMarginBottom.Anchor = AnchorStyles.Left;
            numMarginBottom.Location = new Point(152, 395);
            numMarginBottom.Margin = new Padding(4);
            numMarginBottom.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numMarginBottom.Name = "numMarginBottom";
            numMarginBottom.Size = new Size(154, 27);
            numMarginBottom.TabIndex = 28;
            numMarginBottom.ValueChanged += NumMarginBottom_ValueChanged;
            // 
            // numMarginTop
            // 
            numMarginTop.Anchor = AnchorStyles.Left;
            numMarginTop.Location = new Point(152, 357);
            numMarginTop.Margin = new Padding(4);
            numMarginTop.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numMarginTop.Name = "numMarginTop";
            numMarginTop.Size = new Size(154, 27);
            numMarginTop.TabIndex = 27;
            numMarginTop.ValueChanged += NumMarginTop_ValueChanged;
            // 
            // numMarginRight
            // 
            numMarginRight.Anchor = AnchorStyles.Left;
            numMarginRight.Location = new Point(152, 320);
            numMarginRight.Margin = new Padding(4);
            numMarginRight.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numMarginRight.Name = "numMarginRight";
            numMarginRight.Size = new Size(154, 27);
            numMarginRight.TabIndex = 24;
            numMarginRight.ValueChanged += NumMarginRight_ValueChanged;
            // 
            // numMarginLeft
            // 
            numMarginLeft.Anchor = AnchorStyles.Left;
            numMarginLeft.Location = new Point(152, 287);
            numMarginLeft.Margin = new Padding(4);
            numMarginLeft.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numMarginLeft.Name = "numMarginLeft";
            numMarginLeft.Size = new Size(154, 27);
            numMarginLeft.TabIndex = 23;
            numMarginLeft.ValueChanged += NumMarginLeft_ValueChanged;
            // 
            // label1
            // 
            label1.Anchor = AnchorStyles.Left;
            label1.AutoSize = true;
            label1.Location = new Point(4, 8);
            label1.Margin = new Padding(4, 0, 4, 0);
            label1.Name = "label1";
            label1.Size = new Size(39, 20);
            label1.TabIndex = 0;
            label1.Text = "列数";
            // 
            // label2
            // 
            label2.Anchor = AnchorStyles.Left;
            label2.AutoSize = true;
            label2.Location = new Point(4, 42);
            label2.Margin = new Padding(4, 0, 4, 0);
            label2.Name = "label2";
            label2.Size = new Size(39, 20);
            label2.TabIndex = 1;
            label2.Text = "行数";
            // 
            // label3
            // 
            label3.Anchor = AnchorStyles.Left;
            label3.AutoSize = true;
            label3.Location = new Point(4, 76);
            label3.Margin = new Padding(4, 0, 4, 0);
            label3.Name = "label3";
            label3.Size = new Size(129, 20);
            label3.TabIndex = 2;
            label3.Text = "相邻图片水平间距";
            // 
            // label9
            // 
            label9.Anchor = AnchorStyles.Left;
            label9.AutoSize = true;
            label9.Location = new Point(4, 111);
            label9.Margin = new Padding(4, 0, 4, 0);
            label9.Name = "label9";
            label9.Size = new Size(129, 20);
            label9.TabIndex = 16;
            label9.Text = "相邻图片垂直间距";
            // 
            // label10
            // 
            label10.Anchor = AnchorStyles.Left;
            label10.AutoSize = true;
            label10.Location = new Point(4, 145);
            label10.Margin = new Padding(4, 0, 4, 0);
            label10.Name = "label10";
            label10.Size = new Size(69, 20);
            label10.TabIndex = 18;
            label10.Text = "输出比例";
            // 
            // label5
            // 
            label5.Anchor = AnchorStyles.Left;
            label5.AutoSize = true;
            label5.Location = new Point(4, 181);
            label5.Margin = new Padding(4, 0, 4, 0);
            label5.Name = "label5";
            label5.Size = new Size(69, 20);
            label5.TabIndex = 4;
            label5.Text = "画布宽度";
            // 
            // label6
            // 
            label6.Anchor = AnchorStyles.Left;
            label6.AutoSize = true;
            label6.Location = new Point(4, 219);
            label6.Margin = new Padding(4, 0, 4, 0);
            label6.Name = "label6";
            label6.Size = new Size(69, 20);
            label6.TabIndex = 5;
            label6.Text = "画布高度";
            // 
            // label8
            // 
            label8.Anchor = AnchorStyles.Left;
            label8.AutoSize = true;
            label8.Location = new Point(4, 255);
            label8.Margin = new Padding(4, 0, 4, 0);
            label8.Name = "label8";
            label8.Size = new Size(69, 20);
            label8.TabIndex = 7;
            label8.Text = "背景颜色";
            // 
            // numColumns
            // 
            numColumns.Anchor = AnchorStyles.Left;
            numColumns.Location = new Point(152, 4);
            numColumns.Margin = new Padding(4);
            numColumns.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numColumns.Name = "numColumns";
            numColumns.Size = new Size(154, 27);
            numColumns.TabIndex = 9;
            numColumns.Value = new decimal(new int[] { 2, 0, 0, 0 });
            numColumns.ValueChanged += NumericColumns_ValueChanged;
            // 
            // numRows
            // 
            numRows.Anchor = AnchorStyles.Left;
            numRows.Location = new Point(152, 40);
            numRows.Margin = new Padding(4);
            numRows.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            numRows.Name = "numRows";
            numRows.Size = new Size(154, 27);
            numRows.TabIndex = 8;
            numRows.Value = new decimal(new int[] { 1, 0, 0, 0 });
            numRows.ValueChanged += NumericRows_ValueChanged;
            // 
            // numHorizontalSpacing
            // 
            numHorizontalSpacing.Anchor = AnchorStyles.Left;
            numHorizontalSpacing.Location = new Point(152, 73);
            numHorizontalSpacing.Margin = new Padding(4);
            numHorizontalSpacing.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numHorizontalSpacing.Name = "numHorizontalSpacing";
            numHorizontalSpacing.Size = new Size(154, 27);
            numHorizontalSpacing.TabIndex = 10;
            numHorizontalSpacing.Value = new decimal(new int[] { 10, 0, 0, 0 });
            numHorizontalSpacing.ValueChanged += NumericHorizontalSpacing_ValueChanged;
            // 
            // numVerticalSpacing
            // 
            numVerticalSpacing.Anchor = AnchorStyles.Left;
            numVerticalSpacing.Location = new Point(152, 107);
            numVerticalSpacing.Margin = new Padding(4);
            numVerticalSpacing.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numVerticalSpacing.Name = "numVerticalSpacing";
            numVerticalSpacing.Size = new Size(154, 27);
            numVerticalSpacing.TabIndex = 17;
            numVerticalSpacing.ValueChanged += NumericVerticalSpacing_ValueChanged;
            // 
            // cmbOutputRatio
            // 
            cmbOutputRatio.Anchor = AnchorStyles.Left;
            cmbOutputRatio.DropDownStyle = ComboBoxStyle.DropDownList;
            cmbOutputRatio.FormattingEnabled = true;
            cmbOutputRatio.Items.AddRange(new object[] { "1:1 (正方形)", "4:3 (标准)", "3:2 (照片)", "16:9 (宽屏)", "2:1 (全景)", "3:4 (竖屏标准)", "2:3 (竖屏照片)", "9:16 (竖屏宽屏)", "1:2 (竖屏全景)" });
            cmbOutputRatio.Location = new Point(152, 143);
            cmbOutputRatio.Margin = new Padding(4);
            cmbOutputRatio.Name = "cmbOutputRatio";
            cmbOutputRatio.Size = new Size(154, 28);
            cmbOutputRatio.TabIndex = 19;
            cmbOutputRatio.SelectedIndexChanged += CmbOutputRatio_SelectedIndexChanged;
            // 
            // numCanvasWidth
            // 
            numCanvasWidth.Anchor = AnchorStyles.Left;
            numCanvasWidth.Location = new Point(152, 178);
            numCanvasWidth.Margin = new Padding(4);
            numCanvasWidth.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numCanvasWidth.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            numCanvasWidth.Name = "numCanvasWidth";
            numCanvasWidth.Size = new Size(154, 27);
            numCanvasWidth.TabIndex = 12;
            numCanvasWidth.Value = new decimal(new int[] { 800, 0, 0, 0 });
            numCanvasWidth.ValueChanged += NumCanvasWidth_ValueChanged;
            // 
            // numCanvasHeight
            // 
            numCanvasHeight.Anchor = AnchorStyles.Left;
            numCanvasHeight.Location = new Point(152, 216);
            numCanvasHeight.Margin = new Padding(4);
            numCanvasHeight.Maximum = new decimal(new int[] { int.MaxValue, 0, 0, 0 });
            numCanvasHeight.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            numCanvasHeight.Name = "numCanvasHeight";
            numCanvasHeight.Size = new Size(154, 27);
            numCanvasHeight.TabIndex = 13;
            numCanvasHeight.Value = new decimal(new int[] { 600, 0, 0, 0 });
            numCanvasHeight.ValueChanged += NumCanvasHeight_ValueChanged;
            // 
            // btnBackgroundColor
            // 
            btnBackgroundColor.Anchor = AnchorStyles.Left;
            btnBackgroundColor.BackColor = Color.White;
            btnBackgroundColor.Location = new Point(152, 252);
            btnBackgroundColor.Margin = new Padding(4);
            btnBackgroundColor.Name = "btnBackgroundColor";
            btnBackgroundColor.Size = new Size(154, 27);
            btnBackgroundColor.TabIndex = 16;
            btnBackgroundColor.UseVisualStyleBackColor = false;
            btnBackgroundColor.Click += ButtonBackgroundColor_Click;
            // 
            // chkKeepAspectRatio
            // 
            chkKeepAspectRatio.Anchor = AnchorStyles.Left;
            chkKeepAspectRatio.AutoSize = true;
            chkKeepAspectRatio.Checked = true;
            chkKeepAspectRatio.CheckState = CheckState.Checked;
            chkKeepAspectRatio.Location = new Point(152, 499);
            chkKeepAspectRatio.Margin = new Padding(4);
            chkKeepAspectRatio.Name = "chkKeepAspectRatio";
            chkKeepAspectRatio.Size = new Size(106, 24);
            chkKeepAspectRatio.TabIndex = 15;
            chkKeepAspectRatio.Text = "保持宽高比";
            chkKeepAspectRatio.UseVisualStyleBackColor = true;
            chkKeepAspectRatio.CheckedChanged += CheckBoxMaintainAspectRatio_CheckedChanged;
            // 
            // label4
            // 
            label4.Anchor = AnchorStyles.Left;
            label4.AutoSize = true;
            label4.Location = new Point(3, 289);
            label4.Name = "label4";
            label4.Size = new Size(99, 20);
            label4.TabIndex = 21;
            label4.Text = "拼接后左边距";
            // 
            // label7
            // 
            label7.Anchor = AnchorStyles.Left;
            label7.AutoSize = true;
            label7.Location = new Point(3, 323);
            label7.Name = "label7";
            label7.Size = new Size(99, 20);
            label7.TabIndex = 22;
            label7.Text = "拼接后右边距";
            // 
            // label11
            // 
            label11.Anchor = AnchorStyles.Left;
            label11.AutoSize = true;
            label11.Location = new Point(3, 360);
            label11.Name = "label11";
            label11.Size = new Size(99, 20);
            label11.TabIndex = 25;
            label11.Text = "拼接后上边距";
            // 
            // label12
            // 
            label12.Anchor = AnchorStyles.Left;
            label12.AutoSize = true;
            label12.Location = new Point(3, 398);
            label12.Name = "label12";
            label12.Size = new Size(99, 20);
            label12.TabIndex = 26;
            label12.Text = "拼接后下边距";
            // 
            // label13
            // 
            label13.Anchor = AnchorStyles.Left;
            label13.AutoSize = true;
            label13.Location = new Point(3, 435);
            label13.Name = "label13";
            label13.Size = new Size(69, 20);
            label13.TabIndex = 29;
            label13.Text = "背景图片";
            // 
            // panel3
            // 
            panel3.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            panel3.Controls.Add(btnClearBackgroundPicture);
            panel3.Controls.Add(btnSelectBackgroundPicture);
            panel3.Location = new Point(151, 430);
            panel3.Name = "panel3";
            panel3.Size = new Size(184, 30);
            panel3.TabIndex = 30;
            // 
            // btnClearBackgroundPicture
            // 
            btnClearBackgroundPicture.Dock = DockStyle.Left;
            btnClearBackgroundPicture.Location = new Point(94, 0);
            btnClearBackgroundPicture.Name = "btnClearBackgroundPicture";
            btnClearBackgroundPicture.Size = new Size(94, 30);
            btnClearBackgroundPicture.TabIndex = 33;
            btnClearBackgroundPicture.Text = "清除背景";
            btnClearBackgroundPicture.UseVisualStyleBackColor = true;
            btnClearBackgroundPicture.Click += btnClearBackgroundPicture_Click;
            // 
            // btnSelectBackgroundPicture
            // 
            btnSelectBackgroundPicture.Dock = DockStyle.Left;
            btnSelectBackgroundPicture.Location = new Point(0, 0);
            btnSelectBackgroundPicture.Name = "btnSelectBackgroundPicture";
            btnSelectBackgroundPicture.Size = new Size(94, 30);
            btnSelectBackgroundPicture.TabIndex = 32;
            btnSelectBackgroundPicture.Text = "选择背景";
            btnSelectBackgroundPicture.UseVisualStyleBackColor = true;
            btnSelectBackgroundPicture.Click += btnSelectBackgroundPicture_Click;
            // 
            // label14
            // 
            label14.Anchor = AnchorStyles.Left;
            label14.AutoSize = true;
            label14.Location = new Point(3, 468);
            label14.Name = "label14";
            label14.Size = new Size(99, 20);
            label14.TabIndex = 31;
            label14.Text = "背景展示方式";
            // 
            // panel4
            // 
            panel4.AutoScroll = true;
            panel4.AutoSize = true;
            panel4.Controls.Add(rdbAutoSize);
            panel4.Controls.Add(rdbStretch);
            panel4.Location = new Point(151, 466);
            panel4.Name = "panel4";
            panel4.Size = new Size(184, 24);
            panel4.TabIndex = 32;
            // 
            // rdbAutoSize
            // 
            rdbAutoSize.AutoSize = true;
            rdbAutoSize.Dock = DockStyle.Fill;
            rdbAutoSize.Location = new Point(90, 0);
            rdbAutoSize.Name = "rdbAutoSize";
            rdbAutoSize.Size = new Size(94, 24);
            rdbAutoSize.TabIndex = 34;
            rdbAutoSize.Text = "自动大小居中";
            rdbAutoSize.UseVisualStyleBackColor = true;
            // 
            // rdbStretch
            // 
            rdbStretch.AutoSize = true;
            rdbStretch.Checked = true;
            rdbStretch.Dock = DockStyle.Left;
            rdbStretch.Location = new Point(0, 0);
            rdbStretch.Name = "rdbStretch";
            rdbStretch.Size = new Size(90, 24);
            rdbStretch.TabIndex = 33;
            rdbStretch.TabStop = true;
            rdbStretch.Text = "铺满画布";
            rdbStretch.UseVisualStyleBackColor = true;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(lstImages);
            groupBox1.Controls.Add(panel1);
            groupBox1.Controls.Add(lblImageCount);
            groupBox1.Dock = DockStyle.Top;
            groupBox1.Location = new Point(0, 0);
            groupBox1.Margin = new Padding(4);
            groupBox1.Name = "groupBox1";
            groupBox1.Padding = new Padding(4);
            groupBox1.Size = new Size(346, 333);
            groupBox1.TabIndex = 0;
            groupBox1.TabStop = false;
            groupBox1.Text = "图片列表";
            // 
            // lstImages
            // 
            lstImages.Columns.AddRange(new ColumnHeader[] { pictureFilePath });
            lstImages.Dock = DockStyle.Fill;
            lstImages.LabelWrap = false;
            lstImages.Location = new Point(4, 24);
            lstImages.Margin = new Padding(4);
            lstImages.Name = "lstImages";
            lstImages.Size = new Size(338, 252);
            lstImages.TabIndex = 0;
            lstImages.UseCompatibleStateImageBehavior = false;
            lstImages.View = View.Details;
            lstImages.KeyDown += lstImages_KeyDown;
            // 
            // pictureFilePath
            // 
            pictureFilePath.Text = "图片路径";
            pictureFilePath.Width = 200;
            // 
            // panel1
            // 
            panel1.Controls.Add(btnRemoveImage);
            panel1.Controls.Add(btnAddImage);
            panel1.Controls.Add(btnClearAllImages);
            panel1.Dock = DockStyle.Bottom;
            panel1.Location = new Point(4, 276);
            panel1.Margin = new Padding(4);
            panel1.Name = "panel1";
            panel1.Size = new Size(338, 53);
            panel1.TabIndex = 1;
            // 
            // btnRemoveImage
            // 
            btnRemoveImage.Location = new Point(133, 11);
            btnRemoveImage.Margin = new Padding(4);
            btnRemoveImage.Name = "btnRemoveImage";
            btnRemoveImage.Size = new Size(96, 33);
            btnRemoveImage.TabIndex = 1;
            btnRemoveImage.Text = "移除图片";
            btnRemoveImage.UseVisualStyleBackColor = true;
            btnRemoveImage.Click += ButtonRemoveImages_Click;
            // 
            // btnAddImage
            // 
            btnAddImage.Location = new Point(18, 11);
            btnAddImage.Margin = new Padding(4);
            btnAddImage.Name = "btnAddImage";
            btnAddImage.Size = new Size(96, 33);
            btnAddImage.TabIndex = 0;
            btnAddImage.Text = "添加图片";
            btnAddImage.UseVisualStyleBackColor = true;
            btnAddImage.Click += ButtonAddImages_Click;
            // 
            // btnClearAllImages
            // 
            btnClearAllImages.Location = new Point(248, 11);
            btnClearAllImages.Margin = new Padding(4);
            btnClearAllImages.Name = "btnClearAllImages";
            btnClearAllImages.Size = new Size(96, 33);
            btnClearAllImages.TabIndex = 3;
            btnClearAllImages.Text = "清空所有";
            btnClearAllImages.UseVisualStyleBackColor = true;
            btnClearAllImages.Click += ButtonClearAllImages_Click;
            // 
            // lblImageCount
            // 
            lblImageCount.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            lblImageCount.AutoSize = true;
            lblImageCount.ForeColor = SystemColors.HotTrack;
            lblImageCount.Location = new Point(163, 0);
            lblImageCount.Name = "lblImageCount";
            lblImageCount.Size = new Size(82, 20);
            lblImageCount.TabIndex = 2;
            lblImageCount.Text = "图片总数:0";
            // 
            // panelPreview
            // 
            panelPreview.AutoScroll = true;
            panelPreview.Controls.Add(groupBox2);
            panelPreview.Dock = DockStyle.Fill;
            panelPreview.Location = new Point(0, 0);
            panelPreview.Margin = new Padding(4);
            panelPreview.Name = "panelPreview";
            panelPreview.Size = new Size(966, 778);
            panelPreview.TabIndex = 0;
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(picPreview);
            groupBox2.Dock = DockStyle.Fill;
            groupBox2.Location = new Point(0, 0);
            groupBox2.Margin = new Padding(4);
            groupBox2.Name = "groupBox2";
            groupBox2.Padding = new Padding(4);
            groupBox2.Size = new Size(966, 778);
            groupBox2.TabIndex = 0;
            groupBox2.TabStop = false;
            groupBox2.Text = "预览";
            // 
            // picPreview
            // 
            picPreview.Dock = DockStyle.Fill;
            picPreview.Location = new Point(4, 24);
            picPreview.Margin = new Padding(4);
            picPreview.Name = "picPreview";
            picPreview.Size = new Size(958, 750);
            picPreview.SizeMode = PictureBoxSizeMode.Zoom;
            picPreview.TabIndex = 0;
            picPreview.TabStop = false;
            picPreview.MouseDown += PicPreview_MouseDown;
            picPreview.MouseMove += PicPreview_MouseMove;
            picPreview.MouseUp += PicPreview_MouseUp;
            // 
            // panelPreviewControl
            // 
            panelPreviewControl.Controls.Add(groupBoxPreviewControl);
            panelPreviewControl.Dock = DockStyle.Bottom;
            panelPreviewControl.Location = new Point(0, 778);
            panelPreviewControl.Name = "panelPreviewControl";
            panelPreviewControl.Size = new Size(966, 122);
            panelPreviewControl.TabIndex = 5;
            // 
            // groupBoxPreviewControl
            // 
            groupBoxPreviewControl.Controls.Add(trackBarMosaicPreview);
            groupBoxPreviewControl.Controls.Add(lblMosaicPreviewInfo);
            groupBoxPreviewControl.Dock = DockStyle.Fill;
            groupBoxPreviewControl.Location = new Point(0, 0);
            groupBoxPreviewControl.Name = "groupBoxPreviewControl";
            groupBoxPreviewControl.Size = new Size(966, 122);
            groupBoxPreviewControl.TabIndex = 0;
            groupBoxPreviewControl.TabStop = false;
            groupBoxPreviewControl.Text = "拼图预览控制";
            // 
            // trackBarMosaicPreview
            // 
            trackBarMosaicPreview.Dock = DockStyle.Bottom;
            trackBarMosaicPreview.LargeChange = 1;
            trackBarMosaicPreview.Location = new Point(3, 63);
            trackBarMosaicPreview.Maximum = 0;
            trackBarMosaicPreview.Name = "trackBarMosaicPreview";
            trackBarMosaicPreview.Size = new Size(960, 56);
            trackBarMosaicPreview.TabIndex = 0;
            trackBarMosaicPreview.Scroll += trackBarMosaicPreview_Scroll;
            trackBarMosaicPreview.KeyDown += trackBarMosaicPreview_KeyDown;
            // 
            // lblMosaicPreviewInfo
            // 
            lblMosaicPreviewInfo.Dock = DockStyle.Top;
            lblMosaicPreviewInfo.Location = new Point(3, 23);
            lblMosaicPreviewInfo.Name = "lblMosaicPreviewInfo";
            lblMosaicPreviewInfo.Size = new Size(960, 30);
            lblMosaicPreviewInfo.TabIndex = 1;
            lblMosaicPreviewInfo.Text = "拼图 0/0";
            lblMosaicPreviewInfo.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // panel2
            // 
            panel2.Controls.Add(btnCancel);
            panel2.Controls.Add(btnOK);
            panel2.Controls.Add(btnSaveCurrent);
            panel2.Controls.Add(btnSaveBatch);
            panel2.Controls.Add(lblStatus);
            panel2.Dock = DockStyle.Bottom;
            panel2.Location = new Point(0, 900);
            panel2.Margin = new Padding(4);
            panel2.Name = "panel2";
            panel2.Size = new Size(966, 53);
            panel2.TabIndex = 1;
            // 
            // btnCancel
            // 
            btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnCancel.DialogResult = DialogResult.Cancel;
            btnCancel.Location = new Point(856, 11);
            btnCancel.Margin = new Padding(4);
            btnCancel.Name = "btnCancel";
            btnCancel.Size = new Size(96, 33);
            btnCancel.TabIndex = 1;
            btnCancel.Text = "取消";
            btnCancel.UseVisualStyleBackColor = true;
            // 
            // btnOK
            // 
            btnOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnOK.Location = new Point(751, 11);
            btnOK.Margin = new Padding(4);
            btnOK.Name = "btnOK";
            btnOK.Size = new Size(96, 33);
            btnOK.TabIndex = 0;
            btnOK.Text = "确定";
            btnOK.UseVisualStyleBackColor = true;
            btnOK.Click += ButtonOK_Click;
            // 
            // btnSaveCurrent
            // 
            btnSaveCurrent.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnSaveCurrent.Location = new Point(469, 11);
            btnSaveCurrent.Margin = new Padding(4);
            btnSaveCurrent.Name = "btnSaveCurrent";
            btnSaveCurrent.Size = new Size(120, 33);
            btnSaveCurrent.TabIndex = 2;
            btnSaveCurrent.Text = "保存当前拼图";
            btnSaveCurrent.UseVisualStyleBackColor = true;
            btnSaveCurrent.Click += ButtonSaveCurrent_Click;
            // 
            // btnSaveBatch
            // 
            btnSaveBatch.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            btnSaveBatch.Location = new Point(609, 11);
            btnSaveBatch.Margin = new Padding(4);
            btnSaveBatch.Name = "btnSaveBatch";
            btnSaveBatch.Size = new Size(120, 33);
            btnSaveBatch.TabIndex = 3;
            btnSaveBatch.Text = "保存批量拼图";
            btnSaveBatch.UseVisualStyleBackColor = true;
            btnSaveBatch.Click += ButtonSaveBatch_Click;
            // 
            // lblStatus
            // 
            lblStatus.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            lblStatus.AutoSize = true;
            lblStatus.ForeColor = SystemColors.HotTrack;
            lblStatus.Location = new Point(12, 20);
            lblStatus.Name = "lblStatus";
            lblStatus.Size = new Size(39, 20);
            lblStatus.TabIndex = 4;
            lblStatus.Text = "就绪";
            // 
            // ImageMosaicDialog
            // 
            AutoScaleDimensions = new SizeF(120F, 120F);
            AutoScaleMode = AutoScaleMode.Dpi;
            CancelButton = btnCancel;
            ClientSize = new Size(1317, 953);
            Controls.Add(splitContainer1);
            Icon = (Icon)resources.GetObject("$this.Icon");
            Margin = new Padding(4);
            MinimizeBox = false;
            MinimumSize = new Size(1022, 783);
            Name = "ImageMosaicDialog";
            ShowInTaskbar = false;
            StartPosition = FormStartPosition.CenterParent;
            Text = "图片拼接";
            FormClosing += ImageMosaicDialog_FormClosing;
            Load += ImageMosaicDialog_Load;
            splitContainer1.Panel1.ResumeLayout(false);
            splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)splitContainer1).EndInit();
            splitContainer1.ResumeLayout(false);
            groupBox3.ResumeLayout(false);
            tableLayoutPanel1.ResumeLayout(false);
            tableLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)numMarginBottom).EndInit();
            ((System.ComponentModel.ISupportInitialize)numMarginTop).EndInit();
            ((System.ComponentModel.ISupportInitialize)numMarginRight).EndInit();
            ((System.ComponentModel.ISupportInitialize)numMarginLeft).EndInit();
            ((System.ComponentModel.ISupportInitialize)numColumns).EndInit();
            ((System.ComponentModel.ISupportInitialize)numRows).EndInit();
            ((System.ComponentModel.ISupportInitialize)numHorizontalSpacing).EndInit();
            ((System.ComponentModel.ISupportInitialize)numVerticalSpacing).EndInit();
            ((System.ComponentModel.ISupportInitialize)numCanvasWidth).EndInit();
            ((System.ComponentModel.ISupportInitialize)numCanvasHeight).EndInit();
            panel3.ResumeLayout(false);
            panel4.ResumeLayout(false);
            panel4.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            panel1.ResumeLayout(false);
            panelPreview.ResumeLayout(false);
            groupBox2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)picPreview).EndInit();
            panelPreviewControl.ResumeLayout(false);
            groupBoxPreviewControl.ResumeLayout(false);
            groupBoxPreviewControl.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)trackBarMosaicPreview).EndInit();
            panel2.ResumeLayout(false);
            panel2.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private SplitContainer splitContainer1;
        private GroupBox groupBox1;
        private ListView lstImages;
        private Panel panel1;
        private Button btnRemoveImage;
        private Button btnAddImage;
        private Button btnClearAllImages;
        private Label lblImageCount;
        private Panel panelPreview;
        private Panel panelPreviewControl;
        private GroupBox groupBox2;
        private PictureBox picPreview;
        private Panel panel2;
        private Button btnCancel;
        private Button btnOK;
        private Button btnSaveCurrent;
        private Button btnSaveBatch;
        private Label lblStatus;
        private GroupBox groupBox3;
        private TableLayoutPanel tableLayoutPanel1;
        private Label label1;
        private Label label2;
        private Label label3;
        private Label label9;
        private Label label10;
        private Label label5;
        private Label label6;
        // 移除对齐方式标签
        private Label label8;
        private NumericUpDown numRows;
        private NumericUpDown numColumns;
        private NumericUpDown numHorizontalSpacing;
        private NumericUpDown numVerticalSpacing;
        private ComboBox cmbOutputRatio;
        private NumericUpDown numCanvasWidth;
        private NumericUpDown numCanvasHeight;
        // 移除对齐方式下拉框
        private Button btnBackgroundColor;
        private CheckBox chkKeepAspectRatio;
        private ColumnHeader pictureFilePath;
        private Label label4;
        private Label label7;
        private NumericUpDown numMarginBottom;
        private NumericUpDown numMarginTop;
        private NumericUpDown numMarginRight;
        private NumericUpDown numMarginLeft;
        private Label label11;
        private Label label12;
        private Label label13;
        private Panel panel3;
        private Button btnClearBackgroundPicture;
        private Button btnSelectBackgroundPicture;
        private Label label14;
        private Panel panel4;
        private RadioButton rdbAutoSize;
        private RadioButton rdbStretch;
        private GroupBox groupBoxPreviewControl;
        private TrackBar trackBarMosaicPreview;
        private Label lblMosaicPreviewInfo;
        // imageListThumbnails已移除，不再需要
    }
}